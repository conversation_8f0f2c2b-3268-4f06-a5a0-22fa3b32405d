import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Store } from "@ngrx/store";
import { NotificationsService } from "angular2-notifications";
import { of, switchMap, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { OnError } from "src/app/app.actions";
import { AppState } from "src/app/app.reducer";
import { FilterService } from "src/app/services/filter.service";
import { DeleteFilter, DeleteFilterSuccess, FetchFilter, FetchFilterSuccess, FilterActionTypes, FilterExist, FilterExistSuccess, SaveFilter, SaveFilterSuccess, UpdateSavedFilterSuccess } from "./filter.action";

@Injectable()
export class FilterEffects {
    constructor(private actions$: Actions, private filterService: FilterService,
        private store: Store<AppState>,
        private _notificationService: NotificationsService,
    ) { }

    saveFilter$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FilterActionTypes.SAVE_FILTER),
            map((action: SaveFilter) => action.payload),
            switchMap((data: any) => {
                return this.filterService.saveFilter(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success('Filter saved Successfully');
                            this.store.dispatch(new SaveFilterSuccess(resp));
                            return new FetchFilter();
                        }
                        return new FetchFilterSuccess({});
                    }),
                    catchError((err: any) => {
                        Array.isArray(err?.error?.messages)
                            ? this._notificationService.error(err.error.messages[0])
                            : this._notificationService.error(err?.error?.messages);
                        return throwError(() => err);
                    })
                );
            })
        )
    );

    getFilter$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FilterActionTypes.FETCH_FILTER),
            switchMap(() => {
                const moduleMapping: { [key: string]: string } = {
                    data: 'prospects',
                    listing: 'property',
                    property: 'property',
                    leads: 'leads',
                    projects: 'projects',
                };
                const currentPath = window.location.pathname;
                const matchingKey = Object.keys(moduleMapping).find(key => currentPath.includes(key));
                const module = matchingKey ? moduleMapping[matchingKey] : undefined;

                return this.filterService.getFilter(module).pipe(
                    map((resp: any) => {
                        return new FetchFilterSuccess(resp?.items);
                    }),
                );
            })
        )
    );

    deleteFilter$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FilterActionTypes.DELETE_FILTER),
            map((action: DeleteFilter) => action.payload),
            switchMap((data: any) => {
                return this.filterService.deleteFilter(data).pipe(
                    map((resp: any) => {
                        this._notificationService.success('Filter deleted Successfully');
                        this.store.dispatch(new DeleteFilterSuccess(resp));
                        return new FetchFilter();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    updateSavedFilter$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FilterActionTypes.UPDATE_SAVED_FILTER),
            map((action: any) => action.payload),
            switchMap((data: any) => {
                return this.filterService.updateFilter(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success('Filter updated Successfully');
                            this.store.dispatch(new UpdateSavedFilterSuccess(data));
                            return new FetchFilter();
                        }
                        return new FetchFilterSuccess({});
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    filterExists$ = createEffect(() =>
        this.actions$.pipe(
            ofType(FilterActionTypes.FILTER_EXIST),
            switchMap((action: FilterExist) => {
                return this.filterService.filterExists(action.name, action.module).pipe(
                    map((resp: any) => new FilterExistSuccess(resp.data)),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

}
