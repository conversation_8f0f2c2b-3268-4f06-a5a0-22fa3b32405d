import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { EMPTY, of, throwError } from 'rxjs';
import {
  catchError,
  map,
  mergeMap,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { CloseModal, OnError } from 'src/app/app.actions';
import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { QRConfirmationComponent } from 'src/app/features/no-auth/lead-qr-form/qr-confirmation/qr-confirmation.component';
import {
  AddLead,
  AddLeadProjects,
  AddLeadProjectsSuccess,
  AddLeadRotationGroup,
  AddLeadSuccess,
  AddQRLead,
  AddQRLeadSuccess,
  BulkAgency,
  BulkAgencySuccess,
  BulkCampaign,
  BulkCampaignSuccess,
  BulkChannelPartner,
  BulkChannelPartnerSuccess,
  BulkProjects,
  BulkProjectsSuccess,
  BulkReassignLead,
  BulkReassignLeadSuccess,
  BulkSource,
  BulkSourceSuccess,
  ClearCardData,
  CommunicationBulkCount,
  CommunicationBulkCountSuccess,
  CommunicationBulkMessage,
  CommunicationBulkMessageSuccess,
  CommunicationCount,
  CommunicationCountSuccess,
  CommunicationMessage,
  CommunicationMessageSuccess,
  DeleteLeadRotation,
  DeleteLeads,
  DeleteLeadsSuccess,
  EmptyEffect,
  ExcelUpload,
  ExcelUploadSuccess,
  ExportLeads,
  ExportLeadsSuccess,
  FetchActiveCount,
  FetchAdditionalPropertyList,
  FetchAdditionalPropertyListSuccess,
  FetchAdditionalPropertyValue,
  FetchAdditionalPropertyValueSuccess,
  FetchAgencyNameList,
  FetchAgencyNameListAnonymous,
  FetchAgencyNameListAnonymousSuccess,
  FetchAgencyNameListSuccess,
  FetchAllParentLeadById,
  FetchAllParentLeadByIdSuccess,
  FetchBulkOperation,
  FetchBulkOperationSuccess,
  FetchCampaignList,
  FetchCampaignListAnonymous,
  FetchCampaignListAnonymousSuccess,
  FetchCampaignListSuccess,
  FetchChannelPartnerList,
  FetchChannelPartnerListAnonymous,
  FetchChannelPartnerListAnonymousSuccess,
  FetchChannelPartnerListSuccess,
  FetchCountryBasedCity,
  FetchCountryBasedCitySuccess,
  FetchDuplicateFeature,
  FetchDuplicateFeatureSuccess,
  FetchExcelUploadedList,
  FetchExcelUploadedSuccess,
  FetchExportStatus,
  FetchExportStatusSuccess,
  FetchLeadActiveCountSuccess,
  FetchLeadAltCountryCode,
  FetchLeadAltCountryCodeSuccess,
  FetchLeadAppointmentsByProjects,
  FetchLeadAppointmentsByProjectsSuccess,
  FetchLeadBaseFilterCount,
  FetchLeadBaseFilterCountSuccess,
  FetchLeadById,
  FetchLeadByIdWithArchive,
  FetchLeadByIdWithArchiveSuccess,
  FetchLeadCities,
  FetchLeadCitiesSuccess,
  FetchLeadClusterName,
  FetchLeadClusterNameSuccess,
  FetchLeadCommunities,
  FetchLeadCommunitiesSuccess,
  FetchLeadCountries,
  FetchLeadCountriesSuccess,
  FetchLeadCountryCode,
  FetchLeadCountryCodeSuccess,
  FetchLeadCurrency,
  FetchLeadCurrencySuccess,
  FetchLeadCustomTopFilters,
  FetchLeadCustomTopFiltersChildren,
  FetchLeadCustomTopFiltersChildrenSuccess,
  FetchLeadCustomTopFiltersSuccess,
  FetchLeadDroppedCount,
  FetchLeadDroppedCountSuccess,
  FetchLeadExport,
  FetchLeadExportSuccess,
  FetchLeadFlagsCountSuccess,
  FetchLeadHistoryList,
  FetchLeadHistoryListSuccess,
  FetchLeadIdSuccess,
  FetchLeadLandLine,
  FetchLeadLandLineSuccess,
  FetchLeadList,
  FetchLeadListSuccess,
  FetchLeadListV2,
  FetchLeadLocalites,
  FetchLeadLocalitesSuccess,
  FetchLeadNationality,
  FetchLeadNationalitySuccess,
  FetchLeadNotInterestedCount,
  FetchLeadNotInterestedCountSuccess,
  FetchLeadNotesList,
  FetchLeadNotesListSuccess,
  FetchLeadPostalCode,
  FetchLeadPostalCodeSuccess,
  FetchLeadRotation,
  FetchLeadRotationSuccess,
  FetchLeadStates,
  FetchLeadStatesSuccess,
  FetchLeadStatusCount,
  FetchLeadStatusCountSuccess,
  FetchLeadSubCommunities,
  FetchLeadSubCommunitiesSuccess,
  FetchLeadTowerNames,
  FetchLeadTowerNamesSuccess,
  FetchLeadUnitName,
  FetchLeadUnitNameSuccess,
  FetchLeadZones,
  FetchLeadZonesSuccess,
  FetchLeadsCommunicationByIds,
  FetchLeadsCommunicationByIdsSuccess,
  FetchLocations,
  FetchLocationsSuccess,
  FetchMatchingPropertyOrProjectList,
  FetchMatchingPropertyOrProjectListSuccess,
  FetchMigrateExcelUploadedList,
  FetchMigrateExcelUploadedSuccess,
  FetchModuleWiseSearchProperties,
  FetchModuleWiseSearchPropertiesSuccess,
  FetchProjectList,
  FetchProjectListSuccess,
  FetchPropertyList,
  FetchPropertyListSuccess,
  FetchQRProjectList,
  FetchQRProjectListSuccess,
  FetchQRPropertyList,
  FetchQRPropertyListSuccess,
  FetchSubSourceList,
  FetchSubSourceListSuccess,
  FetchUploadTypeNameList,
  FetchUploadTypeNameListSuccess,
  HasLeadAltInfo,
  HasLeadAltInfoSuccess,
  HasLeadInfo,
  HasLeadInfoSuccess,
  LeadActionTypes,
  LeadExcelUpload,
  LeadExcelUploadSuccess,
  MeetingOrVisitDone,
  MeetingOrVisitDoneSuccess,
  NavigateToLink,
  NavigateToLinkSuccess,
  PermanentDeleteLeads,
  PermanentDeleteLeadsSuccess,
  ReassignBoth,
  ReassignBothSuccess,
  ReassignLead,
  ReassignLeadSuccess,
  RestoreLeads,
  RestoreLeadsSuccess,
  SecondaryAssignLead,
  SecondaryAssignLeadSuccess,
  UpdateCardData,
  UpdateFilterPayload,
  UpdateInvoiceFilter,
  UpdateIsLoadMore,
  UpdateLead,
  UpdateLeadNotes,
  UpdateLeadNotesSuccess,
  UpdateLeadShareCount,
  UpdateLeadStatus,
  UpdateLeadStatusSuccess,
  UpdateLeadSuccess,
  UpdateLeadsTagInfo,
  UpdateMultipleLead,
  UpdateMultipleLeadStatus,
  UpdateMultipleLeadStatusSuccess,
  UploadLeadDocument,
  UploadLeadDocumentSuccess,
  UploadMappedColumns,
  UploadMigrateMappedColumns,
  updateDuplicateAssign,
  updateDuplicateAssignSuccess,
  updateLeadRotation
} from 'src/app/reducers/lead/lead.actions';
import {
  getFiltersPayload,
  getInvoiceFiltersPayload,
  getIsLeadCustomStatusEnabled,
  getIsLoadMore
} from 'src/app/reducers/lead/lead.reducer';
import {
  LeadPreviewChanged
} from 'src/app/reducers/loader/loader.actions';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { LeadsRotationService } from 'src/app/services/controllers/leads-rotation.service';
import { GetLeadsService } from 'src/app/services/controllers/leads.service';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class LeadEffects {

  getLeadList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_LIST),
      switchMap(() =>
        this._store.select(window.location.pathname.includes('/invoice') ? getInvoiceFiltersPayload : getFiltersPayload).pipe(
          take(1),
          map((filterPayload: any) => window.location.pathname.includes('/invoice') ? new UpdateInvoiceFilter(filterPayload) : new UpdateFilterPayload(filterPayload)) // Properly return the action
        )
      )
    )
  );

  addLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.ADD_LEAD),
      map((action: AddLead) => action.payload),
      switchMap((data: any) => {
        return this.api.add(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Lead added Successfully');
              this._store.dispatch(new AddLeadSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addQRLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.ADD_QR_LEAD),
      switchMap((action: AddQRLead) => {
        return this.api.addQRLead(action.payload, action.templateId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              // this._notificationService.success('Lead added Successfully');
              let initialState: any = {
                class: 'modal-450 modal-dialog-centered ph-modal-unset',
                ignoreBackdropClick: false,
              };
              this.modalRef = this.modalService.show(
                QRConfirmationComponent,
                initialState
              );
              return new AddQRLeadSuccess();
            }
            return new AddQRLeadSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addBulkLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.EXCEL_UPLOAD),
      switchMap((action: ExcelUpload) => {
        return this.api.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Excel uploaded Successfully');
              new FetchLeadList();
              return new ExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              new FetchLeadList();
              return new ExcelUploadSuccess(resp.data);
            }
          }),
          catchError((err) => {
            this._notificationService.error(err?.error?.Message?.split(',')[0]);
            return of(new OnError(err));
          })
        );
      })
    )
  );

  updateLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEAD),
      switchMap((action: UpdateLead) => {
        return this.api.update(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Lead Updated Successfully');
              this._store.dispatch(new UpdateLeadSuccess(resp.succeeded));
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateLeadNotes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEAD_NOTES),
      switchMap((action: UpdateLeadNotes) => {
        return this.api.updateNotes(action.id, action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Notes Updated Successfully'
              );
              if (action.canFetchLeadList) {
                // this._store.dispatch(new FetchLeadList());
                this._store.dispatch(new LeadPreviewChanged());
              }
              const userDetails = localStorage.getItem('userDetails');
              const currentUserName = userDetails ? JSON.parse(userDetails).given_name + ' ' + JSON.parse(userDetails).family_name || JSON.parse(userDetails).preferred_username || "Current User" : "Current User";
              const newNote = {
                fieldName: "Notes",
                filterKey: 2,
                oldValue: "",
                newValue: action.payload.notes,
                updatedBy: currentUserName,
                updatedOn: new Date().toISOString(),
                auditActionType: "Updated"
              };
              return new UpdateLeadNotesSuccess(newNote);
            }
            return new FetchLeadNotesListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadNotes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_NOTES_LIST),
      map((action: FetchLeadNotesList) => action),
      switchMap((data: any) =>
        this.api.getNotesList(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadNotesListSuccess(resp.data);
            }
            return new FetchLeadNotesListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updateMultipleLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_MULTIPLE_LEAD),
      switchMap((action: UpdateMultipleLead) => {
        if (action.isUpdateStatus)
          this._store.dispatch(new UpdateMultipleLeadStatus());
        return this.api.updateBulkLeadStatus(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Leads updated Successfully');
              if (action.isUpdateStatus) {
                this._store.dispatch(new UpdateMultipleLeadStatusSuccess());
              }
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_BY_ID),
      map((action: FetchLeadById) => action.leadId),
      switchMap((data: any) =>
        this.api.get(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadIdSuccess(resp.data);
            }
            return new FetchLeadIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  updateStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEAD_STATUS),
      switchMap((action: UpdateLeadStatus) => {
        return this.api.updateLeadStatus(action.payload, action.leadId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Status Updated Successfully'
              );
              this._store.dispatch(new UpdateLeadStatusSuccess());
              this._store.dispatch(new FetchLeadById(action.leadId));
              return action.canFetchLeadList
                ? new FetchLeadList()
                : new EmptyEffect();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateLeadShareCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEAD_SHARE_COUNT),
      switchMap((action: UpdateLeadShareCount) => {
        return this.api.increaseShareCount(action.leadId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Lead Successfully Shared');
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateLeadsTagInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEADS_TAG_INFO),
      mergeMap((action: UpdateLeadsTagInfo) => {
        return this.api.updateLeadTags(action.payload, action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Lead Successfully ${resp.message}`
              );
              return action.canFetchLeadList
                ? new FetchLeadList()
                : new EmptyEffect();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  emptyEffect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.EMPTY_EFFECT),
      switchMap((action: EmptyEffect) => {
        return [];
      })
    )
  );

  reassignLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.REASSIGN_LEAD),
      switchMap((action: ReassignLead) => {
        return this.api.reassignLead(action.payload).pipe(
          map((resp: any) => {
            let msg =
              'Lead' +
              (action.payload.leadIds?.length > 1 ? 's' : '') +
              ' Successfully ';
            this._store.select(getFiltersPayload).subscribe((data: any) => {
              msg +=
                data?.leadVisibility === 3
                  ? 'Assigned'
                  : action.payload?.userIds?.length &&
                    !action.payload?.userIds?.includes(EMPTY_GUID)
                    ? 'Reassigned'
                    : 'Unassigned';
            });
            if (resp.succeeded) {
              if (action?.payload?.leadIds?.length === 1) {
                this._store.dispatch(
                  new FetchLeadById(action?.payload?.leadIds?.[0])
                );
              }
              if (action.canFetchLeadList)
                this._store.dispatch(new FetchLeadList());
              this._notificationService.success(msg);
              return new ReassignLeadSuccess(resp);
            }
            return new ReassignLeadSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  reassignBoth$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.REASSIGN_BOTH),
      switchMap((action: ReassignBoth) => {
        return this.api.reassignBoth(action.payload).pipe(
          map((resp: any) => {
            let msg =
              'Lead' +
              (action.payload.leadIds?.length > 1 ? 's' : '') +
              ' Assignment Updated Successfully ';
            if (resp.succeeded) {
              if (action?.payload?.leadIds?.length === 1) {
                this._store.dispatch(
                  new FetchLeadById(action?.payload?.leadIds?.[0])
                );
              }
              if (action.canFetchLeadList)
                this._store.dispatch(new FetchLeadList());
              this._notificationService.success(msg);
              return new ReassignBothSuccess(resp);
            }
            return new ReassignBothSuccess();
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  secondaryAssignLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.SECONDARY_ASSIGN_LEAD),
      switchMap((action: SecondaryAssignLead) => {
        return this.api.secondaryAssignLead(action.payload).pipe(
          withLatestFrom(this._store.select(getFiltersPayload)),
          map(([resp, filtersData]: [any, any]) => {
            const count = action.payload?.leadIds?.length;
            if (count < 50) {
              let msg =
                'Lead' +
                (action.payload.leadIds?.length > 1 ? 's' : '') +
                ' Successfully ';
              msg +=
                filtersData?.leadVisibility === 3
                  ? 'Secondary Assigned'
                  : action.payload?.userIds?.length &&
                    !action.payload?.userIds?.includes(EMPTY_GUID)
                    ? 'Secondary Reassigned'
                    : 'Unassigned';
              if (resp.succeeded) {
                if (action.canFetchLeadList)
                  this._store.dispatch(new FetchLeadList());
                this._notificationService.success(msg);
                return new SecondaryAssignLeadSuccess(resp);
              }
            } else {
              this._store.dispatch(new FetchLeadList());
              return new SecondaryAssignLeadSuccess(resp);
            }
            return new SecondaryAssignLeadSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkReassignLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_REASSIGN_LEAD),
      switchMap((action: BulkReassignLead) => {
        return this.api.bulkReassignLead(action.payload).pipe(
          withLatestFrom(this._store.select(getFiltersPayload)),
          map(([resp, filtersData]: [any, any]) => {
            const count = action.payload?.leadIds?.length;
            if (count < 50) {
              let msg =
                'Lead' +
                (action.payload?.leadIds?.length > 1 ? 's' : '') +
                ' Successfully ';

              msg +=
                filtersData?.leadVisibility === 3
                  ? 'Assigned'
                  : action.payload?.userIds?.toString() !== EMPTY_GUID
                    ? 'Reassigned'
                    : 'Unassigned';

              if (resp.succeeded) {
                this._store.dispatch(new FetchLeadList());
                this._notificationService.success(msg);
                return new BulkReassignLeadSuccess(resp);
              }
            } else {
              this._store.dispatch(new FetchLeadList());
              return new BulkReassignLeadSuccess(resp);
            }
            return new BulkReassignLeadSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addBulkLeadExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.LEAD_EXCEL_UPLOAD),
      switchMap((action: LeadExcelUpload) => {
        return this.api.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Leads Excel uploaded Successfully'
              );
              return new LeadExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchLeadList();
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPLOAD_MAPPED_COLUMNS),
      switchMap((action: UploadMappedColumns) => {
        return this.api.uploadMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new ExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new ExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMigrateMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPLOAD_MIGRATE_MAPPED_COLUMNS),
      switchMap((action: UploadMigrateMappedColumns) => {
        return this.api.uploadMigrateMappingColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new ExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new ExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getMigrateExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST),
      map((action: FetchMigrateExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getMigrateExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchMigrateExcelUploadedSuccess(resp);
              }
              return new FetchMigrateExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  doesLeadExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.HAS_LEAD_INFO),
      switchMap((action: HasLeadInfo) => {
        return this.api.doesLeadExists(action.data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new HasLeadInfoSuccess(resp.data);
            }
            return new HasLeadInfoSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  doesLeadAltExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.HAS_LEAD_ALT_INFO),
      switchMap((action: HasLeadAltInfo) => {
        return this.api.doesLeadExists(action.data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new HasLeadAltInfoSuccess(resp.data);
            }
            return new HasLeadAltInfoSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadLeadDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPLOAD_DOCUMENT),
      switchMap((action: UploadLeadDocument) => {
        return this.api.uploadLeadDocument(action.id, action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Document Added Successfully'
              );
              this._store.dispatch(new FetchLeadById(action.payload.leadId));
              this._store.dispatch(new LeadPreviewChanged());
              return new UploadLeadDocumentSuccess(action.payload.documents);
            }
            return new UploadLeadDocumentSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteLeadDocument$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(LeadActionTypes.DELETE_DOCUMENT),
        switchMap((action: UploadLeadDocument) => {
          return this.api.deletedLeadDocument(action.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  'Lead Document Deleted Successfully'
                );
                this._store.dispatch(new FetchLeadById(action.payload.leadId));
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  getProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_PROJECT_LIST),
      map((action: FetchProjectList) => action),
      switchMap((data: any) => {
        return this.api.getProjectList(data?.isWithArchive).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchProjectListSuccess(resp.data);
            }
            return new FetchProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_QR_PROJECT_LIST),
      map((action: FetchQRProjectList) => action),
      switchMap((data: any) => {
        return this.api.getQRProjectList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRProjectListSuccess(resp.data);
            }
            return new FetchQRProjectListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getPropertyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_PROPERTY_LIST),
      map((action: FetchPropertyList) => action),
      switchMap((data: any) => {
        return this.api.getPropertyList(data?.isWithArchive).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchPropertyListSuccess(resp.data);
            }
            return new FetchPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getQRPropertyList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_QR_PROPERTY_LIST),
      map((action: FetchQRPropertyList) => action),
      switchMap((data: any) => {
        return this.api.getQRPropertyList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchQRPropertyListSuccess(resp.data);
            }
            return new FetchQRPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST),
      map((action: FetchExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchExcelUploadedSuccess(resp);
              }
              return new FetchExcelUploadedSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getExportStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_EXPORT_STATUS),
      map((action: FetchExportStatus) => action),
      switchMap((data: any) => {
        return this.api.getExportStatus(data.pageNumber, data.pageSize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchExportStatusSuccess(resp);
            }
            return new FetchExportStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCurrency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_CURRENCY_LIST),
      map((action: FetchLeadCurrency) => action),
      switchMap((data: any) => {
        return this.api.getCurrency().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCurrencySuccess(resp.data);
            }
            return new FetchLeadCurrencySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationCount$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(LeadActionTypes.COMMUNICATION_COUNT),
        map((action: CommunicationCount) => action),
        switchMap((data: any) => {
          this._store.dispatch(
            new CommunicationCountSuccess(data?.id, data?.payload)
          );
          return this.api.communicationCount(data.id, data.payload).pipe(
            map((resp: any) => { }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  communicationBulkCount$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(LeadActionTypes.COMMUNICATION_BULK_COUNT),
        map((action: CommunicationBulkCount) => action),
        switchMap((data: any) => {
          this._store.dispatch(
            new CommunicationBulkCountSuccess(data?.payload)
          );
          return this.api.communicationBulkCount(data.payload).pipe(
            map((resp: any) => { }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  meetingOrVisitDone$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.MEETING_OR_VISIT_DONE),
      map((action: MeetingOrVisitDone) => action),
      switchMap((data: any) => {
        return this.api.meetingOrVisitDone(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Updated Successfully');
              this._store.dispatch(new LeadPreviewChanged());
              return new MeetingOrVisitDoneSuccess();
            }
            this._store.dispatch(new MeetingOrVisitDoneSuccess());
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLocations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LOCATIONS),
      map((action: FetchLocations) => action),
      switchMap((data: any) => {
        return this.api.getLocations().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLocationsSuccess(resp.data);
            }
            return new FetchLocationsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_CITIES),
      map((action: FetchLeadCities) => action),
      switchMap((data: any) => {
        return this.api.getLeadCities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCitiesSuccess(resp.data);
            }
            return new FetchLeadCitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadStates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_STATES),
      map((action: FetchLeadStates) => action),
      switchMap((data: any) => {
        return this.api.getLeadStates().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadStatesSuccess(resp.data);
            }
            return new FetchLeadStatesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCountries$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_COUNTRIES),
      map((action: FetchLeadCountries) => action),
      switchMap((data: any) => {
        return this.api.getLeadCountries().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCountriesSuccess(resp.data);
            }
            return new FetchLeadCountriesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadSubCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES),
      map((action: FetchLeadSubCommunities) => action),
      switchMap((data: any) => {
        return this.api.getLeadSubCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadSubCommunitiesSuccess(resp.data);
            }
            return new FetchLeadSubCommunitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_COMMUNITIES),
      map((action: FetchLeadCommunities) => action),
      switchMap((data: any) => {
        return this.api.getLeadCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCommunitiesSuccess(resp.data);
            }
            return new FetchLeadCommunitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadTowerNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_TOWER_NAME),
      map((action: FetchLeadTowerNames) => action),
      switchMap((data: any) => {
        return this.api.getLeadTowerNames().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadTowerNamesSuccess(resp.data);
            }
            return new FetchLeadTowerNamesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadLocalites$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_LOCALITES),
      map((action: FetchLeadLocalites) => action),
      switchMap((data: any) => {
        return this.api.getLocalites().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadLocalitesSuccess(resp.data);
            }
            return new FetchLeadLocalitesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadZones$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_ZONES),
      map((action: FetchLeadZones) => action),
      switchMap((data: any) => {
        return this.api.getLeadZones().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadZonesSuccess(resp.data);
            }
            return new FetchLeadZonesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.DELETE_LEADS),
      switchMap((action: DeleteLeads) =>
        this.api.deleteLeads(action.payload).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              return [
                new DeleteLeadsSuccess(),
                new FetchLeadList(true, action.isInvoice),
              ];
            }
            return of(new FetchLeadList());
          }),
          catchError((err) => {
            this._notificationService.error('Failed to delete leads');
            return of(new OnError(err));
          })
        )
      )
    )
  );

  restoreLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.RESTORE_LEADS),
      map((action: RestoreLeads) => action),
      switchMap((data: any) => {
        return this.api.restoreLeads(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Restored Successfully');
              this._store.dispatch(new RestoreLeadsSuccess());
              return new FetchLeadList(
                true,
                location?.href?.includes('/invoice')
              );
            }
            return new FetchLeadList(
              true,
              location?.href?.includes('/invoice')
            );
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getMatchingPropertyProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS),
      map((action: FetchMatchingPropertyOrProjectList) => action),
      switchMap((data: any) =>
        this.commonService.getModuleList(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchMatchingPropertyOrProjectListSuccess(resp);
            }
            return new FetchMatchingPropertyOrProjectListSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getSubSourceList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_SUB_SOURCE_LIST),
      map((action: FetchSubSourceList) => action),
      switchMap((data: any) => {
        return this.api.getSubSourceList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchSubSourceListSuccess(resp.data);
            }
            return new FetchSubSourceListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_SOURCE),
      switchMap((action: BulkSource) => {
        return this.api.bulkSource(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new FetchSubSourceList());
              this._notificationService.success(
                `Lead Source Updated Successfully`
              );
              this._store.dispatch(new BulkSourceSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkProjects$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_PROJECTS),
      switchMap((action: BulkProjects) => {
        return this.api.bulkProjects(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Lead Project(s) Updated Successfully`
              );
              this._store.dispatch(new BulkProjectsSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAgencyNameList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST),
      map((action: FetchAgencyNameList) => action),
      switchMap((data: any) => {
        return this.api.getAgencyNames().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAgencyNameListSuccess(resp.data);
            }
            return new FetchAgencyNameListSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getAgencyNameListAnonymous$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS),
      map((action: FetchAgencyNameListAnonymous) => action),
      switchMap((data: any) => {
        return this.api.getAgencyNamesAnonymous().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAgencyNameListAnonymousSuccess(resp.data);
            }
            return new FetchAgencyNameListAnonymousSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getChannelPartnerList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST),
      map((action: FetchChannelPartnerList) => action),
      switchMap((data: any) => {
        return this.api.getChannelPartnerList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchChannelPartnerListSuccess(resp.data);
            }
            return new FetchChannelPartnerListSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getChannelPartnerListAnonymous$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS),
      map((action: FetchChannelPartnerListAnonymous) => action),
      switchMap((data: any) => {
        return this.api.getChannelPartnerListAnonymous().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchChannelPartnerListAnonymousSuccess(resp.data);
            }
            return new FetchChannelPartnerListAnonymousSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateDuplicateAssign$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST),
      switchMap((action: updateDuplicateAssign) => {
        return this.api.updateDuplicateAssign(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new FetchLeadList());
              if (!resp.items.length) {
                this._notificationService.success(`Lead Assigned Successfully`);
              }
              return new updateDuplicateAssignSuccess(resp);
            }
            return new updateDuplicateAssignSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDuplicateFeature$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_DUPLICATE_FEATURE),
      map((action: FetchDuplicateFeature) => action),
      switchMap((data: any) => {
        return this.api.getDuplicateFeature().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDuplicateFeatureSuccess(resp.data);
            }
            return new FetchDuplicateFeatureSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getLeadsExport$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEADS_EXPORT),
      map((action: FetchLeadExport) => action.payload),
      switchMap((data: any) => {
        return this.commonService.getModuleList(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadExportSuccess(resp.data);
            }
            return new FetchLeadExportSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadNotInterestedCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_NOT_INTERESTED_COUNT),
      map((action: FetchLeadNotInterestedCount) => action.payload),
      switchMap((data: any) => {
        let filterPayload;
        this._store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = {
            ...data,
            path: 'lead/counts/notInterested',
          };
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadNotInterestedCountSuccess(resp.data);
            }
            return new FetchLeadBaseFilterCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadDroppedCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_DROPPED_COUNT),
      map((action: FetchLeadDroppedCount) => action.payload),
      switchMap((data: any) => {
        let filterPayload;
        this._store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = {
            ...data,
            path: 'lead/counts/dropped',
          };
        });
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadDroppedCountSuccess(resp.data);
            }
            return new FetchLeadDroppedCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  exportLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.EXPORT_LEADS),
      switchMap((action: ExportLeads) => {
        let isCustomStatusEnabled;
        this._store
          .select(getIsLeadCustomStatusEnabled)
          .pipe(take(1))
          .subscribe((isEnabled: any) => {
            isCustomStatusEnabled = isEnabled;
          });
        return (
          window.location.pathname.includes('/invoice')
            ? this.api.exportLead(action.payload)
            : isCustomStatusEnabled &&
              window.location.pathname.includes('/leads/manage-leads')
              ? this.api.customExportLead(action.payload)
              : this.api.exportLead(action.payload)
        ).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Leads are being exported in excel format`
              );
              return new ExportLeadsSuccess(resp);
            }
            return new ExportLeadsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.COMMUNICATION_MESSAGE),
      switchMap((action: CommunicationMessage) => {
        return this.api.communicationMessage(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new CommunicationMessageSuccess(resp);
            }
            return new CommunicationMessageSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationBulkMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.COMMUNICATION_BULK_MESSAGE),
      switchMap((action: CommunicationBulkMessage) => {
        return this.api.communicationBulkMessage(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new CommunicationBulkMessageSuccess(resp);
            }
            return new CommunicationBulkMessageSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAppointmentsByProjects$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS),
      switchMap((action: FetchLeadAppointmentsByProjects) => {
        const payload = { ...action?.payload };
        payload.path = 'lead/getappointmentsbyprojects';
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchLeadAppointmentsByProjectsSuccess(resp?.data);
            }
            return new FetchLeadAppointmentsByProjectsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCommunicationsById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS),
      switchMap((action: FetchLeadsCommunicationByIds) => {
        const payload: {
          LeadIds: string[];
          path: string;
          showCommunicationCount?: boolean;
        } = { ...action?.payload, path: 'lead/communications' };

        if (!payload.showCommunicationCount) {
          return of({ type: '[Lead] Noop' });
        }

        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchLeadsCommunicationByIdsSuccess(resp?.data);
            }
            return new FetchLeadsCommunicationByIdsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addProjectsToLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.ADD_LEAD_PROJECTS),
      switchMap((action: AddLeadProjects) => {
        return this.api.addProjects(action?.payload).pipe(
          map((resp: any) => {
            return new AddLeadProjectsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addLeadRotationGroup$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.ADD_LEAD_ROTATION_GROUP),
      switchMap((action: AddLeadRotationGroup) =>
        this.leadRotationApi.createLeadRotationGroup(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Rotation Group added Successfully'
              );
            }
            return new FetchLeadRotation();
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  getLeadRotation = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_ROTATION),
      switchMap((action: FetchLeadRotation) =>
        this.leadRotationApi.getLeadRotation().pipe(
          map((resp: any) => {
            new FetchUsersListForReassignment();
            return new FetchLeadRotationSuccess(resp);
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  deleteLeadRotation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.DELETE_LEAD_ROTATION),
      switchMap((action: DeleteLeadRotation) =>
        this.leadRotationApi.deleteLeadRotation(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Rotation Group deleted Successfully'
              );
            }
            return new FetchLeadRotation();
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  updateLeadRotation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.UPDATE_LEAD_ROTATION),
      switchMap((action: updateLeadRotation) =>
        this.leadRotationApi.updateLeadRotation(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Lead Rotation Group updated Successfully'
              );
            }
            return new FetchLeadRotation();
          }),
          catchError((error) => of(new OnError(error)))
        )
      )
    )
  );

  getLeadCustomTopFiltersChildren$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN),
      map((action: FetchLeadCustomTopFiltersChildren) => action),
      switchMap((action: any) => {
        let filterPayload: any;
        this._store.select(getFiltersPayload).subscribe((data: any) => {
          filterPayload = data;
        });
        let LeadVisibility: any = filterPayload?.LeadVisibility
          ? filterPayload?.LeadVisibility
          : 0;
        let leadVisibility: any = filterPayload?.leadVisibility
          ? filterPayload?.leadVisibility
          : 0;
        let ScheduledType: any = filterPayload?.ScheduledType
          ? filterPayload?.ScheduledType
          : 0;
        let CustomFilterBaseIds: any =
          (filterPayload?.customThirdLevelFilterId
            ? [
              filterPayload?.customThirdLevelFilterId,
              filterPayload?.customSecondLevelFilterId,
              filterPayload?.customFirstLevelFilterId,
            ]
            : null) ||
          (filterPayload?.customSecondLevelFilterId
            ? [
              filterPayload?.customSecondLevelFilterId,
              filterPayload?.customFirstLevelFilterId,
            ]
            : null) ||
          (filterPayload?.customFirstLevelFilterId
            ? [filterPayload?.customFirstLevelFilterId]
            : null);
        filterPayload = {
          ...filterPayload,
          path: 'lead/custom-filters-count-level2',
          ScheduledType,
          LeadVisibility,
          leadVisibility,
          pageNumber: null,
          pageSize: null,
          CustomFilterBaseIds,
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCustomTopFiltersChildrenSuccess(resp.data);
            }
            return new FetchLeadCustomTopFiltersChildrenSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  permanentDeleteLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.PERMANENT_DELETE_LEADS),
      map((action: PermanentDeleteLeads) => action),
      switchMap((data: any) => {
        return this.api.permanentdeleteLeads(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              this._store.dispatch(new PermanentDeleteLeadsSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadList();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getBulkOperationList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_BULK_OPERATION),
      map((action: FetchBulkOperation) => action),
      switchMap((data: any) => {
        return this.api
          .getBulkOperation(data.pageNumber, data.pageSize, data.moduleType)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchBulkOperationSuccess(resp);
              }
              return new FetchBulkOperationSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getUploadTypeNameList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_UPLOADTYPENAME_LIST),
      map((action: FetchUploadTypeNameList) => action),
      switchMap((data: any) => {
        return this.api.getUploadTypeNameList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUploadTypeNameListSuccess(resp.data);
            }
            return new FetchUploadTypeNameListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadAuditHistory$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_HISTORY),
      map((action: FetchLeadHistoryList) => action),
      switchMap((data: any) =>
        this.api.getAuditHistory(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadHistoryListSuccess(resp.data);
            }
            return new FetchLeadHistoryListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getAdditionalProperty$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST),
      map((action: FetchAdditionalPropertyList) => action),
      switchMap((data: any) => {
        return this.api.getAdditionalProperties().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAdditionalPropertyListSuccess(resp.data);
            }
            return new FetchAdditionalPropertyListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAdditionalPropertyValue$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE),
      map((action: FetchAdditionalPropertyValue) => action),
      switchMap((data: any) => {
        return this.api.getAPValues(data?.key).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAdditionalPropertyValueSuccess(resp.data);
            }
            return new FetchAdditionalPropertyValueSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  navigateToLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.NAVIGATE_TO_LINK),
      map((action: NavigateToLink) => action),
      switchMap((data: any) => {
        return this.api.navigateToLink(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new NavigateToLinkSuccess(data.payload);
            }
            return new NavigateToLinkSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCampaignList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST),
      map((action: FetchCampaignList) => action),
      switchMap((data: any) => {
        return this.api.getCampaignList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCampaignListSuccess(resp.data);
            }
            return new FetchCampaignListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCampaignListAnonymous$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS),
      map((action: FetchCampaignListAnonymous) => action),
      switchMap((data: any) => {
        return this.api.getCampaignListAnonymous().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCampaignListAnonymousSuccess(resp.data);
            }
            return new FetchCampaignListAnonymousSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchCountryBasedCity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_COUNTRY_BASED_CITY),
      map((action: FetchCountryBasedCity) => action),
      switchMap((data: any) => {
        return this.api.getCountryBasedCity().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCountryBasedCitySuccess(resp?.data);
            }
            return new FetchCountryBasedCitySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  customTopLevelFilters$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS),
      withLatestFrom(
        this._store.pipe(select((state: any) => state?.lead?.filtersPayload))
      ),
      switchMap(([action, filtersFromState]) => {
        console.log(filtersFromState);

        let CustomFilterBaseIds: any =
          (filtersFromState?.CustomThirdLevelFilter
            ? [
              filtersFromState?.CustomThirdLevelFilter,
              filtersFromState?.CustomSecondLevelFilter,
              filtersFromState?.CustomFirstLevelFilter,
            ]
            : null) ||
          (filtersFromState?.CustomSecondLevelFilter
            ? [
              filtersFromState?.CustomSecondLevelFilter,
              filtersFromState?.CustomFirstLevelFilter,
            ]
            : null) ||
          (filtersFromState?.CustomFirstLevelFilter
            ? [filtersFromState?.CustomFirstLevelFilter]
            : null) ||
          null;
        const filters = {
          ...filtersFromState,
          path: 'lead/custom-filters-count-level1',
          CustomFilterBaseIds,
        };

        return this.commonService.getModuleListByAdvFilter(filters).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              localStorage.setItem('leadLevelFilter', JSON.stringify(resp?.data));
              return new FetchLeadCustomTopFiltersSuccess(resp?.data);
            } else {
              return new FetchLeadCustomTopFiltersSuccess([]);
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  filterBaseCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_BASEFILTER_COUNT),
      withLatestFrom(
        this._store.pipe(
          select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]) => {
        const fetchAction = action as FetchLeadBaseFilterCount;
        console.log('Action filters:', fetchAction.filtersPayload);
        console.log('State filters:', filtersFromState);

        // Merge filtersFromState with any additional filters from action
        // This preserves the existing state but allows overriding specific properties
        const filters = {
          ...filtersFromState,
          ...(fetchAction.filtersPayload || {}),
          path: 'lead/new/counts/basefilter',
        };
console.log('filters', filters);

        return this.commonService.getModuleListByAdvFilter(filters).pipe(
          map((resp: any) => {
            return new FetchLeadBaseFilterCountSuccess(resp?.data || {});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchLeadList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_LIST_V2),
      withLatestFrom(this._store.pipe(select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)), this._store.select(getIsLoadMore)),
      switchMap(([action, filtersFromState, isLoadmore]) => {
        if (!isLoadmore && filtersFromState?.pageNumber === 1) {
          this._store.dispatch(new ClearCardData());
        };
        this._store.dispatch(new UpdateIsLoadMore(false));

        let filters = {
          ...filtersFromState,
          path:
            this.isCustomStatusEnabled &&
              !window.location.pathname.includes('/invoice')
              ? 'lead/custom-filters'
              : 'lead/new/all',
        };

        return this.commonService.getModuleListByAdvFilter(filters).pipe(
          tap((resp: any) => this.dispatchLeadBatches(resp?.items, filters?.showCommunicationCount)),
          switchMap((resp: any) => {
            if (resp?.items?.length === 0 && filters?.pageNumber > 1) {
              filters = {
                ...filters,
                pageNumber: filters?.pageNumber - 1
              }
              if (window.location.pathname.includes('/invoice')) {
                this._store.dispatch(new UpdateInvoiceFilter(filters));
              } else {
                this._store.dispatch(new UpdateFilterPayload(filters));
              }
              return EMPTY
            }
            return of(
              new FetchLeadListSuccess(resp),
              new UpdateCardData(resp?.items)
            )
          }
          ),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  private dispatchLeadBatches(items: any[] = [], showCommunicationCount: any): void {
    if (!items?.length) return;

    const leadIds = items.map((lead: any) => lead?.id).filter(Boolean);
    const batchSize = 50;

    for (let i = 0; i < leadIds.length; i += batchSize) {
      const payload = {
        LeadIds: leadIds.slice(i, i + batchSize),
        showCommunicationCount: showCommunicationCount
      };
      this._store.dispatch(new FetchLeadsCommunicationByIds(payload));
    }
  }

  fetchLeadActiveCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_ACTIVE_COUNT),
      withLatestFrom(
        this._store.pipe(
          select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]) => {
        const filters = {
          ...filtersFromState,
          path: 'lead/counts/active',
        };
        return this.commonService.getModuleListByAdvFilter(filters).pipe(
          map((resp: any) => new FetchLeadActiveCountSuccess(resp?.data || {}))
        )
      }), catchError((err) => of(new OnError(err)))
    )
  );

  getLeadFlagCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_FLAGS_COUNT),
      withLatestFrom(
        this._store.pipe(
          select((state: any) =>
            window.location.pathname.includes('/invoice')
              ? state?.lead?.invoiceFiltersPayload
              : state?.lead?.filtersPayload
          )
        )
      ),
      switchMap(([action, filtersFromState]) => {
        const filters = {
          ...filtersFromState,
          path: 'lead/counts/custom-flags',
        };
        return this.commonService
          .getModuleListByAdvFilter(filters)
          .pipe(
            map((resp: any) => new FetchLeadFlagsCountSuccess(resp?.data || []))
          );
      }),
      catchError((err) => of(new OnError(err)))
    )
  );

  fetchLeadStatusCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_STATUS_COUNT),
      withLatestFrom(
        this._store.pipe(
          select((state: any) =>
            window.location.pathname.includes('/invoice')
              ? state?.lead?.invoiceFiltersPayload
              : state?.lead?.filtersPayload
          )
        )
      ),
      switchMap(([action, filtersFromState]) => {
        console.log('filtersFromState', filtersFromState);
        const filters = {
          ...filtersFromState,
          path: 'lead/counts/statuses',
        };
        return this.commonService
          .getModuleListByAdvFilter(filters)
          .pipe(
            map(
              (resp: any) =>
                new FetchLeadStatusCountSuccess(resp?.data?.items || [])
            )
          );
      }),
      catchError((err) => of(new OnError(err)))
    )
  );

  updateFiltersPayload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        LeadActionTypes.UPDATE_FILTER_PAYLOAD,
        LeadActionTypes.UPDATE_INVOICE_FILTER
      ),
      switchMap(async (action: any) => {
        this.isCustomStatusEnabled = await this._store
          .select(getIsLeadCustomStatusEnabled)
          .pipe(
            map((data: any) => data),
            take(1)
          )
          .toPromise();
        const showFilterCount = action.filter?.showFilterCount;
        const dontCallApi = action.filter?.dontCallApi;

        if (
          !this.isCustomStatusEnabled ||
          window.location.pathname.includes('/invoice')
        ) {
          if (showFilterCount && (!dontCallApi)) {
            this._store.dispatch(new FetchActiveCount());
            this._store.dispatch(new FetchLeadStatusCount());
          }
        } else if (showFilterCount && (!dontCallApi)) {
          this._store.dispatch(new FetchLeadCustomTopFilters());
        }
        // this._store.dispatch(new FetchLeadFlagsCount())
        this._store.dispatch(new FetchLeadListV2());
        if (showFilterCount && (!dontCallApi)) {
          return new FetchLeadBaseFilterCount();
        }
        return { type: '[Lead] Noop' };
      })
    )
  );

  getNationality$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_NATIONALITY),
      map((action: FetchLeadNationality) => action),
      switchMap((data: any) => {
        return this.api.getNationality().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadNationalitySuccess(resp?.data);
            }
            return new FetchLeadNationalitySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getClusterNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_CLUSTER_NAME),
      map((action: FetchLeadClusterName) => action),
      switchMap((data: any) => {
        return this.api.getLeadClusterName().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadClusterNameSuccess(resp?.data);
            }
            return new FetchLeadClusterNameSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUnitNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_UNIT_NAME),
      map((action: FetchLeadUnitName) => action),
      switchMap((data: any) => {
        return this.api.getLeadUnitName().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadUnitNameSuccess(resp?.data);
            }
            return new FetchLeadUnitNameSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAltCountryCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_ALT_COUNTRY_CODE),
      map((action: FetchLeadAltCountryCode) => action),
      switchMap((data: any) => {
        return this.api.getLeadAltCountryCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadAltCountryCodeSuccess(resp?.data);
            }
            return new FetchLeadAltCountryCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCountryCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_COUNTRY_CODE),
      map((action: FetchLeadCountryCode) => action),
      switchMap((data: any) => {
        return this.api.getLeadCountryCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadCountryCodeSuccess(resp?.data);
            }
            return new FetchLeadCountryCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getParentLeadById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID),
      map((action: FetchAllParentLeadById) => action.leadId),
      switchMap((data: any) =>
        this.api.getParentLeadData(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAllParentLeadByIdSuccess(resp.data);
            }
            return new FetchAllParentLeadByIdSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getLeadByIdWithArchive$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE),
      map((action: FetchLeadByIdWithArchive) => action.leadId),
      switchMap((data: any) =>
        this.api.getLeadByIdWithArchive(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadByIdWithArchiveSuccess(resp.data);
            }
            return new FetchLeadByIdWithArchiveSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getPostalCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_POSTAL_CODE),
      map((action: FetchLeadPostalCode) => action),
      switchMap((data: any) => {
        return this.api.getLeadPostalCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadPostalCodeSuccess(resp?.data);
            }
            return new FetchLeadPostalCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLandLine$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_LEAD_LANDLINE),
      map((action: FetchLeadLandLine) => action),
      switchMap((data: any) => {
        return this.api.getLeadLandLine().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchLeadLandLineSuccess(resp?.data);
            }
            return new FetchLeadLandLineSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getModuleWiseSearchProperties$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.FETCH_MODULE_WISE_SEARCH_PROPERTIES),
      map((action: FetchModuleWiseSearchProperties) => action.moduleType),
      switchMap((data: any) => {
        return this.api.getModuleWiseSearchProperties(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchModuleWiseSearchPropertiesSuccess(resp?.data);
            }
            return new FetchModuleWiseSearchPropertiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkAgency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_AGENCY),
      switchMap((action: BulkAgency) => {
        return this.api.bulkUpdateAgency(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Lead Agency(s) Updated Successfully`
              );
              this._store.dispatch(new BulkAgencySuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkChannelPartner$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_CHANNEL_PARTNER),
      switchMap((action: BulkChannelPartner) => {
        return this.api.bulkUpdateChannelPartner(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Lead Channel partner(s) Updated Successfully`
              );
              this._store.dispatch(new BulkChannelPartnerSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkCampaign$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LeadActionTypes.BULK_CAMPAIGN),
      switchMap((action: BulkCampaign) => {
        return this.api.bulkUpdateCampaign(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Lead Campaign(s) Updated Successfully`
              );
              this._store.dispatch(new BulkCampaignSuccess());
              return new FetchLeadList();
            }
            return new FetchLeadListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  isCustomStatusEnabled: boolean = false;

  constructor(
    private actions$: Actions,
    private api: GetLeadsService,
    private leadRotationApi: LeadsRotationService,
    private _notificationService: NotificationsService,
    private _store: Store<AppState>,
    private commonService: CommonService,
    private router: Router,
    private modalService: BsModalService,
    private modalRef: BsModalRef
  ) { }
}
