<form [formGroup]="leadRotationAddGroupForm">
    <div class="border pb-10 px-16 br-4">
        <div class="w-100 border-bottom pt-3" *ngIf="context === 'retention' && Teamusers?.length > 1">
            <form [formGroup]="leadRetentionAddGroupForm">
                <h5 class="fw-600 align-center">Rotation</h5>
                <div class=" d-flex mt-3 mb-3">
                    <label class="checkbox-container"
                        (click)="leadRetentionAddGroupForm.get('leadRotationEnabled')?.markAsTouched()">
                        <input type="checkbox" formControlName="leadRotationEnabled">
                        <span class="checkmark"></span>
                        <h5 class="fw-60 text-dark-800">Enable Lead Rotation</h5>
                    </label>
                </div>
            </form>
        </div>

        <div class="d-flex flex-wrap w-100">
            <ng-container *ngIf="context === 'rotation' else retention">
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="form-group mr-20 mb-4">
                        <div class="field-label">Team</div>
                        <form-errors-wrapper label="Team" [control]="leadRotationAddGroupForm?.controls?.['teamName']"
                            autocomplete="off">
                            <ng-select [virtualScroll]="true" [items]="allTeams" ResizableDropdown [ngClass]="{
                                 'disabled hide-arrow': group.isEditEnabled,
                                'pe-none blinking': allTeamsIsLoading }" formControlName="teamName" id="inpteam"
                                [closeOnSelect]="true" name="user" 
                                placeholder="Select" class="bg-white" bindLabel="teamName" bindValue="teamName"
                                searchable="true">
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="mr-20">
                        <div class="field-label">{{'LEADS.source' |
                            translate}}</div>
                        <form-errors-wrapper [control]="leadRotationAddGroupForm.controls['source']" label="Source">
                            <ng-select [virtualScroll]="true" [items]="filteredLeadSources" [multiple]="true"
                                formControlName="source" [closeOnSelect]="false" ResizableDropdown
                                bindLabel="displayName" bindValue="displayName"
                                [ngClass]="{'blinking pe-none': isSourceListLoading}" class="bg-white"
                                placeholder="{{ 'GLOBAL.select' | translate }} {{ 'LEADS.source' | translate }}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span class="text-truncate-1 break-all">
                                            {{item.displayName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </ng-container>
            <form [formGroup]="leadRetentionAddGroupForm">
                <ng-template #retention>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                        <div class="mr-20">
                            <div class="field-label">Team</div>
                            <form-errors-wrapper label="Team" [control]="leadRetentionAddGroupForm?.controls?.['team']"
                                autocomplete="off">
                                <ng-select [virtualScroll]="true" [items]="allTeams" ResizableDropdown [ngClass]="{
                                    'disabled hide-arrow': group.isEditEnabled,
                                    'pe-none blinking': allTeamsIsLoading }"
                                    [clearable]="group.isEditEnabled ? false : true" formControlName="team" id="inpteam"
                                    [closeOnSelect]="true" name="user" placeholder="Select" class="bg-white"
                                    bindLabel="teamName" bindValue="id" searchable="true">
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                        <div class="mr-20">
                            <div class="field-label">Status</div>
                            <form-errors-wrapper label="Status"
                                [control]="leadRetentionAddGroupForm?.controls?.['status']" autocomplete="off">
                                <ng-select [virtualScroll]="true" class="bg-white"
                                    [items]="isCustomStatusEnabled ? filteredCustomStatusList : masterLeadStatus"
                                    placeholder="Select Status" formControlName="status" bindLabel="displayName"
                                    bindValue="id" [multiple]="true" [closeOnSelect]="false"
                                    (change)="updateSubStatus()" ResizableDropdown>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item.displayName}}</span>
                                        </div>
                                    </ng-template>

                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                        <div class="mr-20">
                            <div class="field-label">Sub-Status</div>
                            <form-errors-wrapper label="Sub-Status"
                                [control]="leadRetentionAddGroupForm?.controls?.['subStatus']" autocomplete="off">
                                <ng-select [virtualScroll]="true" class="bg-white" formControlName="subStatus"
                                    [closeOnSelect]="false"
                                    [ngClass]="{'pe-none bg-secondary hide-arrow': !allCustomSubStatus?.length && !subStatusList?.length }"
                                    [items]="isCustomStatusEnabled ? allCustomSubStatus : subStatusList"
                                    [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                    placeholder="Select Sub-Status" bindLabel="displayName" bindValue="id">
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item.displayName}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                        <div class="mr-20 error-right">
                            <div class="field-label">Shift time</div>
                            <div class="d-flex ip-flex-col w-100">
                                <div class="w-50 ip-w-100 pr-6 ip-pr-0">
                                    <form-errors-wrapper label="Shift time from"
                                        [control]="leadRetentionAddGroupForm?.controls?.['shiftTimeFrom']"
                                        autocomplete="off">
                                        <input type="text" readonly formControlName="shiftTimeFrom"
                                            [owlDateTimeTrigger]="dtFrom" [owlDateTime]="dtFrom" placeholder="From" />

                                        <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                            [startAt]="leadRetentionAddGroupForm?.controls?.['shiftTimeFrom'] ? null : currentDate"
                                            #dtFrom="owlDateTime"></owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                                <div class="w-50  ip-w-100 pl-6 ip-pl-0 ip-mt-20">
                                    <form-errors-wrapper label="Shift time to"
                                        [control]="leadRetentionAddGroupForm?.controls?.['shiftTimeTo']"
                                        autocomplete="off">
                                        <input type="text" readonly formControlName="shiftTimeTo"
                                            [owlDateTimeTrigger]="dtTo" [owlDateTime]="dtTo" placeholder="To" />

                                        <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                            [startAt]="leadRetentionAddGroupForm?.controls?.['shiftTimeTo'] ? null : currentDate"
                                            #dtTo="owlDateTime"></owl-date-time>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100"
                        *ngIf="leadRetentionAddGroupForm.get('leadRotationEnabled').value">
                        <div class="mr-20">
                            <div class="field-label">Lead rotation time</div>
                            <form-errors-wrapper label="Rotation time"
                                [control]="leadRetentionAddGroupForm?.controls?.['rotationTime']" autocomplete="off">
                                <div class="align-center position-relative">
                                    <input type="number" min="1" placeholder="ex. enter..."
                                        formControlName="rotationTime" class="brbr-0 brtr-0">
                                    <div class="position-absolute right-10">
                                        <span class="mr-4 px-4 py-2 border-left"></span>
                                        <span class="text-xs text-light-slate">Minutes</span>
                                    </div>
                                </div>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100"
                        *ngIf="leadRetentionAddGroupForm.get('leadRotationEnabled').value">
                        <div class="mr-20">
                            <div class="field-label">Number of Rotation</div>
                            <form-errors-wrapper label="No. of rotation"
                                [control]="leadRetentionAddGroupForm?.controls?.['rotationNum']" autocomplete="off">
                                <ng-select [virtualScroll]="true" [items]="rotationNumOptions" bindLabel="label"
                                    bindValue="value" formControlName="rotationNum" ResizableDropdown
                                    placeholder="{{ 'GLOBAL.select' | translate }} number" class="bg-white">
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                        <div class="mr-20">
                            <div class="field-label">Days</div>
                            <form-errors-wrapper label="Days" [control]="leadRetentionAddGroupForm?.controls?.['days']"
                                autocomplete="off">
                                <ng-select [virtualScroll]="true" formControlName="days" [items]="days"
                                    [multiple]="true" [closeOnSelect]="false" class="bg-white"
                                    placeholder="{{'GLOBAL.select' | translate}} Days" bindLabel="name"
                                    bindValue="value" groupBy="selectedAllGroup" [selectableGroup]="true"
                                    ResizableDropdown [selectableGroupAsModel]="false">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span>
                                            <span class="text-truncate-1 break-all">Everyday</span>
                                        </div>
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <div class="checkbox-container">
                                            <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                                                [checked]="item$.selected">
                                            <span class="checkmark"></span>
                                            <span class="text-truncate-1 break-all">{{item.name}}</span>
                                        </div>
                                    </ng-template>
                                </ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </ng-template>
            </form>

            <ng-container *ngIf="context === 'rotation'">
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="mr-20 error-right">
                        <div class="field-label">Shift Time</div>
                        <div class="d-flex ip-flex-col w-100">
                            <div class="w-50 ip-w-100 pr-6 ip-pr-0">
                                <form-errors-wrapper label="Shift time from"
                                    [control]="leadRotationAddGroupForm?.controls?.['shiftTimeFrom']"
                                    autocomplete="off">
                                    <input type="text" readonly formControlName="shiftTimeFrom"
                                        [owlDateTimeTrigger]="dtFrom" [owlDateTime]="dtFrom" placeholder="From" />

                                    <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                        [startAt]="leadRotationAddGroupForm?.controls?.['shiftTimeFrom'] ? null : currentDate"
                                        #dtFrom="owlDateTime"></owl-date-time>
                                </form-errors-wrapper>
                            </div>
                            <div class="w-50  ip-w-100 pl-6 ip-pl-0 ip-mt-20">
                                <form-errors-wrapper label="Shift time to"
                                    [control]="leadRotationAddGroupForm?.controls?.['shiftTimeTo']" autocomplete="off">
                                    <input type="text" readonly formControlName="shiftTimeTo"
                                        [owlDateTimeTrigger]="dtTo" [owlDateTime]="dtTo" placeholder="To" />

                                    <owl-date-time [pickerType]="'timer'" [hour12Timer]="true"
                                        [startAt]="leadRetentionAddGroupForm?.controls?.['shiftTimeTo'] ? null : currentDate"
                                        #dtTo="owlDateTime"></owl-date-time>
                                </form-errors-wrapper>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="mr-20">
                        <div class="field-label">Lead Rotation Time</div>
                        <form-errors-wrapper label="Rotation time"
                            [control]="leadRotationAddGroupForm?.controls?.['rotationTime']" autocomplete="off">
                            <div class="align-center">
                                <input type="number" min="1" placeholder="ex. enter..." formControlName="rotationTime"
                                    class="brbr-0 brtr-0">
                                <div class="brtr-4 brbr-4 border border-start-0 fw-semi-bold py-12 px-5">
                                    minutes(s)
                                </div>

                            </div>
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="mr-20">
                        <div class="field-label">Number of Rotation </div>
                        <form-errors-wrapper label="No. of rotation"
                            [control]="leadRotationAddGroupForm?.controls?.['rotationNum']" autocomplete="off">
                            <ng-select [virtualScroll]="true" [items]="rotationNumOptions" bindLabel="label"
                                bindValue="value" formControlName="rotationNum" ResizableDropdown
                                placeholder="{{ 'GLOBAL.select' | translate }} number" class="bg-white">
                            </ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
                <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                    <div class="mr-20">
                        <div class="field-label">Buffer time</div>
                        <form-errors-wrapper label="bufferTime time"
                            [control]="leadRotationAddGroupForm?.controls?.['bufferTime']" autocomplete="off">
                            <div class="align-center">
                                <input type="number" min="1" placeholder="ex. enter..." formControlName="bufferTime"
                                    class="brbr-0 brtr-0">
                                <div class="brtr-4 brbr-4 border border-start-0 fw-semi-bold py-12 px-5">
                                    minutes(s)
                                </div>
                            </div>
                        </form-errors-wrapper>
                    </div>
                </div>
            </ng-container>
        </div>
        <ng-container *ngIf="group.isEditEnabled && context==='rotation'">
            <div class="border-bottom mt-16"></div>
            <div class="d-flex flex-wrap fw-600 text-black-200">
                <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                    *ngFor="let groupUser of group?.userIds">
                    <span class="align-center cursor-pointer text-dark-gray">
                        {{getAssignedToDetails(groupUser, allUsers, true)}}
                    </span>
                </div>
            </div>
        </ng-container>
        <ng-container *ngIf="group.isEditEnabled && context==='retention'">
            <div class="border-bottom mt-16"></div>
            <div class="d-flex flex-wrap fw-600 text-black-200">
                <div class="bg-secondary px-12 py-8 br-20 mr-20 mt-12 align-center text-nowrap"
                    *ngFor="let user of Teamusers">
                    <span class="align-center cursor-pointer"
                        [ngClass]="user?.isActive ? 'text-dark-gray': 'text-dark-red'">
                        {{getAssignedToDetails(user.id, allUsers, true)}}
                    </span>
                </div>
            </div>
        </ng-container>
        <div class="border-bottom my-12 pt-4"></div>
        <div class="flex-end">
            <div class="text-decoration-underline mr-16 cursor-pointer" (click)="closeAddGroup(group)">Cancel</div>
            <div class="btn-coal" *ngIf="context==='rotation'"
                [ngClass]="{'pe-none bg-gray-dark': !leadRotationAddGroupForm?.dirty}" (click)="addGroup(group)">Save
            </div>
            <div class="btn-coal" *ngIf="context==='retention'"
                [ngClass]="{'pe-none bg-gray-dark': !leadRetentionAddGroupForm?.dirty }" (click)="addGroup(group)">Save
            </div>
        </div>

    </div>
</form>