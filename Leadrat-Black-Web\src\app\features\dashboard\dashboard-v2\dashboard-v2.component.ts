import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { ChartType } from 'angular-google-charts';
import { filter, take, takeUntil } from 'rxjs/operators';

import {
  DASHBOARD_TYPE,
  DATE_FILTER_LIST,
  DATE_TYPE_FILTER_LIST,
  GRAPH_COLORS,
  LEAD_STATUS,
  LEAD_VISIBILITY_IMAGE,
  MONTHS,
  TO_PERFORMER_FILTERS_KEY_LABEL,
  VALIDATION_CLEAR,
  VALIDATION_SET,
  WEEK_DAYS,
  dashboardUpcomingEvents,
  upcomingEventsColors,
} from 'src/app/app.constants';
import {
  DateRange,
  LeadDateType,
  LeadFrequency,
  LeadSource,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  changeCalendar,
  dateConverted,
  getDateRange,
  getDaysInMonth,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  isEmptyObject,
  onPickerOpened,
  patchTimeZoneDate,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  FetchCPLTracking,
  FetchGoogleAdsMarketingFinances,
  FetchDashboardCallReport,
  FetchDashboardCountByStatus,
  FetchDashboardLeadReport,
  FetchDashboardLeadSource,
  FetchDashboardLeadTracker,
  FetchDashboardMeeting,
  FetchDashboardPerformance,
  FetchDashboardPipeline,
  FetchDashboardReceived,
  FetchDashboardRemindersCountByDate,
  FetchDashboardSiteVisit,
  FetchDashboardSourceDetails,
  FetchDashboardUpcomingEvents,
  FetchTopPerformer,
  UpdateCPLFilterPayload,
  UpdateGoogleAdsMarketingFilterPayload,
  UpdateCallReportFilterPayload,
  UpdateDashboardType,
  UpdateDashboardUsers,
  UpdateDashboardWithTeam,
  UpdateLeadReportFilterPayload,
  UpdateMeetingFilterPayload,
  UpdatePerformanceFilterPayload,
  UpdatePipelineFilterPayload,
  UpdateReceivedFilterPayload,
  UpdateSVFilterPayload,
  UpdateSource2FilterPayload,
  UpdateSourceFilterPayload,
  UpdateTrackerFilterPayload,
} from 'src/app/reducers/dashboard/dashboard.actions';
import {
  getCallReportIsLoading,
  getCountByStatusIsLoading,
  getDashboard,
  getDashboardRemindersCountByDate,
  getDashboardType,
  getDashboardUsers,
  getDashboardWithTeam,
  getFiltersPayload,
  getFiltersPayloadV1,
  getIsTopPerformersLoading,
  getLeadReportsIsLoading,
  getLeadTrackerIsLoading,
  getMeetingIsLoading,
  getPerformanceIsLoading,
  getPipelineIsLoading,
  getReceivedIsLoading,
  getRemindersCountIsLoading,
  getSiteVisitIsLoading,
  getSourceDetailsIsLoading,
  getSourceIsLoading,
  getTopPerformersList,
  getUpcomingEventsIsLoading,
  getGoogleAdsMarketing,
  getGoogleAdsMarketingIsLoading,
} from 'src/app/reducers/dashboard/dashboard.reducers';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchFbAccount } from 'src/app/reducers/Integration/integration.actions';
import { getFbAccount } from 'src/app/reducers/Integration/integration.reducer';
import {
  getPropertyListIsLoading,
  getSubSourceList,
} from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProfile } from 'src/app/reducers/profile/profile.actions';
import { getProfile } from 'src/app/reducers/profile/profile.reducers';
import {
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
} from 'src/app/reducers/project/project.reducer';
import { getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';
import {
  getOnlyReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { IntegrationService } from 'src/app/services/controllers/integration.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'dashboard-v2',
  templateUrl: './dashboard-v2.component.html',
})
export class DashboardV2Component implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('scrollContainer') scrollContainer: ElementRef | undefined;
  s3BucketUrl: string = env.s3ImageBucketURL;

  canViewDashboard: boolean = false;
  canViewUnassignedCount: boolean = false;
  canViewDeletedLeadsCount: boolean = false;
  canViewLeadSource: boolean = false;
  showLeftNav: boolean;
  showEvents: boolean = false;
  showCategoryUI: boolean = true;
  isAllDataPointsEmpty: boolean;
  dashboardType: any;
  DashboardSelectedUsers: Array<String> = [];
  withTeam: boolean;
  totalCount: number;
  allLeadsCount: number;

  dashboardUpcomingEvents = dashboardUpcomingEvents;
  LeadSource = LeadSource;
  leadVisibility = LEAD_VISIBILITY_IMAGE;
  leadStatus = LEAD_STATUS;
  dateTypeFilterList = DATE_TYPE_FILTER_LIST;
  dateFilterList = DATE_FILTER_LIST;
  cplDateFilterList = DATE_FILTER_LIST?.filter((item) => item.value !== 'TillDate');
  isEmptyObject = isEmptyObject;
  screen = window;
  upcomingEvents: Array<Array<Object>> = [[], [], [], [], [], [], [], []];
  selectedDateIndex: number;
  areEmptyUpcomingEvents: boolean;
  upcomingEventsCount: Object;
  remindersCountByDate: [];
  weekDaysList = WEEK_DAYS;
  monthsList = MONTHS;
  yearsList: Array<any> = [];
  years: number;
  months: string;
  monthDays: any;

  leadsCountByStatus: any;
  socialProfileSource: Array<any> = [];
  thirdPartySource: Array<any> = [];
  othersSource: Array<any> = [];
  leadCallReportData: any;
  leadActivityData: any;
  leadBarChartConfig: any;
  stackData: any[];
  pipelineData: any;
  leadReceivedData: any;
  meetingData: any;
  siteVisitData: any;
  leadReports: Array<Object>;
  leadReportsAll: Array<Object>;
  searchTermReport: string;
  teamsPerformanceData: any;
  teamsPerformanceDataAll: any;
  searchTermPerformance: string;
  searchTermCpl: string;
  searchTermGoogleAdsCpl: string;
  isPipelineLoading: boolean;
  isReceivedLoading: boolean;
  isCallReportLoading: boolean;
  isActivityLoading: boolean;
  isMeetingLoading: boolean;
  isSiteVisitLoading: boolean;
  isCountByStatusLoading: boolean;
  isSourceLoading: boolean;
  isSourceDetailsLoading: boolean;
  isUpcomingEventsLoading: boolean;
  isReminderCountLoading: boolean;
  isLeadReportsLoading: boolean;
  isPerformanceLoading: boolean;
  isGoogleAdsMarketingLoading: boolean;

  hoveredReceivedData: { [key: string]: number } = {};
  hoveredCallData: { [key: string]: number } = {};
  hoveredActivityData: { [key: string]: number } = {};
  hoveredMeetingData: { [key: string]: number } = {};
  hoveredSVData: { [key: string]: number } = {};

  globalFrequencyList: string[] = ['Day', 'Week', 'Month', 'Quarter', 'Year'];
  SVFrequencyList: string[] = ['Hour'];
  meetingFrequencyList: string[] = ['Hour'];
  activityFrequencyList: string[] = ['Hour'];
  callFrequencyList: string[] = ['Hour'];
  receivedFrequencyList: string[] = ['Hour'];
  leadSources: Array<any> = [];
  canViewLead: boolean;
  isTopPerformerseLoading: boolean = false;
  topPerformerseList: any[] = [];
  selectedFilter: number | null = 1;
  projectList: any[] = [];
  projectListIsLoading: boolean = false;
  propertyList: any[] = [];
  propertyListILoading: boolean = false;
  allSubSourceList: any[] = [];
  subSourceList: any[] = [];
  currency: any[] = [];
  defaultCurrency: string = '₹';
  userBasicDetails: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  userData: any;
  orgName: any;
  topPerformerFiltersKeyLabel: any = TO_PERFORMER_FILTERS_KEY_LABEL;
  showFilters: boolean = false;
  topPerformanceFilter: any = {
    FilterType: 1,
  };
  isSourcesLoading: boolean;
  cplTrackerData: any[] = []
  cplTrackerDataAll: any[] = [];
  googleAdsCplTrackerData: any[] = [];
  googleAdsCplTrackerDataAll: any[] = [];
  googleAdsAccountDetails: any[] = [];
  globalSettings: any;
  get isMyTeamDashboard(): boolean {
    return this.dashboardType == 1;
  }

  get isFilterTypeOne(): boolean {
    return this.topPerformanceFilter?.FilterType === 1;
  }

  filters: any = {
    showGlobalFilter: false,
    showSourceFilter: false,
    showSource2Filter: false,
    showPipelineFilter: false,
    showReceivedFilter: false,
    showCallFilter: false,
    showActivityFilter: false,
    showMeetingFilter: false,
    showSiteVisitFilter: false,
    showLeadReportFilter: false,
    showPerformanceFilter: false,
    showTopPerformerFilter: false,
    showCplFilter: false,
    showGoogleAdsCplFilter: false,
    showGoogleAdsCplAccount: false,
  };

  chartOptions: any = {
    sourceChartOptions: {},
    receivedChartOptions: {},
    callChartOptions: {},
    activityChartOptions: {},
    SVChartOptions: {},
    meetingChartOptions: {},
  };

  globalForm: FormGroup;
  sourceForm: FormGroup;
  source2Form: FormGroup;
  pipelineForm: FormGroup;
  receivedForm: FormGroup;
  callForm: FormGroup;
  activityForm: FormGroup;
  meetingForm: FormGroup;
  SVForm: FormGroup;
  leadReportForm: FormGroup;
  cplTrackForm: FormGroup;
  googleAdsCplTrackForm: FormGroup;
  performanceForm: FormGroup;
  topPerformerForm: FormGroup;

  isAssociatedData: boolean = false;
  hasOthersData: any;
  hasSPData: any;
  hasTPData: any;
  hasAssociatedData: any;
  dashboardTypes = DASHBOARD_TYPE.slice(0, 1);
  dashboardVisibility = DASHBOARD_TYPE[0];
  allUsers: Array<Object> = [];
  usersList: Array<Object> = [];
  onlyReportees: Array<Object> = [];
  userIds: Array<String> = [];
  accountDetails: Array<Object> = [];
  constructor(
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private store: Store<AppState>,
    private router: Router,
    private shareDataService: ShareDataService,
    private elRef: ElementRef,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder,
    private integrationService: IntegrationService
  ) {
    this.globalForm = this.fb.group({
      globalDType: ['All'],
      globalRange: ['TillDate'],
      globalDate: [null],
      globalFrequency: ['Week'],
    });

    this.sourceForm = this.fb.group({
      sourceDType: [null],
      sourceRange: [null],
      sourceDate: [null],
    });

    this.source2Form = this.fb.group({
      source2DType: [null],
      source2Range: [null],
      source2Date: [null],
    });

    this.pipelineForm = this.fb.group({
      pipelineDType: [null],
      pipelineRange: [null],
      pipelineDate: [null],
      pipelineSource: [null],
    });

    this.receivedForm = this.fb.group({
      receivedDType: [null],
      receivedRange: [null],
      receivedDate: [null],
      receivedFrequency: [null],
    });

    this.callForm = this.fb.group({
      callRange: [null],
      callDate: [null],
      callFrequency: [null],
    });

    this.activityForm = this.fb.group({
      activityDType: [null],
      activityRange: [null],
      activityDate: [null],
      activityFrequency: [null],
    });

    this.meetingForm = this.fb.group({
      meetingDType: [null],
      meetingRange: [null],
      meetingDate: [null],
      meetingFrequency: [null],
    });

    this.SVForm = this.fb.group({
      SVDType: [null],
      SVRange: [null],
      SVDate: [null],
      SVFrequency: [null],
    });

    this.leadReportForm = this.fb.group({
      leadReportDType: [null],
      leadReportRange: [null],
      leadReportDate: [null],
      leadReportSource: [null],
    });

    this.cplTrackForm = this.fb.group({
      cplTrackDType: [null],
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
      cplAccountName: [null],
    });

    this.googleAdsCplTrackForm = this.fb.group({
      cplTrackDType: [null],
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
      googleAdsCplAccountName: [null],
    });

    this.performanceForm = this.fb.group({
      performanceDType: [null],
      performanceRange: [null],
      performanceDate: [null],
      performanceSource: [null],
    });

    this.topPerformerForm = this.fb.group({
      topPerformerDType: ['All'],
      topPerformerRange: ['TillDate'],
      topPerformerDate: [null],
      Sources: [null],
      SubSources: [null],
      Projects: [null],
      Properties: [null],
    });

    this.metaTitle.setTitle('CRM | Dashboard');
    this.headerTitle.setLangTitle('SIDEBAR.dashboard');
    for (let i = 0; i < 4; i++) {
      this.yearsList.push((new Date().getFullYear() + i).toString());
    }

    this.validateForms();
    this.patchValues();
  }

  ngOnInit(): void {
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.store.dispatch(new FetchFbAccount());
    this.store
      .select(getFbAccount)
      .pipe(
        takeUntil(this.stopper),
        filter(accounts => accounts && accounts.length > 0),
        take(1)
      )
      .subscribe((response: any) => {
        this.accountDetails = response?.filter((account: any) => account?.ads?.length)
          .map((account: any) => ({
            accountId: account.accountId,
            facebookAccountName: account.facebookAccountName
          })) || [];

        if (this.accountDetails?.length > 0) {
          const firstAccount = this.accountDetails[0] as any;
          this.cplTrackForm.patchValue({
            cplAccountName: firstAccount.accountId
          });
          this.onCplTrackChange();
        }
      });

    // Initialize Google Ads accounts first, then call onDashboardSelection
    this.initializeGoogleAdsAccountDetails();

    this.store
      .select(getProfile)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.orgName = data?.displayName;
      });

    // this.store.dispatch(new FetchProjectIdWithName());
    // this.store.dispatch(new FetchPropertyWithIdNameList());
    // this.store.dispatch(new FetchSubSourceList());
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettings = data;
        this.currency = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultSymbol
          : null;
      });

    this.store
      .select(getFbAccount)
      .pipe(takeUntil(this.stopper))
      .subscribe((response: any) => {
        this.accountDetails = response?.filter((account: any) => account?.ads?.length)
          .map((account: any) => ({
            accountId: account.accountId,
            facebookAccountName: account.facebookAccountName
          })) || [];
        if (this.accountDetails.length > 0 && !this.cplTrackForm.get('cplAccountName')?.value) {
          const firstAccount = this.accountDetails[0] as any;
          this.cplTrackForm.patchValue({
            cplAccountName: firstAccount.accountId
          });
          this.onCplAccountChange();
        }
      });

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        this.currentDate = changeCalendar(
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
        );

        if (this.googleAdsAccountDetails.length > 0 &&
          this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) {
          // Ensure account is selected before making API call
          if (!this.googleAdsCplTrackForm.value.googleAdsCplAccountName) {
            this.googleAdsCplTrackForm.patchValue({
              googleAdsCplAccountName: this.googleAdsAccountDetails[0].accountId
            });
          }
          this.onGoogleAdsCplTrackChange();
        }
      });
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: string[]) => {
        const permissionsSet = new Set(permissions);
        const {
          totalLeads = 0,
          unassignedLeads = 0,
          deletedLeads = 0,
        } = this.leadsCountByStatus || {};
        let calculatedTotalCount = totalLeads - unassignedLeads - deletedLeads;
        if (permissionsSet.has('Permissions.Leads.ViewUnAssignedLead')) {
          this.canViewUnassignedCount = true;
          calculatedTotalCount += unassignedLeads;
        }
        if (permissionsSet.has('Permissions.Leads.Delete')) {
          this.canViewDeletedLeadsCount = true;
          calculatedTotalCount += deletedLeads;
        }
        this.totalCount = calculatedTotalCount;
        this.canViewDashboard = permissionsSet.has(
          'Permissions.Dashboard.View'
        );
        this.canViewLeadSource = permissionsSet.has(
          'Permissions.Leads.ViewLeadSource'
        );
        this.canViewLead = permissionsSet.has('Permissions.Leads.View');
        if (
          permissionsSet.has('Permissions.Dashboard.ViewTeam') &&
          !this.dashboardTypes.includes(DASHBOARD_TYPE[1])
        ) {
          this.dashboardTypes = [...this.dashboardTypes, DASHBOARD_TYPE[1]];
        }
        if (
          permissionsSet.has('Permissions.Dashboard.ViewOrg') &&
          !this.dashboardTypes.includes(DASHBOARD_TYPE[2])
        ) {
          this.dashboardTypes = [...this.dashboardTypes, DASHBOARD_TYPE[2]];
        }
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUsers = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUsers = assignToSort(this.allUsers, '');
      });

    this.store
      .select(getOnlyReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.onlyReportees = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.onlyReportees = assignToSort(this.onlyReportees, '');
      });

    this.store
      .select(getDashboard)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.leadsCountByStatus = data?.countByStatus;
        this.allLeadsCount = data?.leadSource?.totalCount;
        this.othersSource = data?.leadSource?.items?.filter(
          (item: any) => item.type === 0 && ![8, 14].includes(item.leadSource)
        ); //Gharoffice, Portfolio
        this.socialProfileSource = data?.leadSource?.items?.filter(
          (item: any) => item.type === 1 && ![42].includes(item.leadSource)
        );
        //tiktok
        this.thirdPartySource = data?.leadSource?.items?.filter(
          (item: any) => item.type === 2 && ![18].includes(item.leadSource)
        ); //SquareYards
        this.updateDataFlags();

        this.stackData = data?.sourceDetails;
        this.leadReceivedData = data?.received;
        this.pipelineData = data?.pipeline;
        this.leadCallReportData = data?.callReport;
        this.leadActivityData = data?.leadTracker;
        this.meetingData = data?.meeting;
        this.siteVisitData = data?.siteVisit;
        this.leadReports = data?.leadReports;
        this.leadReportsAll = data?.leadReports;
        this.teamsPerformanceData = data?.performance;
        this.teamsPerformanceDataAll = data?.performance;
        this.cplTrackerData = data?.cplTracking?.items;
        this.cplTrackerDataAll = data?.cplTracking?.items;
        this.googleAdsCplTrackerData = data?.googleAdsMarketing?.items;
        this.googleAdsCplTrackerDataAll = data?.googleAdsMarketing?.items;
        this.loadingData();

        if (data?.upcomingEvents?.upcomingEvents?.length)
          this.areEmptyUpcomingEvents = false;
        else this.areEmptyUpcomingEvents = true;
        this.upcomingEventsCount = data?.upcomingEvents;
        this.upcomingEvents = [];
        data?.upcomingEvents?.upcomingEvents?.map((item: any) => {
          let className = upcomingEventsColors[item?.status];
          let date = new Date(item?.scheduledDate).getDate();
          item = {
            ...item,
            time: getTimeZoneDate(
              item?.scheduledDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            ),
            className: className,
            date: getTimeZoneDate(
              item?.scheduledDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ),
          };
          this.upcomingEvents?.push(item);
        });
        this.upcomingEvents = this.upcomingEvents?.sort(
          (a: any, b: any) =>
            Number(new Date(a.scheduledDate)) -
            Number(new Date(b.scheduledDate))
        );
      });

    this.store
      .select(getDashboardType)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.dashboardType = data;
        this.years = new Date().getFullYear();
        this.months = MONTHS[new Date().getMonth()];
        this.updateDaysInMonth();
        // if (this.dashboardType) this.store.dispatch(new FetchTopPerformer({ LeadVisibility: data, FilterType: 1, Currency: this.defaultCurrency }));
      });

    this.store
      .select(getDashboardRemindersCountByDate)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.remindersCountByDate = data;
        const date = document.getElementsByClassName('calender-date');
        date.item(this.selectedDateIndex)?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center',
        });
      });

    this.store
      .select(getDashboardUsers)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.DashboardSelectedUsers = data;
      });

    this.store
      .select(getDashboardWithTeam)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.withTeam = data;
      });
    this.loadersInfo();

    this.store
      .select(getIsTopPerformersLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isTopPerformerseLoading = data;
      });

    this.store
      .select(getGoogleAdsMarketing)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.googleAdsCplTrackerData = data?.items || [];
        this.googleAdsCplTrackerDataAll = data?.items || [];
      });

    this.store
      .select(getTopPerformersList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.topPerformerseList = data;
      });
    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          ?.slice()
          ?.sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getPropertyWithIdNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data.slice().sort((a: any, b: any) => {
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });
      });

    this.store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    this.store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.propertyListILoading = data;
      });

    this.store
      .select(getSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allSubSourceList = data;
        this.subSourceList = Object.values(data)
          .flat()
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store.dispatch(new FetchAllSources());
    this.store
      .select(getAllSources)
      .pipe(takeUntil(this.stopper))
      .subscribe((leadSource: any) => {
        if (leadSource) {
          const enabledSources = leadSource
            .filter((source: any) => source.isEnabled)
            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));
          this.leadSources = [...enabledSources];
        } else {
          this.leadSources = [];
        }
      });
    this.store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });
  }

  initializeGoogleAdsAccountDetails() {
    const payload = {
      LeadSource: 44
    };
    this.integrationService.getAllGoogleCampaignAccounts(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response?.succeeded && response?.items) {
            this.googleAdsAccountDetails = response.items.map((account: any) => ({
              accountId: account.id,
              googleAdsAccountName: account.accountName,
              customerId: account.customerId,
              leadCount: account.leadCount,
              status: account.status
            }));
            if (this.googleAdsAccountDetails.length > 0) {
              const defaultAccountId = this.googleAdsAccountDetails[0].accountId;
              this.googleAdsCplTrackForm.patchValue({
                googleAdsCplAccountName: defaultAccountId
              });
              const accountPayload = {
                AccountId: defaultAccountId
              };
              this.store.dispatch(new UpdateGoogleAdsMarketingFilterPayload(accountPayload));
              if (this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) {
                this.onGoogleAdsCplTrackChange();
              }
            }
          } else {
            this.googleAdsAccountDetails = [];
          }
          this.onDashboardSelection();
        },
        error: (error) => {
          console.error('Error fetching Google Ads accounts:', error);
          this.googleAdsAccountDetails = [];
          this.onDashboardSelection();
        }
      });
  }

  generateStackData(stackData: any) {
    const aggregatedData: { [status: string]: any } = {};

    if (Array.isArray(stackData?.[1])) {
      stackData?.[1].forEach((item: any) => {
        if (item && Object.keys(item).length > 0) {
          Object.keys(item).forEach((status: string) => {
            const name = status.trim();

            if (name) {
              if (!aggregatedData[name]) {
                aggregatedData[name] = [];
              }

              const dataPoints = item[status].map((point: any) => ({
                y: point.count,
                label: point.label,
                indexLabel: point.count.toString(),
              }));

              aggregatedData[status].push({
                type: 'stackedBar100',
                name: status,
                showInLegend: true,
                color: GRAPH_COLORS[status],
                dataPoints: dataPoints,
                indexLabelFontColor: 'white',
              });
            }
          });
        }
      });
    }

    const convertedData: any[] = Object.values(aggregatedData);
    let flattenedData = convertedData.flat();

    let filteredData = flattenedData
      .map((entry) => {
        const filteredDataPoints = entry.dataPoints.filter(
          (dataPoint: any) => dataPoint.y !== 0
        );
        return filteredDataPoints.length > 0
          ? { ...entry, dataPoints: filteredDataPoints }
          : null;
      })
      .filter((entry) => entry !== null);

    let filteredLabels = filteredData
      .map((entry) => entry.dataPoints.map((dataPoint: any) => dataPoint.label))
      .flat();
    let uniqueArray = [...new Set(filteredLabels)];

    flattenedData = flattenedData.map((item) => {
      const filteredDataPoints = item.dataPoints
        .filter((dataPoint: any) => uniqueArray.includes(dataPoint.label))
        .map((dataPoint: any) => ({
          ...dataPoint,
          y: uniqueArray.includes(dataPoint.label)
            ? dataPoint.y
              ? dataPoint.y
              : null
            : null,
        }));
      return {
        ...item,
        dataPoints: filteredDataPoints,
      };
    });
    this.isAllDataPointsEmpty = flattenedData.every(
      (item) => item.dataPoints.length === 0
    );

    return flattenedData.flat();
  }

  generateDynamicData(data: any, category: string) {
    const transformedData: any = {};
    for (const [quarter, values] of Object.entries(data)) {
      for (const [key, value] of Object.entries(
        values as { [key: string]: number }
      )) {
        if (!transformedData[key]) {
          transformedData[key] = {};
        }
        transformedData[key][quarter] = value;
      }
    }
    const transformedData1 = [];
    let keysToDisplay: any = [];
    switch (category) {
      case 'call':
        keysToDisplay = [
          'connected',
          'dialed',
          'incoming',
          'missed',
          'notConnected',
        ];
        for (const category of Object.keys(transformedData)) {
          this.hoveredCallData[category] = 0;
          for (const timeCategory of Object.keys(transformedData[category])) {
            this.hoveredCallData[category] +=
              transformedData[category][timeCategory];
          }
        }
        break;
      case 'activity':
        keysToDisplay = ['whatsApp', 'sms', 'call', 'email'];
        for (const category of Object.keys(transformedData)) {
          this.hoveredActivityData[category] = 0;
          for (const timeCategory of Object.keys(transformedData[category])) {
            this.hoveredActivityData[category] +=
              transformedData[category][timeCategory];
          }
        }
        break;
      case 'meeting':
        keysToDisplay = ['meetingDone', 'meetingNotDone', 'meetingOverdue'];
        for (const category of Object.keys(transformedData)) {
          this.hoveredMeetingData[category] = 0;
          for (const timeCategory of Object.keys(transformedData[category])) {
            this.hoveredMeetingData[category] +=
              transformedData[category][timeCategory];
          }
        }
        break;
      case 'siteVisit':
        keysToDisplay = [
          'siteVisitDone',
          'siteVisitNotDone',
          'siteVisitOverdue',
        ];
        for (const category of Object.keys(transformedData)) {
          this.hoveredSVData[category] = 0;
          for (const timeCategory of Object.keys(transformedData[category])) {
            this.hoveredSVData[category] +=
              transformedData[category][timeCategory];
          }
        }
        break;
    }

    for (const [type, values] of Object.entries(transformedData)) {
      let dataPoints = [];
      let dataPoints1 = [];

      for (const [key, value] of Object.entries(
        values as { [key: string]: number }
      )) {
        const numericValue = Number(value);
        if (!isNaN(numericValue) && keysToDisplay.includes(type)) {
          dataPoints.push({ label: key, y: numericValue });
        }
        if (!isNaN(numericValue)) {
          dataPoints1.push({ label: key, y: numericValue });
        }
      }
      dataPoints = this.sortByDate(dataPoints);
      dataPoints1 = this.sortByDate(dataPoints1);
      transformedData1.push({
        name: this.getReferralName(type),
        type: 'splineArea',
        dataPoints,
        dataPoints1,
        color: GRAPH_COLORS[type],
      });
    }
    return transformedData1;
  }

  getReferralName(type: string) {
    switch (type) {
      case 'siteVisitDone':
        return this.globalSettings?.shouldRenameSiteVisitColumn ? 'referralTaken' : 'siteVisitDone';
      case 'siteVisitNotDone':
        return this.globalSettings?.shouldRenameSiteVisitColumn ? 'referralNotTaken' : 'siteVisitNotDone';
      case 'siteVisitOverdue':
        return this.globalSettings?.shouldRenameSiteVisitColumn ? 'referralOverdue' : 'siteVisitOverdue';
      default:
        return type;
    }
  }

  generateReceivedDynamicData(data: any) {
    const colorArray = [
      '#FCFF77',
      '#C6FF6D',
      '#13C6FF',
      '#FFBDD1',
      '#57FFD6',
      '#8CD6FF',
      '#D182F0',
      '#4E3EB2',
    ];

    const transformedData: any = {};
    for (const [quarter, values] of Object.entries(data)) {
      for (const [key, value] of Object.entries(
        values as { [key: string]: number }
      )) {
        if (!transformedData[key]) {
          transformedData[key] = {};
        }
        transformedData[key][quarter] = value;
      }
    }
    const transformedData1: any = [];
    for (const category of Object.keys(transformedData)) {
      this.hoveredReceivedData[category] = 0;
      for (const timeCategory of Object.keys(transformedData[category])) {
        this.hoveredReceivedData[category] +=
          transformedData[category][timeCategory];
      }
    }
    for (const [index, [type, values]] of Object.entries(
      transformedData
    ).entries() as any) {
      let dataPoints = [];
      let dataPoints1 = [];

      for (const [key, value] of Object.entries(
        values as { [key: string]: number }
      )) {
        const numericValue = Number(value);
        if (!isNaN(numericValue) && type) {
          dataPoints.push({ label: key, y: numericValue });
        }
        if (!isNaN(numericValue)) {
          dataPoints1.push({ label: key, y: numericValue });
        }
      }
      dataPoints = this.sortByDate(dataPoints);
      dataPoints1 = this.sortByDate(dataPoints1);
      transformedData1.push({
        name: LeadSource[type] || 'others',
        type: 'splineArea',
        dataPoints,
        dataPoints1,
        color: colorArray[index % colorArray.length],
      });
    }
    return transformedData1;
  }

  renderBarChart() {
    let leadSlabsData = [
      [
        'Total',
        this.pipelineData?.total,
        'stroke-color: #B5DF5D; stroke-width: 2; fill-color: #D1EB67',
        this.pipelineData?.total,
      ],
      [
        'In Engagement',
        this.pipelineData?.inEngagement,
        'stroke-color: #7AB260; stroke-width: 2; fill-color: #90C578',
        this.pipelineData?.inEngagement,
      ],
      [
        'Site Visit Scheduled',
        this.pipelineData?.siteVisitScheduled,
        'stroke-color: #6EB08C; stroke-width: 2; fill-color: #85C7A3',
        this.pipelineData?.siteVisitScheduled,
      ],
      [
        'Site Visit Done',
        this.pipelineData?.siteVisitDone,
        'stroke-color: #3E885F; stroke-width: 2; fill-color: #589E77',
        this.pipelineData?.siteVisitDone,
      ],
      [
        'Booked',
        this.pipelineData?.booked,
        'stroke-color: #265C55; stroke-width: 2; fill-color: #32766D',
        this.pipelineData?.booked,
      ],
      [
        'Booking Cancel',
        this.pipelineData?.bookingCancel,
        'stroke-color: #114246; stroke-width: 2; fill-color: #204F53',
        this.pipelineData?.bookingCancel,
      ],
    ];

    this.leadBarChartConfig = {
      type: ChartType.ColumnChart,
      columns: ['', '', { role: 'style' }, { role: 'annotation' }],
      data: [...leadSlabsData],
      options: {
        legend: 'none',
        tooltip: { isHtml: true },
        bar: { groupWidth: '100%' },
        hAxis: {
          titleTextStyle: {
            color: '#FF0000',
          },
          slantedText: false,
          showTextEvery: 1,
        },
        vAxis: {
          textPosition: 'none',
          gridlines: {
            color: '#FFFFFF',
          },
          baselineColor: '#FFFFFF',
        },
      },
    };
  }

  updateDaysInMonth() {
    const timeZoneOffset =
      this.userBasicDetails?.timeZoneInfo?.baseUTcOffset ||
      getSystemTimeOffset();
    let payload = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      Month: MONTHS.indexOf(this.months) + 1,
      Year: Number(this.years),
      timeZoneId:
        this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
        getSystemTimeZoneId(),
      baseUTcOffset: timeZoneOffset,
    };
    this.store.dispatch(new FetchDashboardRemindersCountByDate(payload));
    this.monthDays = getDaysInMonth(
      Number(this.years),
      MONTHS.indexOf(this.months)
    );
    if (
      new Date().getFullYear() === Number(this.years) &&
      MONTHS[new Date().getMonth()] === this.months
    ) {
      this.selectedDateIndex = dateConverted(
        new Date(this.currentDate.setHours(0, 0, 0, 0))
      ).getDate();
    } else this.selectedDateIndex = timeZoneOffset < 0 ? 1 : 0;
    this.onUpcomingEventsDateSelection(
      this.monthDays[this.selectedDateIndex],
      this.selectedDateIndex
    );
  }

  onUpcomingEventsDateSelection(selectedDate: Date, i: number) {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    this.selectedDateIndex = i;
    let payload = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      EventDate: setTimeZoneDate(selectedDate, timeZoneOffset),
    };
    this.store.dispatch(new FetchDashboardUpcomingEvents(payload));
  }

  validateForms() {
    this.globalForm.get('globalRange').valueChanges.subscribe((val: any) => {
      switch (val) {
        case 'TillDate':
        case 'Custom':
          this.globalFrequencyList = [
            'Day',
            'Week',
            'Month',
            'Quarter',
            'Year',
          ];
          this.globalForm.controls['globalFrequency'].setValue('Day');
          break;
        case 'Today':
        case 'Yesterday':
          this.globalFrequencyList = ['Hour'];
          this.globalForm.controls['globalFrequency'].setValue('Hour');
          break;
        case 'Last7Days':
          this.globalFrequencyList = ['Hour', 'Day'];
          this.globalForm.controls['globalFrequency'].setValue('Hour');
          break;
        case 'CurrentMonth':
          this.globalFrequencyList = ['Day', 'Week'];
          this.globalForm.controls['globalFrequency'].setValue('Day');
          break;
        default:
          this.globalFrequencyList = ['Hour'];
          this.globalForm.controls['globalFrequency'].setValue('Hour');
      }

      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.globalForm, 'globalDate', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.globalForm, 'globalDate');
      }
    });

    this.sourceForm.get('sourceRange').valueChanges.subscribe((val: any) => {
      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.sourceForm, 'sourceDate', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.sourceForm, 'sourceDate');
      }
    });

    this.source2Form.get('source2Range').valueChanges.subscribe((val: any) => {
      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.source2Form, 'source2Date', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.source2Form, 'source2Date');
      }
    });

    this.pipelineForm
      .get('pipelineRange')
      .valueChanges.subscribe((val: any) => {
        if (val == 'Custom') {
          toggleValidation(VALIDATION_SET, this.pipelineForm, 'pipelineDate', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.pipelineForm, 'pipelineDate');
        }
      });

    this.receivedForm
      .get('receivedRange')
      .valueChanges.subscribe((val: any) => {
        switch (val) {
          case 'TillDate':
          case 'Custom':
            this.receivedFrequencyList = [
              'Day',
              'Week',
              'Month',
              'Quarter',
              'Year',
            ];
            this.receivedForm.controls['receivedFrequency'].setValue('Day');
            break;
          case 'Today':
          case 'Yesterday':
            this.receivedFrequencyList = ['Hour'];
            this.receivedForm.controls['receivedFrequency'].setValue('Hour');
            break;
          case 'Last7Days':
            this.receivedFrequencyList = ['Hour', 'Day'];
            this.receivedForm.controls['receivedFrequency'].setValue('Hour');
            break;
          case 'CurrentMonth':
            this.receivedFrequencyList = ['Day', 'Week'];
            this.receivedForm.controls['receivedFrequency'].setValue('Day');
            break;
          default:
            this.receivedFrequencyList = ['Hour'];
            this.receivedForm.controls['receivedFrequency'].setValue('Hour');
        }

        if (val == 'Custom') {
          toggleValidation(VALIDATION_SET, this.receivedForm, 'receivedDate', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.receivedForm, 'receivedDate');
        }
      });

    this.callForm.get('callRange').valueChanges.subscribe((val: any) => {
      switch (val) {
        case 'TillDate':
        case 'Custom':
          this.callFrequencyList = ['Day', 'Week', 'Month', 'Quarter', 'Year'];
          this.callForm.controls['callFrequency'].setValue('Day');
          break;
        case 'Today':
        case 'Yesterday':
          this.callFrequencyList = ['Hour'];
          this.callForm.controls['callFrequency'].setValue('Hour');
          break;
        case 'Last7Days':
          this.callFrequencyList = ['Hour', 'Day'];
          this.callForm.controls['callFrequency'].setValue('Hour');
          break;
        case 'CurrentMonth':
          this.callFrequencyList = ['Day', 'Week'];
          this.callForm.controls['callFrequency'].setValue('Day');
          break;
        default:
          this.callFrequencyList = ['Hour'];
          this.callForm.controls['callFrequency'].setValue('Hour');
      }

      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.callForm, 'callDate', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.callForm, 'callDate');
      }
    });

    this.activityForm
      .get('activityRange')
      .valueChanges.subscribe((val: any) => {
        switch (val) {
          case 'TillDate':
          case 'Custom':
            this.activityFrequencyList = [
              'Day',
              'Week',
              'Month',
              'Quarter',
              'Year',
            ];
            this.activityForm.controls['activityFrequency'].setValue('Day');
            break;
          case 'Today':
          case 'Yesterday':
            this.activityFrequencyList = ['Hour'];
            this.activityForm.controls['activityFrequency'].setValue('Hour');
            break;
          case 'Last7Days':
            this.activityFrequencyList = ['Hour', 'Day'];
            this.activityForm.controls['activityFrequency'].setValue('Hour');
            break;
          case 'CurrentMonth':
            this.activityFrequencyList = ['Day', 'Week'];
            this.activityForm.controls['activityFrequency'].setValue('Day');
            break;
          default:
            this.activityFrequencyList = ['Hour'];
            this.activityForm.controls['activityFrequency'].setValue('Hour');
        }

        if (val == 'Custom') {
          toggleValidation(VALIDATION_SET, this.activityForm, 'activityDate', [
            Validators.required,
          ]);
        } else {
          toggleValidation(VALIDATION_CLEAR, this.activityForm, 'activityDate');
        }
      });

    this.meetingForm.get('meetingRange').valueChanges.subscribe((val: any) => {
      switch (val) {
        case 'TillDate':
        case 'Custom':
          this.meetingFrequencyList = [
            'Day',
            'Week',
            'Month',
            'Quarter',
            'Year',
          ];
          this.meetingForm.controls['meetingFrequency'].setValue('Day');
          break;
        case 'Today':
        case 'Yesterday':
          this.meetingFrequencyList = ['Hour'];
          this.meetingForm.controls['meetingFrequency'].setValue('Hour');
          break;
        case 'Last7Days':
          this.meetingFrequencyList = ['Hour', 'Day'];
          this.meetingForm.controls['meetingFrequency'].setValue('Hour');
          break;
        case 'CurrentMonth':
          this.meetingFrequencyList = ['Day', 'Week'];
          this.meetingForm.controls['meetingFrequency'].setValue('Day');
          break;
        default:
          this.meetingFrequencyList = ['Hour'];
          this.meetingForm.controls['meetingFrequency'].setValue('Hour');
      }

      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.meetingForm, 'meetingDate', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.meetingForm, 'meetingDate');
      }
    });

    this.SVForm.get('SVRange').valueChanges.subscribe((val: any) => {
      switch (val) {
        case 'TillDate':
        case 'Custom':
          this.SVFrequencyList = ['Day', 'Week', 'Month', 'Quarter', 'Year'];
          this.SVForm.controls['SVFrequency'].setValue('Day');
          break;
        case 'Today':
        case 'Yesterday':
          this.SVFrequencyList = ['Hour'];
          this.SVForm.controls['SVFrequency'].setValue('Hour');
          break;
        case 'Last7Days':
          this.SVFrequencyList = ['Hour', 'Day'];
          this.SVForm.controls['SVFrequency'].setValue('Hour');
          break;
        case 'CurrentMonth':
          this.SVFrequencyList = ['Day', 'Week'];
          this.SVForm.controls['SVFrequency'].setValue('Day');
          break;
        default:
          this.SVFrequencyList = ['Hour'];
          this.SVForm.controls['SVFrequency'].setValue('Hour');
      }

      if (val == 'Custom') {
        toggleValidation(VALIDATION_SET, this.SVForm, 'SVDate', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.SVForm, 'SVDate');
      }
    });

    this.leadReportForm
      .get('leadReportRange')
      .valueChanges.subscribe((val: any) => {
        if (val == 'Custom') {
          toggleValidation(
            VALIDATION_SET,
            this.leadReportForm,
            'leadReportDate',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.leadReportForm,
            'leadReportDate'
          );
        }
      });

    this.cplTrackForm
      .get('cplTrackRange')
      .valueChanges.subscribe((val: any) => {
        if (val == 'Custom') {
          toggleValidation(
            VALIDATION_SET,
            this.cplTrackForm,
            'cplTrackDate',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.cplTrackForm,
            'cplTrackDate'
          );
        }
      });

    this.performanceForm
      .get('performanceRange')
      .valueChanges.subscribe((val: any) => {
        if (val == 'Custom') {
          toggleValidation(
            VALIDATION_SET,
            this.performanceForm,
            'performanceDate',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.performanceForm,
            'performanceDate'
          );
        }
      });
  }

  patchValues() {
    this.store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: any) => {
        this.sourceForm.patchValue({
          sourceDType: LeadDateType[filters.source.DateType],
          sourceRange: filters.source.IsCustomDate
            ? 'Custom'
            : DateRange[filters.source.DateRange],
          sourceDate: filters.source?.FromDate
            ? [
              patchTimeZoneDate(
                filters.source?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.source?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
        });
        this.source2Form.patchValue({
          source2DType: LeadDateType[filters.source2.DateType],
          source2Range: filters.source2.IsCustomDate
            ? 'Custom'
            : DateRange[filters.source2.DateRange],
          source2Date: filters.source2?.FromDate
            ? [
              patchTimeZoneDate(
                filters.source2?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.source2?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
        });
        this.pipelineForm.patchValue({
          pipelineDType: LeadDateType[filters.pipeline.DateType],
          pipelineRange: filters.pipeline.IsCustomDate
            ? 'Custom'
            : DateRange[filters.pipeline.DateRange],
          pipelineDate: filters.pipeline?.FromDate
            ? [
              patchTimeZoneDate(
                filters.pipeline?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.pipeline?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
        });
        this.receivedForm.patchValue({
          receivedDType: LeadDateType[filters.received.DateType],
          receivedRange: filters.received.IsCustomDate
            ? 'Custom'
            : DateRange[filters.received.DateRange],
          receivedDate: filters.received?.FromDate
            ? [
              patchTimeZoneDate(
                filters.received?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.received?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
          receivedFrequency: LeadFrequency[filters.received.Frequency],
        });
        this.callForm.patchValue({
          callRange: filters.callReport.IsCustomDate
            ? 'Custom'
            : DateRange[filters.callReport.DateRange],
          callDate: filters.callReport?.FromDate
            ? [
              patchTimeZoneDate(
                filters.callReport?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.callReport?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
          callFrequency: LeadFrequency[filters.callReport.Frequency],
        });
        this.activityForm.patchValue({
          activityDType: LeadDateType[filters.activity.DateType],
          activityRange: filters.activity.IsCustomDate
            ? 'Custom'
            : DateRange[filters.activity.DateRange],
          activityDate: filters.activity?.FromDate
            ? [
              patchTimeZoneDate(
                filters.activity?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.activity?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
          activityFrequency: LeadFrequency[filters.activity.Frequency],
        });
        this.meetingForm.patchValue({
          meetingDType: LeadDateType[filters.meeting.DateType],
          meetingRange: filters.meeting.IsCustomDate
            ? 'Custom'
            : DateRange[filters.meeting.DateRange],
          meetingDate: filters.meeting?.FromDate
            ? [
              patchTimeZoneDate(
                filters.meeting?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.meeting?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
          meetingFrequency: LeadFrequency[filters.meeting.Frequency],
        });
        this.SVForm.patchValue({
          SVDType: LeadDateType[filters.siteVisit.DateType],
          SVRange: filters.siteVisit.IsCustomDate
            ? 'Custom'
            : DateRange[filters.siteVisit.DateRange],
          SVDate: filters.siteVisit?.FromDate
            ? [
              patchTimeZoneDate(
                filters.siteVisit?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.siteVisit?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
          SVFrequency: LeadFrequency[filters.siteVisit.Frequency],
        });
        this.leadReportForm.patchValue({
          leadReportDType: LeadDateType[filters.leadReport.DateType],
          leadReportRange: filters.leadReport.IsCustomDate
            ? 'Custom'
            : DateRange[filters.leadReport.DateRange],
          leadReportDate: filters.leadReport?.FromDate
            ? [
              patchTimeZoneDate(
                filters.leadReport?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.leadReport?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
        });
        const patchedDateRange = filters.cplTracker.DateRange === 4 ? 0 : filters.cplTracker.DateRange;

        this.cplTrackForm.patchValue({
          cplTrackDType: LeadDateType[filters.cplTracker.DateType],
          cplTrackRange: filters.cplTracker.IsCustomDate
            ? 'Custom'
            : (DateRange[patchedDateRange] || 'Today'),
          cplTrackDate:
            filters.cplTracker?.FromDate && filters.cplTracker.IsCustomDate
              ? [
                patchTimeZoneDate(
                  filters.cplTracker?.FromDate,
                  this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
                ),
                patchTimeZoneDate(
                  filters.cplTracker?.ToDate,
                  this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
                ),
              ]
              : null,
          cplAccountName: filters.cplTracker?.AccountId || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
        });
        this.performanceForm.patchValue({
          performanceDType: LeadDateType[filters.performance.DateType],
          performanceRange: filters.performance.IsCustomDate
            ? 'Custom'
            : DateRange[filters.performance.DateRange],
          performanceDate: filters.performance?.FromDate
            ? [
              patchTimeZoneDate(
                filters.performance?.FromDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
              patchTimeZoneDate(
                filters.performance?.ToDate,
                this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
              ),
            ]
            : null,
        });
      });
  }

  loadersInfo() {
    this.store
      .select(getCountByStatusIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCountByStatusLoading = isLoading;
      });

    this.store
      .select(getUpcomingEventsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isUpcomingEventsLoading = isLoading;
      });

    this.store
      .select(getRemindersCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isReminderCountLoading = isLoading;
      });

    this.store
      .select(getSourceIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceLoading = isLoading;
      });

    this.store
      .select(getSourceDetailsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSourceDetailsLoading = isLoading;
      });

    this.store
      .select(getPipelineIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isPipelineLoading = isLoading;
      });

    this.store
      .select(getReceivedIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isReceivedLoading = isLoading;
      });

    this.store
      .select(getCallReportIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCallReportLoading = isLoading;
      });

    this.store
      .select(getLeadTrackerIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isActivityLoading = isLoading;
      });

    this.store
      .select(getMeetingIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isMeetingLoading = isLoading;
      });

    this.store
      .select(getSiteVisitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isSiteVisitLoading = isLoading;
      });

    this.store
      .select(getLeadReportsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isLeadReportsLoading = isLoading;
      });

    this.store
      .select(getPerformanceIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isPerformanceLoading = isLoading;
      });

    this.store
      .select(getGoogleAdsMarketingIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGoogleAdsMarketingLoading = isLoading;
      });
  }

  loadingData() {
    if (this.pipelineData) {
      this.renderBarChart();
    }

    let initialChartOptions = {
      animationEnabled: true,
      toolTip: {
        shared: true,
        cornerRadius: 5,
        backgroundColor: '#292746cc',
        fontSize: 11,
        fontColor: 'white',
        borderColor: '#292746cc',
      },
      axisY: {
        gridThickness: 1,
        gridColor: '#D9D9D9',
        tickLength: 0,
        lineColor: 'transparent',
        labelFontColor: '#97959E',
      },
      axisX: {
        tickLength: 0,
        lineColor: 'transparent',
        labelFontColor: '#97959E',
      },
    };

    let datasetOption: any = {
      type: 'area',
      fillOpacity: 0.3,
      markerSize: 5,
      // markerColor: 'white',
      // markerBorderColor: 'black',
      // markerBorderThickness: 3,
    };

    if (this.stackData) {
      this.chartOptions.sourceChartOptions = {
        animationEnabled: true,
        axisX: {
          lineColor: 'transparent',
          labelFontColor: '#494949',
          labelFontWeight: 'bold',
        },
        axisY: {
          tickColor: 'white',
          lineColor: 'transparent',
          labelFontColor: 'white',
          gridColor: 'white',
        },
        toolTip: {
          shared: true,
          ...initialChartOptions?.toolTip,
        },
        legend: {
          fontColor: '#595667',
          fontSize: 12,
        },
        data: this.generateStackData(this.stackData),
      };
    }

    if (this.leadReceivedData) {
      this.chartOptions.receivedChartOptions = {
        ...initialChartOptions,
        toolTip: {
          ...initialChartOptions?.toolTip,
          updated: (e: any) => {
            // this.hoveredReceivedData = {}
            this.hoveredReceivedData.label = e.entries[0].dataPoint.label;
            for (const dataset of e.chart.options.data) {
              const dataPoint =
                dataset.dataPoints1[e.entries?.[0]?.dataPointIndex];
              if (dataPoint) {
                this.hoveredReceivedData[dataset.name] = dataPoint.y.toString();
              }
            }
          },
        },
        data: this.generateReceivedDynamicData(this.leadReceivedData).map(
          (dataset: any) => ({
            ...dataset,
            ...datasetOption,
          })
        ),
      };
    }

    if (this.leadCallReportData) {
      this.chartOptions.callChartOptions = {
        ...initialChartOptions,
        toolTip: {
          ...initialChartOptions?.toolTip,
          updated: (e: any) => {
            this.hoveredCallData.label = e.entries[0].dataPoint.label;
            for (const dataset of e.chart.options.data) {
              const dataPoint =
                dataset.dataPoints1[e.entries?.[0]?.dataPointIndex];
              if (dataPoint) {
                this.hoveredCallData[dataset.name] = dataPoint.y;
              }
            }
          },
        },
        data: this.generateDynamicData(this.leadCallReportData, 'call').map(
          (dataset: any) => ({
            ...dataset,
            ...datasetOption,
          })
        ),
      };
    }

    if (this.leadActivityData) {
      this.chartOptions.activityChartOptions = {
        ...initialChartOptions,
        toolTip: {
          ...initialChartOptions?.toolTip,
          updated: (e: any) => {
            this.hoveredActivityData.label = e.entries[0].dataPoint.label;
            for (const dataset of e.chart.options.data) {
              const dataPoint =
                dataset.dataPoints1[e.entries?.[0]?.dataPointIndex];
              if (dataPoint) {
                this.hoveredActivityData[dataset.name] = dataPoint.y;
              }
            }
          },
        },
        data: this.generateDynamicData(this.leadActivityData, 'activity').map(
          (dataset: any) => ({
            ...dataset,
            ...datasetOption,
          })
        ),
      };
    }

    if (this.meetingData) {
      this.chartOptions.meetingChartOptions = {
        ...initialChartOptions,
        toolTip: {
          ...initialChartOptions?.toolTip,
          updated: (e: any) => {
            this.hoveredMeetingData.label = e.entries[0].dataPoint.label;
            for (const dataset of e.chart.options.data) {
              const dataPoint =
                dataset.dataPoints1[e.entries?.[0]?.dataPointIndex];
              if (dataPoint) {
                this.hoveredMeetingData[dataset.name] = dataPoint.y;
              }
            }
          },
        },
        data: this.generateDynamicData(this.meetingData, 'meeting').map(
          (dataset: any) => ({
            ...dataset,
            ...datasetOption,
          })
        ),
      };
    }

    if (this.siteVisitData) {
      this.chartOptions.SVChartOptions = {
        ...initialChartOptions,
        toolTip: {
          ...initialChartOptions?.toolTip,
          updated: (e: any) => {
            this.hoveredSVData.label = e.entries[0].dataPoint.label;
            for (const dataset of e.chart.options.data) {
              const dataPoint =
                dataset.dataPoints1[e.entries?.[0]?.dataPointIndex];
              if (dataPoint) {
                this.hoveredSVData[dataset.name] = dataPoint.y;
              }
            }
          },
        },
        data: this.generateDynamicData(this.siteVisitData, 'siteVisit').map(
          (dataset: any) => ({
            ...dataset,
            ...datasetOption,
          })
        ),
      };
    }

    if (this.leadsCountByStatus?.totalLeads) {
      this.totalCount =
        this.leadsCountByStatus?.totalLeads -
        this.leadsCountByStatus?.unassignedLeads -
        this.leadsCountByStatus?.deletedLeads;
      if (this.canViewUnassignedCount) {
        this.totalCount += this.leadsCountByStatus?.unassignedLeads;
      }
      if (this.canViewDeletedLeadsCount) {
        this.totalCount += this.leadsCountByStatus?.deletedLeads;
      }
    }
  }

  //Filters

  onSearchByAgent(type: string) {
    if (type === 'Report') {
      this.leadReports = this.leadReportsAll?.filter((item: any) => {
        let fullName = (item?.firstName + ' ' + item?.lastName).toLowerCase();
        if (fullName?.includes(this.searchTermReport?.toLowerCase()))
          return true;
        return false;
      });
    } else if (type === 'Performance') {
      this.teamsPerformanceData = this.teamsPerformanceDataAll?.filter(
        (item: any) => {
          let fullName = (item?.firstName + ' ' + item?.lastName).toLowerCase();
          if (fullName?.includes(this.searchTermPerformance?.toLowerCase()))
            return true;
          return false;
        }
      );
    } else if (type === 'Cpl') {
      this.cplTrackerData = this.cplTrackerDataAll?.filter(
        (item: any) => {
          let fullName = (item?.userName).toLowerCase();
          if (fullName?.includes(this.searchTermCpl?.toLowerCase()))
            return true;
          return false;
        }
      );
    } else if (type === 'GoogleAdsCpl') {
      this.googleAdsCplTrackerData = this.googleAdsCplTrackerDataAll?.filter(
        (item: any) => {
          if (item?.userName) {
            let fullName = item.userName.toLowerCase();
            if (fullName?.includes(this.searchTermGoogleAdsCpl?.toLowerCase()))
              return true;
          }
          return false;
        }
      );
    }
  }

  onSourceChange() {
    if (!this.sourceForm.valid) {
      validateAllFormFields(this.sourceForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.sourceForm.value.sourceDType],
      DateRange:
        this.sourceForm.value.sourceRange !== 'Custom'
          ? DateRange[this.sourceForm.value.sourceRange]
          : null,
      IsCustomDate:
        this.sourceForm.value.sourceRange == 'Custom' ? true : false,
      FromDate:
        this.sourceForm.value.sourceDate &&
          this.sourceForm.value.sourceRange == 'Custom'
          ? setTimeZoneDate(this.sourceForm.value.sourceDate[0], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.sourceForm.value.sourceRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.sourceForm.value.sourceDate &&
          this.sourceForm.value.sourceRange == 'Custom'
          ? setTimeZoneDate(this.sourceForm.value.sourceDate[1], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.sourceForm.value.sourceRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateSourceFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadSource(payload));
    this.filters.showSourceFilter = false;
  }

  onSource2Change() {
    if (!this.source2Form.valid) {
      validateAllFormFields(this.source2Form);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.source2Form.value.source2DType],
      DateRange:
        this.source2Form.value.source2Range !== 'Custom'
          ? DateRange[this.source2Form.value.source2Range]
          : null,
      IsCustomDate:
        this.source2Form.value.source2Range == 'Custom' ? true : false,
      FromDate: this.source2Form.value.sourceDate
        ? setTimeZoneDate(this.source2Form.value.source2Date[0], timeZoneOffset)
        : null,
      ToDate: this.source2Form.value.source2Date
        ? setTimeZoneDate(this.source2Form.value.source2Date[1], timeZoneOffset)
        : null,
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };

    this.store.dispatch(new FetchDashboardSourceDetails(payload));
    this.store.dispatch(new UpdateSource2FilterPayload(payload));
    this.filters.showSource2Filter = false;
  }

  onPipelineChange() {
    if (!this.pipelineForm.valid) {
      validateAllFormFields(this.pipelineForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.pipelineForm.value.pipelineDType],
      DateRange:
        this.pipelineForm.value.pipelineRange !== 'Custom'
          ? DateRange[this.pipelineForm.value.pipelineRange]
          : null,
      IsCustomDate:
        this.pipelineForm.value.pipelineRange == 'Custom' ? true : false,
      FromDate: this.pipelineForm.value.pipelineDate
        ? setTimeZoneDate(
          this.pipelineForm.value.pipelineDate[0],
          timeZoneOffset
        )
        : null,
      ToDate: this.pipelineForm.value.pipelineDate
        ? setTimeZoneDate(
          this.pipelineForm.value.pipelineDate[1],
          timeZoneOffset
        )
        : null,
      LeadSources: this.pipelineForm.value.pipelineSource,
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new FetchDashboardPipeline(payload));
    this.store.dispatch(new UpdatePipelineFilterPayload(payload));
    this.filters.showPipelineFilter = false;
  }

  onReceivedChange() {
    if (!this.receivedForm.valid) {
      validateAllFormFields(this.receivedForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.receivedForm.value.receivedDType],
      DateRange:
        this.receivedForm.value.receivedRange !== 'Custom'
          ? DateRange[this.receivedForm.value.receivedRange]
          : null,
      Frequency: LeadFrequency[this.receivedForm.value.receivedFrequency],
      timeZoneId:
        this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset:
        this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      IsCustomDate:
        this.receivedForm.value.receivedRange == 'Custom' ? true : false,
      FromDate:
        this.receivedForm.value.receivedDate &&
          this.receivedForm.value.receivedRange == 'Custom'
          ? setTimeZoneDate(
            this.receivedForm.value.receivedDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.receivedForm.value
                .receivedRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.receivedForm.value.receivedDate &&
          this.receivedForm.value.receivedRange == 'Custom'
          ? setTimeZoneDate(
            this.receivedForm.value.receivedDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.receivedForm.value
                .receivedRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateReceivedFilterPayload(payload));
    this.store.dispatch(new FetchDashboardReceived(payload));
    this.filters.showReceivedFilter = false;
  }

  onCallChange() {
    if (!this.callForm.valid) {
      validateAllFormFields(this.callForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateRange:
        this.callForm.value.callRange !== 'Custom'
          ? DateRange[this.callForm.value.callRange]
          : null,
      Frequency: LeadFrequency[this.callForm.value.callFrequency],
      IsCustomDate: this.callForm.value.callRange == 'Custom' ? true : false,
      FromDate:
        this.callForm.value.callDate &&
          this.callForm.value.callRange == 'Custom'
          ? setTimeZoneDate(this.callForm.value.callDate[0], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.callForm.value.callRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.callForm.value.callDate &&
          this.callForm.value.callRange == 'Custom'
          ? setTimeZoneDate(this.callForm.value.callDate[1], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.callForm.value.callRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateCallReportFilterPayload(payload));
    this.store.dispatch(new FetchDashboardCallReport(payload));
    this.filters.showCallFilter = false;
  }

  onActivityChange() {
    if (!this.activityForm.valid) {
      validateAllFormFields(this.activityForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.activityForm.value.activityDType],
      DateRange:
        this.activityForm.value.activityRange !== 'Custom'
          ? DateRange[this.activityForm.value.activityRange]
          : null,
      Frequency: LeadFrequency[this.activityForm.value.activityFrequency],
      IsCustomDate:
        this.activityForm.value.activityRange == 'Custom' ? true : false,
      FromDate:
        this.activityForm.value.activityDate &&
          this.activityForm.value.activityRange == 'Custom'
          ? setTimeZoneDate(
            this.activityForm.value.activityDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.activityForm.value
                .activityRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.activityForm.value.activityDate &&
          this.activityForm.value.activityRange == 'Custom'
          ? setTimeZoneDate(
            this.activityForm.value.activityDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.activityForm.value
                .activityRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateTrackerFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadTracker(payload));
    this.filters.showActivityFilter = false;
  }

  onMeetingChange() {
    if (!this.meetingForm.valid) {
      validateAllFormFields(this.meetingForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.meetingForm.value.meetingDType],
      DateRange:
        this.meetingForm.value.meetingRange !== 'Custom'
          ? DateRange[this.meetingForm.value.meetingRange]
          : null,
      Frequency: LeadFrequency[this.meetingForm.value.meetingFrequency],
      IsCustomDate:
        this.meetingForm.value.meetingRange == 'Custom' ? true : false,
      FromDate:
        this.meetingForm.value.meetingDate &&
          this.meetingForm.value.meetingRange == 'Custom'
          ? setTimeZoneDate(
            this.meetingForm.value.meetingDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.meetingForm.value.meetingRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.meetingForm.value.meetingDate &&
          this.meetingForm.value.meetingRange == 'Custom'
          ? setTimeZoneDate(
            this.meetingForm.value.meetingDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.meetingForm.value.meetingRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateMeetingFilterPayload(payload));
    this.store.dispatch(new FetchDashboardMeeting(payload));
    this.filters.showMeetingFilter = false;
  }

  onSVChange() {
    if (!this.SVForm.valid) {
      validateAllFormFields(this.SVForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.SVForm.value.SVDType],
      DateRange:
        this.SVForm.value.SVRange !== 'Custom'
          ? DateRange[this.SVForm.value.SVRange]
          : null,
      Frequency: LeadFrequency[this.SVForm.value.SVFrequency],
      IsCustomDate: this.SVForm.value.SVRange == 'Custom' ? true : false,
      FromDate:
        this.SVForm.value.SVDate && this.SVForm.value.SVRange == 'Custom'
          ? setTimeZoneDate(this.SVForm.value.SVDate[0], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[this.SVForm.value.SVRange as keyof typeof DateRange],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.SVForm.value.SVDate && this.SVForm.value.SVRange == 'Custom'
          ? setTimeZoneDate(this.SVForm.value.SVDate[1], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[this.SVForm.value.SVRange as keyof typeof DateRange],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdateSVFilterPayload(payload));
    this.store.dispatch(new FetchDashboardSiteVisit(payload));
    this.filters.showSiteVisitFilter = false;
  }

  onLeadReportChange() {
    if (!this.leadReportForm.valid) {
      validateAllFormFields(this.leadReportForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.leadReportForm.value.leadReportDType],
      DateRange:
        this.leadReportForm.value.leadReportRange !== 'Custom'
          ? DateRange[this.leadReportForm.value.leadReportRange]
          : null,
      IsCustomDate:
        this.leadReportForm.value.leadReportRange == 'Custom' ? true : false,
      FromDate:
        this.leadReportForm.value.leadReportDate &&
          this.leadReportForm.value.leadReportRange == 'Custom'
          ? setTimeZoneDate(
            this.leadReportForm.value.leadReportDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.leadReportForm.value
                .leadReportRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.leadReportForm.value.leadReportDate &&
          this.leadReportForm.value.leadReportRange == 'Custom'
          ? setTimeZoneDate(
            this.leadReportForm.value.leadReportDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.leadReportForm.value
                .leadReportRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      LeadSources: this.leadReportForm.value.leadReportSource,
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,
    };
    this.store.dispatch(new UpdateLeadReportFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadReport(payload));
    this.filters.showLeadReportFilter = false;
  }

  onCplTrackChange() {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }

    let currentFilters: any = {};
    this.store.select(getFiltersPayload).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.cplTracker || {};
    }).unsubscribe();

    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;

    const isCustom = this.cplTrackForm.value.cplTrackRange === 'Custom';
    const selectedRange = this.cplTrackForm.value.cplTrackRange;

    // Get range dates or default to today
    const [defaultFrom, defaultTo] =
      getDateRange(DateRange[selectedRange as keyof typeof DateRange], this.currentDate) ||
      [this.currentDate, this.currentDate];

    const dateRangePayload: any = {
      DateRange: isCustom ? null : DateRange[selectedRange],
      IsCustomDate: isCustom,
      FromDate: isCustom
        ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate?.[0], timeZoneOffset)
        : setTimeZoneDate(defaultFrom, timeZoneOffset),
      ToDate: isCustom
        ? setTimeZoneDate(this.cplTrackForm.value.cplTrackDate?.[1], timeZoneOffset)
        : setTimeZoneDate(defaultTo, timeZoneOffset),
    };
    const payload: any = {
      ...currentFilters,
      ...dateRangePayload,
      AccountId: this.cplTrackForm.value.cplAccountName || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
    };

    this.store.dispatch(new UpdateCPLFilterPayload(payload));
    this.store.dispatch(new FetchCPLTracking(payload));
    this.filters.showCplFilter = false;
  }

  getSelectedAccountName(): string {
    const selectedAccountId = this.cplTrackForm.controls['cplAccountName'].value;
    if (!selectedAccountId) {
      return 'Select Account';
    }
    const selectedAccount = this.accountDetails.find((account: any) => account.accountId === selectedAccountId);
    return (selectedAccount as any)?.facebookAccountName || 'Select Account';
  }

  onCplAccountChange() {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }

    // Get current filter payload and merge with account filter
    let currentFilters: any = {};
    this.store.select(getFiltersPayload).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.cplTracker || {};
    }).unsubscribe();

    const selectedAccountId = this.cplTrackForm.value.cplAccountName;
    let payload: any = {
      ...currentFilters,
      AccountId: selectedAccountId,
    };

    this.store.dispatch(new UpdateCPLFilterPayload(payload));
    this.store.dispatch(new FetchCPLTracking(payload));
    this.filters.showCplAccount = false;
  }

  onGoogleAdsCplTrackChange() {
    if (!this.googleAdsCplTrackForm.valid) {
      validateAllFormFields(this.googleAdsCplTrackForm);
      return;
    }
    let currentFilters: any = {};
    this.store.select(getFiltersPayloadV1).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.googleAdsMarketing || {};
    }).unsubscribe();
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const isCustom = this.googleAdsCplTrackForm.value.cplTrackRange === 'Custom';
    const selectedRange = this.googleAdsCplTrackForm.value.cplTrackRange;
    const [defaultFrom, defaultTo] =
      getDateRange(DateRange[selectedRange as keyof typeof DateRange], this.currentDate) ||
      [this.currentDate, this.currentDate];
    const dateRangePayload: any = {
      DateRange: isCustom ? null : DateRange[selectedRange],
      IsCustomDate: isCustom,
      FromDate: isCustom
        ? setTimeZoneDate(this.googleAdsCplTrackForm.value.cplTrackDate?.[0], timeZoneOffset)
        : setTimeZoneDate(defaultFrom, timeZoneOffset),
      ToDate: isCustom
        ? setTimeZoneDate(this.googleAdsCplTrackForm.value.cplTrackDate?.[1], timeZoneOffset)
        : setTimeZoneDate(defaultTo, timeZoneOffset),
    };
    const selectedAccountId = this.ensureGoogleAdsAccountSelected();
    const payload: any = {
      ...currentFilters,
      ...dateRangePayload,
      AccountId: selectedAccountId,
    };
    if (!payload.AccountId && selectedAccountId) {
      payload.AccountId = selectedAccountId;
    }
    this.store.dispatch(new UpdateGoogleAdsMarketingFilterPayload(payload));
    this.store.dispatch(new FetchGoogleAdsMarketingFinances(payload));
    this.filters.showGoogleAdsCplFilter = false;
  }

  onGoogleAdsCplAccountChange() {
    if (!this.googleAdsCplTrackForm.valid) {
      validateAllFormFields(this.googleAdsCplTrackForm);
      return;
    }
    let currentFilters: any = {};
    this.store.select(getFiltersPayloadV1).pipe(takeUntil(this.stopper)).subscribe((filters: any) => {
      currentFilters = filters?.googleAdsMarketing || {};
    }).unsubscribe();
    const selectedAccountId = this.googleAdsCplTrackForm.value.googleAdsCplAccountName;
    let payload: any = {
      ...currentFilters,
      AccountId: selectedAccountId,
    };
    if (!payload.AccountId && selectedAccountId) {
      payload.AccountId = selectedAccountId;
    }
    this.store.dispatch(new UpdateGoogleAdsMarketingFilterPayload(payload));
    this.store.dispatch(new FetchGoogleAdsMarketingFinances(payload));
    this.filters.showGoogleAdsCplAccount = false;
  }

  getSelectedGoogleAdsAccountName(): string {
    const selectedAccountId = this.googleAdsCplTrackForm?.value?.googleAdsCplAccountName;
    if (selectedAccountId) {
      const selectedAccount = this.googleAdsAccountDetails?.find(
        (account: any) => account.accountId === selectedAccountId
      );
      return selectedAccount?.googleAdsAccountName || 'Select Account';
    }
    return 'Select Account';
  }

  ensureGoogleAdsAccountSelected(): string | null {
    let selectedAccountId = this.googleAdsCplTrackForm?.value?.googleAdsCplAccountName;
    if (!selectedAccountId && this.googleAdsAccountDetails?.length > 0) {
      selectedAccountId = this.googleAdsAccountDetails[0].accountId;
      this.googleAdsCplTrackForm.patchValue({
        googleAdsCplAccountName: selectedAccountId
      });
    }
    return selectedAccountId;
  }

  onPerformanceChange() {
    if (!this.performanceForm.valid) {
      validateAllFormFields(this.performanceForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.performanceForm.value.performanceDType],
      DateRange:
        this.performanceForm.value.performanceRange !== 'Custom'
          ? DateRange[this.performanceForm.value.performanceRange]
          : null,
      IsCustomDate:
        this.performanceForm.value.performanceRange == 'Custom' ? true : false,
      FromDate:
        this.performanceForm.value.performanceDate &&
          this.performanceForm.value.performanceRange == 'Custom'
          ? setTimeZoneDate(
            this.performanceForm.value.performanceDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.performanceForm.value
                .performanceRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.performanceForm.value.performanceDate &&
          this.performanceForm.value.performanceRange == 'Custom'
          ? setTimeZoneDate(
            this.performanceForm.value.performanceDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.performanceForm.value
                .performanceRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      LeadSources: this.performanceForm.value.performanceSource,
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };
    this.store.dispatch(new UpdatePerformanceFilterPayload(payload));
    this.store.dispatch(new FetchDashboardPerformance(payload));
    this.filters.showPerformanceFilter = false;
  }

  onGlobalChange() {
    if (!this.globalForm.valid) {
      validateAllFormFields(this.globalForm);
      return;
    }
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0);

    const timeZonedToday = setTimeZoneDate(today, timeZoneOffset);
    let payload: any = {
      LeadVisibility: this.dashboardType,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.globalForm.value.globalDType],
      DateRange:
        this.globalForm.value.globalRange !== 'Custom'
          ? DateRange[this.globalForm.value.globalRange]
          : null,
      Frequency: LeadFrequency[this.globalForm.value.globalFrequency],
      IsCustomDate:
        this.globalForm.value.globalRange == 'Custom' ? true : false,
      FromDate:
        this.globalForm.value.globalDate &&
          this.globalForm.value.globalRange == 'Custom'
          ? setTimeZoneDate(this.globalForm.value.globalDate[0], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.globalForm.value.globalRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.globalForm.value.globalDate &&
          this.globalForm.value.globalRange == 'Custom'
          ? setTimeZoneDate(this.globalForm.value.globalDate[1], timeZoneOffset)
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.globalForm.value.globalRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      IsWithTeam: DASHBOARD_TYPE.indexOf(this.dashboardVisibility) !== 0 ? this.withTeam : null,

    };

    const cplPayload = {
      ...payload,
      DateRange: 0,               // Use 0 instead of 4
      FromDate: timeZonedToday,   // Today
      ToDate: timeZonedToday,
      AccountId: this.cplTrackForm.value.cplAccountName || (this.accountDetails.length > 0 ? (this.accountDetails[0] as any).accountId : null),
    };
    this.store.dispatch(new UpdateSourceFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadSource(payload));

    // this.store.dispatch(new UpdateSource2FilterPayload(payload));
    // this.store.dispatch(new FetchDashboardSourceDetails(payload));

    // this.store.dispatch(new FetchDashboardPipeline(payload));
    // this.store.dispatch(new UpdatePipelineFilterPayload(payload));

    this.store.dispatch(new UpdateReceivedFilterPayload(payload));
    this.store.dispatch(new FetchDashboardReceived(payload));

    this.store.dispatch(new UpdateCallReportFilterPayload(payload));
    this.store.dispatch(new FetchDashboardCallReport(payload));

    this.store.dispatch(new UpdateTrackerFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadTracker(payload));

    this.store.dispatch(new UpdateMeetingFilterPayload(payload));
    this.store.dispatch(new FetchDashboardMeeting(payload));

    this.store.dispatch(new UpdateSVFilterPayload(payload));
    this.store.dispatch(new FetchDashboardSiteVisit(payload));

    this.store.dispatch(new UpdateLeadReportFilterPayload(payload));
    this.store.dispatch(new FetchDashboardLeadReport(payload));

    this.store.dispatch(new UpdatePerformanceFilterPayload(payload));
    this.store.dispatch(new FetchDashboardPerformance(payload));

    this.store.dispatch(new UpdateCPLFilterPayload(cplPayload));
    this.store.dispatch(new FetchCPLTracking(cplPayload));

    this.filters.showGlobalFilter = false;
  }

  onFilterClick(filterName: any): void {
    if (!this.filters[filterName]) {
      this.filters[filterName] = true;

      for (const key in this.filters) {
        if (key !== filterName) {
          this.filters[key] = false;
        }
      }
    } else {
      for (const key in this.filters) {
        this.filters[key] = false;
      }
    }
  }

  updateDataFlags() {
    // Recalculate flags for associated data
    this.hasOthersData = this.othersSource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );
    this.hasSPData = this.socialProfileSource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );
    this.hasTPData = this.thirdPartySource?.some(
      (profile: any) =>
        !this.isAssociatedData || (this.isAssociatedData && profile.count > 0)
    );

    this.hasAssociatedData =
      this.hasOthersData || this.hasSPData || this.hasTPData;
  }

  onClickNavigateFromCount(
    visibility: string,
    firstLevelFilter: string = null,
    secondLevelFilter: string = null,
    userId: string = null,
    filterSource: any,
    scheduledDateTypeFilter: string = null,
    scheduledType: string = null,
    doneStatus: string = null,
    isLeadReportNav: boolean = false,
    statusDate: any[] = [null, null],
    section: string = null
  ) {
    let withTeam;
    let date: any[] = [null, null];
    let dtype: string = null;

    if (visibility === 'Deleted' && this.dashboardType === 0) {
      userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    } else if (visibility === 'Deleted' && this.dashboardType === 1) {
      userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
      withTeam = true;
    }

    if (visibility === "Team's") {
      visibility = 'All';
    }

    let newDate = new Date(this.currentDate);
    switch (section) {
      case 'source':
        switch (this.sourceForm.value.sourceRange) {
          case 'TillDate':
            date[0] = null;
            date[1] = null;
            break;
          case 'Today':
            date[0] = new Date(this.currentDate);
            date[1] = new Date(this.currentDate);
            break;
          case 'Yesterday':
            date[0] = newDate.setDate(newDate.getDate() - 1);
            date[1] = new Date(this.currentDate).setDate(
              new Date(this.currentDate).getDate() - 1
            );
            break;
          case 'Last7Days':
            date[0] = newDate.setDate(newDate.getDate() - 6);
            date[1] = new Date(this.currentDate);
            break;
          case 'CurrentMonth':
            const firstDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth(),
              1
            );
            const lastDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth() + 1,
              0
            );
            date[0] = firstDay;
            date[1] = lastDay;
            break;
          case 'Custom':
            date = [
              new Date(this.sourceForm.value.sourceDate?.[0]),
              new Date(this.sourceForm.value.sourceDate?.[1]),
            ];
            break;
        }
        dtype = this.sourceForm.value.sourceDType;
        filterSource = [LeadSource[filterSource]];
        break;
      case 'leadReport':
        switch (this.leadReportForm.value.leadReportRange) {
          case 'TillDate':
            date[0] = null;
            date[1] = null;
            break;
          case 'Today':
            date[0] = new Date(this.currentDate);
            date[1] = new Date(this.currentDate);
            break;
          case 'Yesterday':
            date[0] = newDate.setDate(newDate.getDate() - 1);
            date[1] = new Date().setDate(new Date().getDate() - 1);
            break;
          case 'Last7Days':
            date[0] = newDate.setDate(newDate.getDate() - 6);
            date[1] = new Date();
            break;
          case 'CurrentMonth':
            const firstDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth(),
              1
            );
            const lastDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth() + 1,
              0
            );
            date[0] = firstDay;
            date[1] = lastDay;
            break;
          case 'Custom':
            date = [
              new Date(this.leadReportForm.value.leadReportDate?.[0]),
              new Date(this.leadReportForm.value.leadReportDate?.[1]),
            ];
            break;
        }
        dtype = this.leadReportForm.value.leadReportDType;
        filterSource = this.leadReportForm.value.leadReportSource;
        break;
      case 'performance':
        switch (this.performanceForm.value.performanceRange) {
          case 'TillDate':
            date[0] = null;
            date[1] = null;
            break;
          case 'Today':
            date[0] = new Date(this.currentDate);
            date[1] = new Date(this.currentDate);
            break;
          case 'Yesterday':
            date[0] = newDate.setDate(newDate.getDate() - 1);
            date[1] = new Date(this.currentDate).setDate(
              new Date(this.currentDate).getDate() - 1
            );
            break;
          case 'Last7Days':
            date[0] = newDate.setDate(newDate.getDate() - 6);
            date[1] = new Date(this.currentDate);
            break;
          case 'CurrentMonth':
            const firstDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth(),
              1
            );
            const lastDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth() + 1,
              0
            );
            date[0] = firstDay;
            date[1] = lastDay;
            break;
          case 'Custom':
            date = [
              new Date(this.performanceForm.value.performanceDate?.[0]),
              new Date(this.performanceForm.value.performanceDate?.[1]),
            ];
            break;
        }
        dtype = this.performanceForm.value.performanceDType;
        filterSource = this.performanceForm.value.performanceSource;
        break;
      case 'topPerformance':
        switch (this.topPerformerForm.value.topPerformerRange) {
          case 'TillDate':
            date[0] = null;
            date[1] = null;
            break;
          case 'Today':
            date[0] = new Date(this.currentDate);
            date[1] = new Date(this.currentDate);
            break;
          case 'Yesterday':
            date[0] = new Date(newDate.setDate(newDate.getDate() - 1));
            date[1] = new Date(newDate.setDate(newDate.getDate() - 1));
            break;
          case 'Last7Days':
            date[0] = new Date(newDate.setDate(newDate.getDate() - 6));
            date[1] = new Date(this.currentDate);
            break;
          case 'CurrentMonth':
            const firstDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth(),
              1
            );
            const lastDay = new Date(
              newDate.getFullYear(),
              newDate.getMonth() + 1,
              0
            );
            date[0] = firstDay;
            date[1] = lastDay;
            break;
          case 'Custom':
            if (this.topPerformerForm.value.topPerformerDate) {
              date = [
                new Date(this.topPerformerForm.value.topPerformerDate[0]),
                new Date(this.topPerformerForm.value.topPerformerDate[1]),
              ];
            }
            break;
        }
        dtype = this.topPerformerForm.value.topPerformerDType;
        filterSource = this.topPerformerForm.value.topPerformerSource;
        break;
    }

    const queryParams: any = {
      leadVisibility: visibility,
      firstLevelFilter: firstLevelFilter,
      secondLevelFilter: secondLevelFilter,
      userId: userId,
      date: date[0]
        ? [
          setTimeZoneDate(
            date[0],
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ),
          setTimeZoneDate(
            date[1],
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ),
        ]
        : null,
      dtype: dtype,
      withTeam: this.withTeam ? this.withTeam : withTeam,
      source: filterSource,
      assignTo: isLeadReportNav ? userId : this.DashboardSelectedUsers || null,
      scheduledDateTypeFilter,
      scheduledType,
      doneStatus: doneStatus ? [doneStatus] : null,
      statusDate: statusDate?.[0]
        ? [
          setTimeZoneDate(
            statusDate[0],
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ),
          setTimeZoneDate(
            statusDate[1],
            this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
          ),
        ]
        : null,
      StatusName: [
        'Callback',
        'Meeting Scheduled',
        'Site Visit Scheduled',
      ].includes(secondLevelFilter)
        ? secondLevelFilter
        : null,
      // projects: this.topPerformerForm.value.Projects,
      // properties: this.topPerformerForm.value.Properties,
      // Source: this.topPerformerForm.value.Sources,
      // SubSources: this.topPerformerForm.value.SubSources,
    };
    if (this.isMyTeamDashboard) {
      queryParams.IsOnlyReportees = true;
    }

    this.router.navigate(['leads/manage-leads'], {
      queryParams,
      state: { prevPage: this.router.url },
    });
  }

  scrollToTop() {
    if (this.scrollContainer) {
      this.scrollContainer.nativeElement.scrollTop = 0;
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  scrollToBottom() {
    if (this.scrollContainer) {
      this.scrollContainer.nativeElement.scrollTop =
        this.scrollContainer.nativeElement.scrollHeight;
    } else {
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }
  }

  onTopPerformerChange() {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    if (!this.topPerformerForm.valid) {
      validateAllFormFields(this.topPerformerForm);
      return;
    }
    this.topPerformanceFilter = {
      ...this.topPerformanceFilter,
      UserIds: this.dashboardType !== 0 ? this.DashboardSelectedUsers : [],
      DateType: LeadDateType[this.topPerformerForm.value.topPerformerDType],
      DateRange:
        this.topPerformerForm.value.topPerformerRange !== 'Custom'
          ? DateRange[this.topPerformerForm.value.topPerformerRange]
          : null,
      IsCustomDate: this.topPerformerForm.value.topPerformerRange === 'Custom',
      FromDate:
        this.topPerformerForm.value.topPerformerDate &&
          this.topPerformerForm.value.topPerformerRange === 'Custom'
          ? setTimeZoneDate(
            this.topPerformerForm.value.topPerformerDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.topPerformerForm.value
                .topPerformerRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[0],
            timeZoneOffset
          ),
      ToDate:
        this.topPerformerForm.value.topPerformerDate &&
          this.topPerformerForm.value.topPerformerRange === 'Custom'
          ? setTimeZoneDate(
            this.topPerformerForm.value.topPerformerDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            getDateRange(
              DateRange[
              this.topPerformerForm.value
                .topPerformerRange as keyof typeof DateRange
              ],
              this.currentDate
            )?.[1],
            timeZoneOffset
          ),
      Sources: this.topPerformerForm.value.Sources,
      SubSources: this.topPerformerForm.value.SubSources,
      Projects: this.topPerformerForm.value.Projects,
      Properties: this.topPerformerForm.value.Properties,
      LeadVisibility: this.dashboardType,
      FilterType: this.selectedFilter,
    };
    this.showFilters = !!(
      this.topPerformanceFilter.Projects?.length ||
      this.topPerformanceFilter.Properties?.length ||
      this.topPerformanceFilter.Sources?.length ||
      this.topPerformanceFilter.SubSources?.length
    );
    this.store.dispatch(new FetchTopPerformer(this.topPerformanceFilter));
    this.filters.showTopPerformerFilter = false;
    this.topPerformerForm.value.topPerformerRange != 'Custom' &&
      this.topPerformerForm.get('topPerformerDate').reset();
  }

  onRemoveFilter(key: string, value: any) {
    if (Array.isArray(this.topPerformanceFilter[key])) {
      const indexToRemove = this.topPerformanceFilter[key].indexOf(value);
      if (indexToRemove !== -1) {
        this.topPerformanceFilter = {
          ...this.topPerformanceFilter,
          [key]: [
            ...this.topPerformanceFilter[key].slice(0, indexToRemove),
            ...this.topPerformanceFilter[key].slice(indexToRemove + 1),
          ],
        };
        const formArray = [...(this.topPerformerForm.value[key] || [])];
        formArray.splice(indexToRemove, 1);
        this.topPerformerForm.patchValue({ [key]: formArray });
      }
    } else {
      this.topPerformanceFilter = {
        ...this.topPerformanceFilter,
        [key]: null,
      };
      this.topPerformerForm.patchValue({ [key]: null });
    }
    this.onTopPerformerChange();
  }

  reset() {
    this.topPerformerForm.patchValue({
      Projects: [],
      Properties: [],
      Sources: [],
      SubSources: [],
      topPerformerDType: 'All',
      topPerformerRange: 'TillDate',
      topPerformerDate: [],
    });
    this.topPerformanceFilter = {};
    this.onTopPerformerChange();
  }

  onRadioChange(filterIndex: number): void {
    this.selectedFilter = filterIndex;
    this.updatePayload();
  }

  updatePayload(): void {
    const payload: any = {
      LeadVisibility: this.dashboardType,
      ...this.topPerformanceFilter,
    };
    payload.FilterType = this.selectedFilter;
    this.store.dispatch(new FetchTopPerformer(payload));
  }

  getArrayOfFilters(key: string, values: any) {
    if (
      [
        'IsCustomDate',
        'DateType',
        'DateRange',
        'path',
        'LeadVisibility',
        'FromDate',
        'ToDate',
        'FilterType',
      ].includes(key) ||
      values?.length === 0
    ) {
      return [];
    }
    return values?.toString()?.split(',');
  }

  sortByDate(dataPoints: { label: string; y: number }[]) {
    return dataPoints.sort((a, b) => {
      const dateA = new Date(a.label.split('-').reverse().join('-')).getTime();
      const dateB = new Date(b.label.split('-').reverse().join('-')).getTime();
      return dateA - dateB;
    });
  }

  onDashboardSelection() {
    const dashboardType = DASHBOARD_TYPE.indexOf(this.dashboardVisibility);
    const timeZoneOffset = this.userData?.timeZoneInfo?.baseUTcOffset;

    const today = new Date(this.currentDate);
    today.setHours(0, 0, 0);

    const timeZonedToday = setTimeZoneDate(today, timeZoneOffset);

    if (dashboardType === 2) {
      this.usersList = this.allUsers;
    } else if (dashboardType === 1) {
      this.usersList = this.onlyReportees;
    } else {
      this.userIds = [];
    }

    this.withTeam = this.userIds?.length ? this.withTeam : false;

    const basePayload = {
      LeadVisibility: dashboardType,
      EventDate: timeZonedToday,
      UserIds: dashboardType !== 0 ? this.userIds : [],
      Month: new Date(this.currentDate).getMonth() + 1,
      Year: new Date(this.currentDate).getFullYear(),
      IsWithTeam: dashboardType !== 0 ? this.withTeam : null,
      IsCustomDate: false,
      DateType: 0,
      DateRange: 4,
      Frequency: 2,
      ToDate: timeZonedToday,
    };

    let accountId = this.cplTrackForm.value.cplAccountName;

    if (!accountId && this.accountDetails && this.accountDetails.length > 0) {
      const firstAccount = this.accountDetails[0] as any;
      accountId = firstAccount.accountId;
      this.cplTrackForm.patchValue({
        cplAccountName: accountId
      });
    }

    const cplPayload = {
      ...basePayload,
      DateRange: 0,
      FromDate: timeZonedToday,
      ToDate: timeZonedToday,
      AccountId: accountId,
    };

    this.dispatchDashboardActions(
      basePayload,
      cplPayload,
      dashboardType,
      this.userIds,
      this.withTeam
    );
  }

  dispatchDashboardActions(
    payload: any,
    cplPayload: any,
    dashboardType: number,
    userIds: any[],
    withTeam: boolean
  ) {
    this.store.dispatch(new UpdateDashboardType(dashboardType));
    this.store.dispatch(new UpdateDashboardUsers(userIds));
    this.store.dispatch(new UpdateDashboardWithTeam(withTeam));
    const actions = [
      new FetchDashboardUpcomingEvents(payload),
      new FetchDashboardRemindersCountByDate(payload),
      new FetchDashboardCountByStatus(payload),
      new FetchDashboardLeadSource(payload),
      new UpdateSourceFilterPayload(payload),
      new FetchDashboardReceived(payload),
      new UpdateReceivedFilterPayload(payload),
      new FetchDashboardCallReport(payload),
      new UpdateCallReportFilterPayload(payload),
      new FetchDashboardLeadTracker({ ...payload, DateType: 1 }),
      new UpdateTrackerFilterPayload({ ...payload, DateType: 1 }),
      new FetchDashboardMeeting(payload),
      new UpdateMeetingFilterPayload(payload),
      new FetchDashboardSiteVisit(payload),
      new UpdateSVFilterPayload(payload),
      new FetchDashboardLeadReport(payload),
      new UpdateLeadReportFilterPayload(payload),
      new FetchDashboardPerformance(payload),
      new UpdatePerformanceFilterPayload(payload),

      new FetchProfile(),
    ];
    if (this.accountDetails?.length > 0) {
      actions.push(new FetchCPLTracking(cplPayload));
      actions.push(new UpdateCPLFilterPayload(cplPayload));
    }
    let selectedGoogleAdsAccountId = this.googleAdsCplTrackForm?.value?.googleAdsCplAccountName;
    if (!selectedGoogleAdsAccountId && this.googleAdsAccountDetails?.length > 0) {
      selectedGoogleAdsAccountId = this.googleAdsAccountDetails[0].accountId;
      this.googleAdsCplTrackForm.patchValue({
        googleAdsCplAccountName: selectedGoogleAdsAccountId
      });
    }

    const googleAdsPayload = {
      LeadVisibility: dashboardType,
      UserIds: userIds,
      IsWithTeam: withTeam,
      timeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      FromDate: setTimeZoneDate(getDateRange(DateRange.Today, this.currentDate)?.[0], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()),
      ToDate: setTimeZoneDate(getDateRange(DateRange.Today, this.currentDate)?.[1], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()),
      AccountId: selectedGoogleAdsAccountId,
    };
    actions.push(new FetchGoogleAdsMarketingFinances(googleAdsPayload));
    actions.push(new UpdateGoogleAdsMarketingFilterPayload(googleAdsPayload));

    actions.forEach((action) => this.store.dispatch(action));
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
