<div>
  <nav class="w-100 h-46 bg-dark align-center">
    <div class="w-100 flex-between align-center mx-32">
      <h6 class="fw-300 header-5 text-white tb-ml-20">{{ currentPath === '/invoice' ? 'Invoice' :
        (titles == 'DashboardV3' || titles == 'Dashboard') ? '' : titles }}</h6>
      <div class="align-center">
        <div class="align-center gap-4">
          <!-- <span class="icon ic-notification m-auto ic-xs"></span>
              <span class="icon ic-call-sms m-auto ic-xs"></span> -->
          <h6 *ngIf="userData?.subscriptionDetails?.isAdmin && userData?.subscriptionDetails?.validDays <= 30" (click)="alertSubscription()" class="text-white btn-red px-8 py-6 br-4 cursor-pointer align-center mr-0 fw-semi-bold">Expiring Soon</h6>
          <span [title]="'Information'" *ngIf="this.moduleContent"
            (click)="openInfoModal(currentPath === '/invoice' ? 'Invoice' : titles)"
            class="icon ic-circle-exclamation ic-slate-110 cursor-pointer m-auto ic-sm"></span>
        </div>
        <div *ngIf="this.moduleContent" class="border-left-black-200 ml-10 h-10"></div>
        <div (click)="isOpenUserDetails = !isOpenUserDetails;getAttendanceList()" class="align-center pl-10"
          [title]="(userData?.firstName + ' ' + (userData?.lastName ? userData?.lastName : ''))">
          <img [appImage]="userData?.imageUrl ? s3BucketUrl+userData.imageUrl : ''" [type]="'defaultAvatar'"
            alt="profile pic" width="30px" height="30px" class="br-50 obj-cover cursor-pointer">
          <h5 class="fw-400 max-w-80 text-white ml-10 cursor-pointer text-truncate-1 text-nowrap break-all">
            {{ userData?.firstName ? userData.firstName : '' }}
          </h5>
        </div>
      </div>
    </div>
  </nav>
  <div class="flex-between flex-grow-1">
    <ng-container *ngIf="titles?.includes('Report')">
      <div class="position-absolute top-55 right-13 z-index-1021">
        <button class="btn-coal mr-10" id="btnReportsTracker" data-automate-id="btnReportsTracker"
          (click)="openReportsTracker()" *ngIf="canExportAllUsers || canExportReportees">
          <span class="ic-tracker icon ic-xxs"></span>
          <span class="ml-8 ip-d-none">Export Tracker</span>
        </button>
      </div>
    </ng-container>

  </div>
  <h5 *ngIf="isLocationDisabled"
    class="bg-danger position-absolute w-100 text-center z-index-1021 text-white py-8 pr-100">
    <span class="icon ic-location-solid ic-xs mr-8"></span>Location permission is mandatory to use
    <span class="fw-600">“Clock IN / OUT”</span> feature.
  </h5>
  <h5
    *ngIf="geoFencingData && isGeoFenceEnabled && userData?.isGeoFenceActive && !isInGeoFence && !isCheckingGeoFence && showGeoFenceBanner"
    class="bg-warning position-absolute w-100 text-center z-index-1021 text-dark py-8 pr-100">
    <span class="icon ic-location-solid ic-xs mr-8"></span>You must be within the geo-fence radius of all assigned
    properties and projects.
    <span class="fw-600">"Clock IN / OUT"</span> is disabled.
  </h5>
  <h5 *ngIf="isGeoFenceEnabled && userData?.isGeoFenceActive && isAccuracyLow && showAccuracyBanner"
    class="bg-danger position-absolute w-100 text-center z-index-1021 text-white py-8 pr-100">
    <span class="icon ic-location-solid ic-xs mr-8"></span>Location accuracy is poor ({{previousAccuracy |
    number:'1.0-0'}}m).
    <span class="fw-600">"Clock IN / OUT"</span> is disabled until accuracy improves.
  </h5>
  <h5 *ngIf="isBannerEnabled || isShiftOver"
    class="bg-light-yellow-100 position-absolute w-100 text-center z-index-1021 text-black-100 py-8 pr-100">
    <span class="icon bg-dark-yellow-100 ic-circle-exclamation ic-xs br-10 mr-8"></span>
    {{ isBannerEnabled ? 'Your shift will end in ' + balanceTime +
    ' minutes. Please ensure all data is saved before the end of your shift.'
    : 'Your shift has ended. CRM will now automatically log out.' }}
  </h5>
</div>
<div class="w-250 br-4 position-absolute right-20 border-gray z-index-1021" *ngIf="isOpenUserDetails">
  <div class="bg-coal p-12 brtl-4 brtr-4 align-center cursor-pointer on-hover"
    [routerLink]="'teams/user-details/' + userId" id="clkEditProfile" (click)="isOpenUserDetails = false">
    <div>
      <img [appImage]="userData?.imageUrl ? s3BucketUrl+userData.imageUrl : ''" [type]="'defaultAvatar'"
        alt="profile pic" width="71" height="70" class="br-50 obj-cover"
        [title]="(userData?.firstName + ' ' + (userData?.lastName ? userData?.lastName : ''))">
    </div>
    <div class="text-gray-110 fw-600 ml-16">
      <h5 class="text-capitalize text-truncate-2 text-white mb-6 text-wrap break-all">{{userData?.firstName}}
        {{userData?.lastName}}</h5>
      <div class="align-center my-2">
        <span class="icon ic-person-secondary ic-slate-90 ic-xxxs mr-6"></span>
        <div class="text-sm text-truncate-1 break-all">{{userData.userName}}</div>
      </div>
      <a [href]="'tel:'+userData.phoneNumber" [ngClass]="{'pe-none' : !userData.phoneNumber}" class="align-center my-2">
        <span class="icon ic-call-ring ic-slate-90 ic-xxxs mr-6"></span>
        <div class="text-sm">{{userData.phoneNumber}}</div>
      </a>
      <a [href]="'mailto:'+userData.email" [ngClass]="{'pe-none' : !userData.email}" class="align-center my-2">
        <span class="icon ic-mail ic-slate-90 ic-xxxs mr-6"></span>
        <div class="text-sm text-truncate-1 break-all">{{userData?.email}}</div>
      </a>
      <div class="align-center my-2" *ngIf="userData?.timeZoneInfo?.timeZoneName && userData?.shouldShowTimeZone">
        <span class="icon ic-clock-list ic-slate-90 ic-xxxs mr-6"></span>
        <div class="text-sm text-truncate-1 break-all" [title]="getTimeZoneTooltip()">
          {{userData?.timeZoneInfo?.timeZoneDisplay}}</div>
      </div>
    </div>
    <div class="position-absolute top-6 right-6 on-hover-show"><span class="icon ic-pen ic-accent-green ic-xxs"></span>
    </div>
  </div>
  <div class="bg-white p-16 brbr-4 brbl-4">
    <div *ngIf="!isClockIn" (click)="clockIn()" class="bg-green-900 w-100 py-12 text-center text-white fw-600 br-20"
      [ngClass]="{'cursor-pointer': (!geoFencingData || !isGeoFenceEnabled || !userData?.isGeoFenceActive || (isInGeoFence && !isAccuracyLow)), 'opacity-50 pe-none': (geoFencingData && isGeoFenceEnabled && userData?.isGeoFenceActive && (!isInGeoFence || isAccuracyLow) && !isCheckingGeoFence)}">
      Clock IN
    </div>
    <div *ngIf="isClockIn" class="align-center">
      <div (click)="clockOut()" class="bg-red-350 w-100 py-12 text-center text-white fw-600 br-20"
        [ngClass]="{'cursor-pointer': (!geoFencingData || !isGeoFenceEnabled || !userData?.isGeoFenceActive || (isInGeoFence && !isAccuracyLow)), 'opacity-50 pe-none': (geoFencingData && isGeoFenceEnabled && userData?.isGeoFenceActive && (!isInGeoFence || isAccuracyLow) && !isCheckingGeoFence)}">
        Clock OUT
      </div>
      <!-- <div class="text-nowrap">
        <div class="fw-600">{{ hours }}h: {{ minutes }}m: {{ seconds }}s</div>
        <div class="text-xs text-dark-gray text-decoration-underline">since last login</div>
      </div> -->
    </div>
    <div class="text-nowrap flex-center-col mt-8">
      <div class="text-xs text-dark-gray">Current Date & Time</div>
      <div class="fw-600">{{ getTimeZoneDate(presentTime, userData?.timeZoneInfo?.baseUTcOffset, 'dateWithFullTime')}}
      </div>
    </div>
    <div class="border-bottom my-16"></div>
    <div class="flex-center cursor-pointer text-slate text-red-hover fw-600" (click)="signOut()">
      <span class="icon ic-logout ic-pale ic-xxxs pr-12"></span>
      <div>{{ 'AUTH.log-out' | translate }}</div>
    </div>
  </div>
</div>
<ng-template #notification>
  <div>
    <div class="p-16 bg-coal flex-between">
      <div class="text-white text-large">{{'GLOBAL.notifications' | translate }}</div>
      <div class="icon ic-close-secondary ic-large cursor-pointer "></div>
    </div>
    <div class="mx-10 my-6 border br-20 bg-white align-center user w-220">
      <div class="activation" [ngClass]="{'active' : selectedSection == 'All'}" (click)="selectedSection = 'All'">
        {{'GLOBAL.all' | translate }}
      </div>
      <div class="activation" [ngClass]="{'active' : selectedSection == 'Leads'}" (click)="selectedSection = 'Leads'">
        {{'GLOBAL.leads' | translate }}
      </div>
      <div class="activation" [ngClass]="{'active' : selectedSection == 'Events'}" (click)="selectedSection = 'Events'">
        {{'DASHBOARD.events' | translate }}
      </div>
    </div>
    <div class="scrollbar h-100-108">
      <div class="my-6 mx-10 fw-600 text-sm text-coal">Today</div>
      <div class="border-bottom"></div>
      <div class="w-100">
        <div class="mx-10 mt-10 d-flex">
          <div class="dot dot-lg-xxl bg-black-100 mr-10">
            <div class="icon  ic-xxs ic-secondary-filter-solid"></div>
          </div>
          <div>
            <div class="flex-between">
              <div class="fw-semi-bold text-sm text-black-100">New lead assigned</div>
              <div class="dot dot-xxs bg-accent-green p-2"></div>
            </div>
            <div class="mt-4 text-xs text-black-200">Hello!!😄👋 A new lead has been assigned to you! Check it out!
            </div>
            <div class="fw-semi-bold text-xs flex-end text-dark-800">2 min ago</div>
          </div>
        </div>
        <div class="border-bottom mt-16"></div>
      </div>
      <div>
        <div class="mx-10 mt-10 d-flex">
          <div class="dot dot-lg-xxl bg-black-100 mr-10">
            <div class="icon  ic-xxs ic-folder-solid"></div>
          </div>
          <div class="w-100">
            <div class="flex-between">
              <div class="fw-semi-bold text-sm text-black-100">Documents successfully uploaded</div>
              <div class="dot dot-xxs bg-accent-green p-2"></div>
            </div>
            <div class="text-black-200">
              <div class="d-flex mt-4 text-xs">
                <span class="text-decoration-underline fw-600 text-accent-green"> 4 Documents </span>
                <div class="ml-4"> have been uploaded against the</div>
              </div>
              <div class="d-flex mt-4 text-xs"><span>lead</span>:<span class="fw-600 ml-4">pampana.manasa</span>
              </div>
            </div>
            <div class="fw-semi-bold text-xs flex-end text-dark-800">2 min ago</div>
          </div>
        </div>
        <div class="border-bottom mt-16"></div>
      </div>
      <div>
        <div class="mx-10 mt-10 d-flex">
          <div class="dot dot-lg-xxl bg-slate-250 mr-10">
            <div class="icon  ic-xxs ic-person-walking"></div>
          </div>
          <div class="w-100">
            <div class="flex-between">
              <div class="fw-semi-bold text-sm text-dark-gray">Site Visit Scheduled</div>
              <div class="dot dot-xxs bg-accent-green p-2"></div>
            </div>
            <div class="mt-4 text-xs text-dark-gray">
              <div>Oh great!! A site-visit has been scheduled with <span class="fw-700">SK Nowlak </span> at
                <span class="fw-700">5PM</span> on <span class="fw-700">15th July, 2025</span>.
              </div>
              <div> Check it out!</div>
            </div>
            <div class="fw-semi-bold text-xs flex-end text-dark-800">2 min ago</div>
          </div>
        </div>
        <div class="border-bottom mt-16"></div>
      </div>
      <div class="w-100">
        <div class="mx-10 mt-10 d-flex">
          <div class="dot dot-lg-xxl bg-black-100 mr-10">
            <div class="icon  ic-xxs ic-calendar-solid"></div>
          </div>
          <div>
            <div class="flex-between">
              <div class="fw-semi-bold text-sm text-black-100">Meeting Scheduled</div>
              <div class="dot dot-xxs bg-accent-green p-2"></div>
            </div>
            <div class="mt-4 text-xs text-black-200">
              <div>Wohoo!! Meeting has been scheduled with
                <span class="fw-700">Ravi </span> at
                <span class="fw-700">12:30PM</span> on <span class="fw-700">12th July, 2025</span>.
              </div>
              <div> Check it out!</div>
            </div>
            <div class="fw-semi-bold text-xs flex-end text-dark-800">2 min ago</div>
          </div>
        </div>
        <div class="border-bottom mt-16"></div>
      </div>
    </div>
  </div>
</ng-template>