<div class="scrollbar h-100-200 mb-60 ph-mb-80 card-white">
    <ng-container>
        <ul class="time-line has-left-content time-line-dashed pb-0 ml-80"
            *ngIf="filteredHistoryList?.length else noHistory">
            <ng-container *ngFor="let item of filteredHistoryList">
                <li class="dot-orange">
                    <p class="left-content text-xs ph-mt-10">{{item.date | relativeDay}}</p>
                    <ng-container *ngFor="let data of item.data">
                        <ng-container *ngFor="let indHistory of data">
                            <div *ngIf="indHistory?.[1]?.length" class="dot-gray"></div>
                            <div *ngIf="indHistory[1].length">
                                <div class="justify-between my-10">
                                    <p>{{ 'GLOBAL.done-by' | translate }}
                                        <a class="fw-600"> {{ indHistory[1][0]?.modifiedBy }}</a>
                                    </p>
                                    <span class="text-gray fw-600 text-nowrap text-xs mx-20">
                                        {{ getTimeZoneDate(indHistory[1][0]?.modifiedOn,
                                        userData?.timeZoneInfo?.baseUTcOffset, 'timeWithMeridiem')}}
                                    </span>
                                </div>
                                <ng-container *ngFor="let history of indHistory?.[1]; let ind = index">
                                    <div class="w-100 text-xs text-gray gap-1">
                                        <ng-container [ngSwitch]="history?.fieldName">
                                            <ng-container *ngSwitchCase="'Is Qualified'">
                                                <div class="d-flex"
                                                    *ngIf="history?.oldValue === 'True' || history?.oldValue === 'False'">
                                                    <p class="white-card fw-700">
                                                        {{ (history?.newValue === 'True' ? 'It is qualified' :
                                                        'It is not qualified')}}
                                                    </p>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Is Converted To Lead'">
                                                <div class="d-flex"
                                                    *ngIf="history?.oldValue === 'True' || history?.oldValue === 'False'">
                                                    <p class="white-card fw-700">
                                                        {{ (history?.newValue === 'True' ? 'It is converted' :
                                                        'It is not converted')}}
                                                    </p>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Is Archived'">
                                                <div class="d-flex"
                                                    *ngIf="history?.oldValue === 'True' || history?.oldValue === 'False'">
                                                    <p class="white-card fw-700">
                                                        {{ (history?.newValue === 'True' ? 'Data was Deleted' :
                                                        'Data was Restored')}}
                                                    </p>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Scheduled Date'">
                                                <div *ngIf="history?.newValue" class="align-center gap-1">
                                                    <span class="gray-dot"></span>
                                                    {{ history?.fieldName }} : <span
                                                        class="fw-600">{{getTimeZoneDate(history?.newValue,
                                                        userData?.timeZoneInfo?.baseUTcOffset, 'dateWithTime')}} </span>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Assign To'">
                                                <div class="align-center gap-1">
                                                    <div class="gray-dot"></div>
                                                    Assigned To User {{history?.oldValue ? ' updated ' : ' added '}}
                                                    <span
                                                        class="fw-600 align-center gap-1 mr-10 word-break line-break">{{history?.oldValue
                                                        || ''}}
                                                        <h3>
                                                            &#x2192;</h3>
                                                        {{ history?.newValue ? history?.newValue : 'unassigned' }}
                                                    </span>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Data Communication'">
                                                <div class="white-card gap-1" *ngIf="jsonFormat(history?.newValue)">
                                                    <p class="fw-700 mb-10">{{ history?.fieldName }} Updated</p>
                                                    <div *ngFor="let field of jsonFormat(history?.newValue)">
                                                        <span *ngIf="field[1]">
                                                            {{field[0]}} -
                                                            <div class="fw-700 text-sm break-word"
                                                                [innerHTML]="retrieveMessageFromBackend(field[1])">
                                                            </div>
                                                        </span>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Possesion Date'">
                                                <div class="align-center gap-1">
                                                    <div class="gray-dot"></div>
                                                    {{ 'Possession Needed By' + (history?.oldValue ? ' updated ' : '
                                                    added')
                                                    }}
                                                    <span class="fw-600 align-center gap-1 mr-10 word-break line-break">
                                                        {{ history?.oldValue ?
                                                        getTimeZoneDate(history?.oldValue,
                                                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') : ''}}
                                                        <h3>&#x2192;</h3>{{
                                                        history?.newValue ?
                                                        getTimeZoneDate(history?.newValue,
                                                        userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') : ''}}
                                                    </span>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'DataCallLog'">
                                                <ng-container
                                                    *ngIf="DataCallFormate(history?.newValue) else contactRecord">
                                                    <ng-container
                                                        *ngIf="DataCallFormate(history?.newValue) as callData">
                                                        <div class="white-card gap-1">
                                                            <p class="fw-700 mb-10">{{ 'Call Recordings'}}</p>
                                                            <div class="flex flex-col">
                                                                <div class="flex items-center gap-1">
                                                                    <span class="fw-600">{{callData.callType}}</span>
                                                                    <span class="mx-1">→</span>
                                                                    <span class="fw-600">{{callData.status}}</span>
                                                                    <span class="mx-1">→</span>
                                                                    <span class="fw-600">{{callData.duration}}</span>
                                                                </div>
                                                                <div *ngIf="callData.url">
                                                                    <audio preload="auto" #audioPlayer controls
                                                                        (play)="pauseOtherAudio(audioPlayer)"
                                                                        (canplay)="isLoading = false"
                                                                        (loadedmetadata)="isLoading = false">
                                                                        <source
                                                                            [src]='callData.url ? decodeAudioUrl(callData.url) : null'
                                                                            type="audio/mp3">
                                                                    </audio>
                                                                    <div *ngIf="isLoading">
                                                                        <ng-lottie [options]="loader" width="30px"
                                                                            height="30px"
                                                                            class="position-absolute top-10 left-6">
                                                                        </ng-lottie>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                                <ng-template #contactRecord>
                                                    <p class="white-card">
                                                        {{'LEADS.contacted-through' | translate}}
                                                        <span class="fw-700 word-break line-break max-w-100px">
                                                            {{ history?.newValue }}</span>
                                                    </p>
                                                </ng-template>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Notes'">
                                                <div class="position-relative" *ngIf="history?.newValue">
                                                    <div>
                                                        <p class="white-card gap-1">
                                                            <span class="fw-700">{{ history?.fieldName }}: </span>
                                                            <span class="word-break line-break"
                                                                [innerHTML]="convertUrlsToLinks(history?.newValue)"></span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Status'">
                                                <div class="align-center gap-1">
                                                    <div class="gray-dot"></div>
                                                    <div class="fw-600 align-center gap-1 mr-10 word-break line-break">
                                                        <span class="text-sm fw-400"> {{ history?.fieldName +
                                                            (history?.oldValue ? ' updated ' : ' added ') }}</span>
                                                        {{history?.oldValue || ''}} <h3>&#x2192;</h3>
                                                        {{ history?.newValue }}
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'BHKs'">
                                                <div class="align-center gap-1">
                                                    <div class="gray-dot"></div>
                                                    <div class="fw-600 align-center gap-1 mr-10 word-break line-break">
                                                        <ng-container
                                                            *ngIf="(history?.oldValue && !history?.newValue) else updatedData">
                                                            <span class="text-sm fw-400 my-4">{{history?.fieldName}}
                                                                <span
                                                                    class="fw-600 word-break text-truncate-2 text-wrap">({{getBHKDisplay(history?.oldValue)}})</span>has
                                                                been removed</span>
                                                        </ng-container>
                                                        <ng-template #updatedData>
                                                            <span class="text-sm fw-400 align-center"> {{
                                                                history?.fieldName + (history?.oldValue ?
                                                                ' updated ' : ' added ') }}</span>
                                                            {{getBHKDisplay(history?.oldValue)}} <h3>&#x2192;</h3>
                                                            {{getBHKDisplay(history?.newValue) }}</ng-template>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchCase="'Beds'">
                                                <div class="align-center gap-1">
                                                    <div class="gray-dot"></div>
                                                    <div class="fw-600 align-center gap-1 mr-10 word-break line-break">
                                                        <ng-container
                                                            *ngIf="(history?.oldValue && !history?.newValue) else updatedData">
                                                            <span class="text-sm fw-400 my-4">{{history?.fieldName}}
                                                                <span
                                                                    class="fw-600 word-break text-truncate-2 text-wrap">({{getBedsDisplay(history?.oldValue)}})</span>
                                                                has been removed</span>
                                                        </ng-container>
                                                        <ng-template #updatedData>
                                                            <span class="text-sm fw-400 align-center"> {{
                                                                history?.fieldName + (history?.oldValue ? ' updated ' :
                                                                ' added ') }}</span>
                                                            {{getBedsDisplay(history?.oldValue) || ''}} <h3>&#x2192;
                                                            </h3>
                                                            {{ getBedsDisplay(history?.newValue) }}</ng-template>
                                                    </div>
                                                </div>
                                            </ng-container>
                                            <ng-container *ngSwitchDefault>
                                                <ng-container [ngSwitch]="history?.fieldName">
                                                    <ng-container *ngSwitchCase="'Data Got Added'">
                                                        <p class="bg-secondary p-10 br-5 max-w-390 fw-700">
                                                            {{ history?.fieldName }} <span class="header-3">🥳</span>
                                                        </p>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'BHKType'">
                                                        <ng-container *ngIf="history?.newValue !== ''">
                                                            <div class="align-center gap-1">
                                                                <div class="gray-dot"></div>
                                                                <div class="align-center gap-1">
                                                                    {{ history?.fieldName + (history?.oldValue ? '
                                                                    updated ' : ' added ') }}
                                                                    <span class="fw-600">{{
                                                                        history?.oldValue || '' }}</span>
                                                                    <h3>&#x2192;</h3>
                                                                    <span class="fw-600">{{
                                                                        history?.newValue }}</span>
                                                                </div>
                                                            </div>

                                                        </ng-container>
                                                    </ng-container>
                                                    <ng-container *ngSwitchCase="'Date Of Birth'">
                                                        <div
                                                            class="fw-600 align-center gap-1 mr-10 break-word line-break">
                                                            <div class="gray-dot"></div>
                                                            <span class="text-sm fw-400">{{history?.fieldName +
                                                                (history?.oldValue ? '
                                                                updated ' : ' added ')}}</span>
                                                            <span>{{history?.oldValue ? getTimeZoneDate(
                                                                history?.oldValue,'00:00:00',
                                                                'dayMonthYear'): ''}}</span>
                                                            <h3 class="mb-2">&#x2192;</h3>
                                                            <span>{{history?.newValue ? getTimeZoneDate(
                                                                history?.newValue,'00:00:00',
                                                                'dayMonthYear'): ''}}</span>
                                                        </div>
                                                    </ng-container>
                                                    <ng-container *ngSwitchDefault>
                                                        <ng-container
                                                            *ngIf="history.fieldName && history.fieldName !== 'Archived On'">
                                                            <div class="align-center gap-1">
                                                                <ng-container
                                                                    *ngIf="(history?.oldValue && !history?.newValue) else default">
                                                                    <div class="gray-dot"></div>
                                                                    <span class="text-sm fw-400 my-4"
                                                                        *ngIf="history?.fieldName !== 'Scheduled Date'">
                                                                        {{
                                                                        history?.fieldName }}
                                                                        <span
                                                                            class="fw-600 word-break text-truncate-2 text-wrap">({{history?.oldValue?.trim()}})</span>
                                                                        has been removed
                                                                    </span>
                                                                </ng-container>
                                                                <ng-template #default>
                                                                    <div class="gray-dot"></div>
                                                                    <div class="align-center gap-1">
                                                                        {{ history?.fieldName + (history?.oldValue ? '
                                                                        updated ' : ' added ') }}
                                                                        <span class="fw-600 break-all">{{
                                                                            isValidDate(history?.oldValue) &&
                                                                            history?.oldValue?.endsWith('Z')
                                                                            ? getTimeZoneDate(history?.oldValue,
                                                                            userData?.timeZoneInfo?.baseUTcOffset)
                                                                            : history?.oldValue || '' }}</span>

                                                                        <h3>&#x2192;</h3>
                                                                        <span class="fw-600 break-all">{{
                                                                            isValidDate(history?.newValue) &&
                                                                            history?.newValue?.endsWith('Z')
                                                                            ? getTimeZoneDate(history?.newValue,
                                                                            userData?.timeZoneInfo?.baseUTcOffset)
                                                                            : history?.newValue }}</span>
                                                                    </div>
                                                                </ng-template>
                                                            </div>
                                                        </ng-container>
                                                    </ng-container>
                                                </ng-container>
                                            </ng-container>
                                        </ng-container>
                                    </div>
                                </ng-container>
                            </div>
                        </ng-container>
                    </ng-container>
                </li>
            </ng-container>
        </ul>
    </ng-container>
</div>

<ng-template #noHistory>
    <div class="h-100-393 flex-center-col">
        <ng-lottie [options]='noDocument' width="200px" height="200px"></ng-lottie>
        <div class="fw-600 header-4 text-light-slate">{{ 'DATA.no-history' | translate }}</div>
    </div>
</ng-template>