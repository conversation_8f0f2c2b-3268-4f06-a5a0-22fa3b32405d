{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/reports/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createEffect, ofType } from '@ngrx/effects';\nimport { select } from '@ngrx/store';\nimport { EMPTY, of, throwError } from 'rxjs';\nimport { catchError, map, mergeMap, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';\nimport { CloseModal, OnError } from 'src/app/app.actions';\nimport { EMPTY_GUID } from 'src/app/app.constants';\nimport { QRConfirmationComponent } from 'src/app/features/no-auth/lead-qr-form/qr-confirmation/qr-confirmation.component';\nimport { AddLeadProjectsSuccess, AddLeadSuccess, AddQRLeadSuccess, BulkAgencySuccess, BulkCampaignSuccess, BulkChannelPartnerSuccess, BulkProjectsSuccess, BulkReassignLeadSuccess, BulkSourceSuccess, ClearCardData, CommunicationBulkCountSuccess, CommunicationBulkMessageSuccess, CommunicationCountSuccess, CommunicationMessageSuccess, DeleteLeadsSuccess, EmptyEffect, ExcelUploadSuccess, ExportLeadsSuccess, FetchActiveCount, FetchAdditionalPropertyListSuccess, FetchAdditionalPropertyValueSuccess, FetchAgencyNameListAnonymousSuccess, FetchAgencyNameListSuccess, FetchAllParentLeadByIdSuccess, FetchBulkOperationSuccess, FetchCampaignListAnonymousSuccess, FetchCampaignListSuccess, FetchChannelPartnerListAnonymousSuccess, FetchChannelPartnerListSuccess, FetchCountryBasedCitySuccess, FetchDuplicateFeatureSuccess, FetchExcelUploadedSuccess, FetchExportStatusSuccess, FetchLeadActiveCountSuccess, FetchLeadAltCountryCodeSuccess, FetchLeadAppointmentsByProjectsSuccess, FetchLeadBaseFilterCount, FetchLeadBaseFilterCountSuccess, FetchLeadById, FetchLeadByIdWithArchiveSuccess, FetchLeadCitiesSuccess, FetchLeadClusterNameSuccess, FetchLeadCommunitiesSuccess, FetchLeadCountriesSuccess, FetchLeadCountryCodeSuccess, FetchLeadCurrencySuccess, FetchLeadCustomTopFilters, FetchLeadCustomTopFiltersChildrenSuccess, FetchLeadCustomTopFiltersSuccess, FetchLeadDroppedCountSuccess, FetchLeadExportSuccess, FetchLeadFlagsCountSuccess, FetchLeadHistoryListSuccess, FetchLeadIdSuccess, FetchLeadLandLineSuccess, FetchLeadList, FetchLeadListSuccess, FetchLeadListV2, FetchLeadLocalitesSuccess, FetchLeadNationalitySuccess, FetchLeadNotInterestedCountSuccess, FetchLeadNotesListSuccess, FetchLeadPostalCodeSuccess, FetchLeadRotation, FetchLeadRotationSuccess, FetchLeadStatesSuccess, FetchLeadStatusCount, FetchLeadStatusCountSuccess, FetchLeadSubCommunitiesSuccess, FetchLeadTowerNamesSuccess, FetchLeadUnitNameSuccess, FetchLeadZonesSuccess, FetchLeadsCommunicationByIds, FetchLeadsCommunicationByIdsSuccess, FetchLocationsSuccess, FetchMatchingPropertyOrProjectListSuccess, FetchMigrateExcelUploadedSuccess, FetchModuleWiseSearchPropertiesSuccess, FetchProjectListSuccess, FetchPropertyListSuccess, FetchQRProjectListSuccess, FetchQRPropertyListSuccess, FetchSubSourceList, FetchSubSourceListSuccess, FetchUploadTypeNameListSuccess, HasLeadAltInfoSuccess, HasLeadInfoSuccess, LeadActionTypes, LeadExcelUploadSuccess, MeetingOrVisitDoneSuccess, NavigateToLinkSuccess, PermanentDeleteLeadsSuccess, ReassignBothSuccess, ReassignLeadSuccess, RestoreLeadsSuccess, SecondaryAssignLeadSuccess, UpdateCardData, UpdateFilterPayload, UpdateInvoiceFilter, UpdateIsLoadMore, UpdateLeadNotesSuccess, UpdateLeadStatusSuccess, UpdateLeadSuccess, UpdateMultipleLeadStatus, UpdateMultipleLeadStatusSuccess, UploadLeadDocumentSuccess, updateDuplicateAssignSuccess } from 'src/app/reducers/lead/lead.actions';\nimport { getFiltersPayload, getInvoiceFiltersPayload, getIsLeadCustomStatusEnabled, getIsLoadMore } from 'src/app/reducers/lead/lead.reducer';\nimport { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';\nimport { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ngrx/effects\";\nimport * as i2 from \"src/app/services/controllers/leads.service\";\nimport * as i3 from \"src/app/services/controllers/leads-rotation.service\";\nimport * as i4 from \"angular2-notifications\";\nimport * as i5 from \"@ngrx/store\";\nimport * as i6 from \"src/app/services/shared/common.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"ngx-bootstrap/modal\";\nexport class LeadEffects {\n  constructor(actions$, api, leadRotationApi, _notificationService, _store, commonService, router, modalService, modalRef) {\n    var _this = this;\n\n    this.actions$ = actions$;\n    this.api = api;\n    this.leadRotationApi = leadRotationApi;\n    this._notificationService = _notificationService;\n    this._store = _store;\n    this.commonService = commonService;\n    this.router = router;\n    this.modalService = modalService;\n    this.modalRef = modalRef;\n    this.getLeadList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_LIST), switchMap(() => this._store.select(window.location.pathname.includes('/invoice') ? getInvoiceFiltersPayload : getFiltersPayload).pipe(take(1), map(filterPayload => window.location.pathname.includes('/invoice') ? new UpdateInvoiceFilter(filterPayload) : new UpdateFilterPayload(filterPayload)) // Properly return the action\n    ))));\n    this.addLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.ADD_LEAD), map(action => action.payload), switchMap(data => {\n      return this.api.add(data).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead added Successfully');\n\n          this._store.dispatch(new AddLeadSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.addQRLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.ADD_QR_LEAD), switchMap(action => {\n      return this.api.addQRLead(action.payload, action.templateId).pipe(map(resp => {\n        if (resp.succeeded) {\n          // this._notificationService.success('Lead added Successfully');\n          let initialState = {\n            class: 'modal-450 modal-dialog-centered ph-modal-unset',\n            ignoreBackdropClick: false\n          };\n          this.modalRef = this.modalService.show(QRConfirmationComponent, initialState);\n          return new AddQRLeadSuccess();\n        }\n\n        return new AddQRLeadSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.addBulkLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.EXCEL_UPLOAD), switchMap(action => {\n      return this.api.uploadExcel(action.file).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Excel uploaded Successfully');\n\n          new FetchLeadList();\n          return new ExcelUploadSuccess(resp.data);\n        } else {\n          this._store.dispatch(new CloseModal());\n\n          this._notificationService.warn(`${resp.message}`);\n\n          new FetchLeadList();\n          return new ExcelUploadSuccess(resp.data);\n        }\n      }), catchError(err => {\n        var _a, _b;\n\n        this._notificationService.error((_b = (_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.Message) === null || _b === void 0 ? void 0 : _b.split(',')[0]);\n\n        return of(new OnError(err));\n      }));\n    })));\n    this.updateLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEAD), switchMap(action => {\n      return this.api.update(action.payload, action.id).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Updated Successfully');\n\n          this._store.dispatch(new UpdateLeadSuccess(resp.succeeded));\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.updateLeadNotes$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEAD_NOTES), switchMap(action => {\n      return this.api.updateNotes(action.id, action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Notes Updated Successfully');\n\n          if (action.canFetchLeadList) {\n            // this._store.dispatch(new FetchLeadList());\n            this._store.dispatch(new LeadPreviewChanged());\n          }\n\n          const userDetails = localStorage.getItem('userDetails');\n          const currentUserName = userDetails ? JSON.parse(userDetails).given_name + ' ' + JSON.parse(userDetails).family_name || JSON.parse(userDetails).preferred_username || \"Current User\" : \"Current User\";\n          const newNote = {\n            fieldName: \"Notes\",\n            filterKey: 2,\n            oldValue: \"\",\n            newValue: action.payload.notes,\n            updatedBy: currentUserName,\n            updatedOn: new Date().toISOString(),\n            auditActionType: \"Updated\"\n          };\n          return new UpdateLeadNotesSuccess(newNote);\n        }\n\n        return new FetchLeadNotesListSuccess([]);\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadNotes$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_NOTES_LIST), map(action => action), switchMap(data => this.api.getNotesList(data.id).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchLeadNotesListSuccess(resp.data);\n      }\n\n      return new FetchLeadNotesListSuccess([]);\n    }), catchError(err => of(new OnError(err)))))));\n    this.updateMultipleLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_MULTIPLE_LEAD), switchMap(action => {\n      if (action.isUpdateStatus) this._store.dispatch(new UpdateMultipleLeadStatus());\n      return this.api.updateBulkLeadStatus(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Leads updated Successfully');\n\n          if (action.isUpdateStatus) {\n            this._store.dispatch(new UpdateMultipleLeadStatusSuccess());\n          }\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadById$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_BY_ID), map(action => action.leadId), switchMap(data => this.api.get(data).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchLeadIdSuccess(resp.data);\n      }\n\n      return new FetchLeadIdSuccess();\n    }), catchError(err => of(new OnError(err)))))));\n    this.updateStatus$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEAD_STATUS), switchMap(action => {\n      return this.api.updateLeadStatus(action.payload, action.leadId).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Status Updated Successfully');\n\n          this._store.dispatch(new UpdateLeadStatusSuccess());\n\n          this._store.dispatch(new FetchLeadById(action.leadId));\n\n          return action.canFetchLeadList ? new FetchLeadList() : new EmptyEffect();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.updateLeadShareCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEAD_SHARE_COUNT), switchMap(action => {\n      return this.api.increaseShareCount(action.leadId).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Successfully Shared');\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.updateLeadsTagInfo$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEADS_TAG_INFO), mergeMap(action => {\n      return this.api.updateLeadTags(action.payload, action.id).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Lead Successfully ${resp.message}`);\n\n          return action.canFetchLeadList ? new FetchLeadList() : new EmptyEffect();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.emptyEffect$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.EMPTY_EFFECT), switchMap(action => {\n      return [];\n    })));\n    this.reassignLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.REASSIGN_LEAD), switchMap(action => {\n      return this.api.reassignLead(action.payload).pipe(map(resp => {\n        var _a, _b, _c, _d, _e;\n\n        let msg = 'Lead' + (((_a = action.payload.leadIds) === null || _a === void 0 ? void 0 : _a.length) > 1 ? 's' : '') + ' Successfully ';\n\n        this._store.select(getFiltersPayload).subscribe(data => {\n          var _a, _b, _c, _d;\n\n          msg += (data === null || data === void 0 ? void 0 : data.leadVisibility) === 3 ? 'Assigned' : ((_b = (_a = action.payload) === null || _a === void 0 ? void 0 : _a.userIds) === null || _b === void 0 ? void 0 : _b.length) && !((_d = (_c = action.payload) === null || _c === void 0 ? void 0 : _c.userIds) === null || _d === void 0 ? void 0 : _d.includes(EMPTY_GUID)) ? 'Reassigned' : 'Unassigned';\n        });\n\n        if (resp.succeeded) {\n          if (((_c = (_b = action === null || action === void 0 ? void 0 : action.payload) === null || _b === void 0 ? void 0 : _b.leadIds) === null || _c === void 0 ? void 0 : _c.length) === 1) {\n            this._store.dispatch(new FetchLeadById((_e = (_d = action === null || action === void 0 ? void 0 : action.payload) === null || _d === void 0 ? void 0 : _d.leadIds) === null || _e === void 0 ? void 0 : _e[0]));\n          }\n\n          if (action.canFetchLeadList) this._store.dispatch(new FetchLeadList());\n\n          this._notificationService.success(msg);\n\n          return new ReassignLeadSuccess(resp);\n        }\n\n        return new ReassignLeadSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.reassignBoth$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.REASSIGN_BOTH), switchMap(action => {\n      return this.api.reassignBoth(action.payload).pipe(map(resp => {\n        var _a, _b, _c, _d, _e;\n\n        let msg = 'Lead' + (((_a = action.payload.leadIds) === null || _a === void 0 ? void 0 : _a.length) > 1 ? 's' : '') + ' Assignment Updated Successfully ';\n\n        if (resp.succeeded) {\n          if (((_c = (_b = action === null || action === void 0 ? void 0 : action.payload) === null || _b === void 0 ? void 0 : _b.leadIds) === null || _c === void 0 ? void 0 : _c.length) === 1) {\n            this._store.dispatch(new FetchLeadById((_e = (_d = action === null || action === void 0 ? void 0 : action.payload) === null || _d === void 0 ? void 0 : _d.leadIds) === null || _e === void 0 ? void 0 : _e[0]));\n          }\n\n          if (action.canFetchLeadList) this._store.dispatch(new FetchLeadList());\n\n          this._notificationService.success(msg);\n\n          return new ReassignBothSuccess(resp);\n        }\n\n        return new ReassignBothSuccess();\n      }), catchError(err => {\n        var _a, _b;\n\n        throwError(err);\n        Array.isArray((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.messages) ? this._notificationService.error(err.error.messages[0]) : this._notificationService.error((_b = err === null || err === void 0 ? void 0 : err.error) === null || _b === void 0 ? void 0 : _b.messages);\n        return throwError(() => err);\n      }));\n    })));\n    this.secondaryAssignLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.SECONDARY_ASSIGN_LEAD), switchMap(action => {\n      return this.api.secondaryAssignLead(action.payload).pipe(withLatestFrom(this._store.select(getFiltersPayload)), map(([resp, filtersData]) => {\n        var _a, _b, _c, _d, _e, _f, _g;\n\n        const count = (_b = (_a = action.payload) === null || _a === void 0 ? void 0 : _a.leadIds) === null || _b === void 0 ? void 0 : _b.length;\n\n        if (count < 50) {\n          let msg = 'Lead' + (((_c = action.payload.leadIds) === null || _c === void 0 ? void 0 : _c.length) > 1 ? 's' : '') + ' Successfully ';\n          msg += (filtersData === null || filtersData === void 0 ? void 0 : filtersData.leadVisibility) === 3 ? 'Secondary Assigned' : ((_e = (_d = action.payload) === null || _d === void 0 ? void 0 : _d.userIds) === null || _e === void 0 ? void 0 : _e.length) && !((_g = (_f = action.payload) === null || _f === void 0 ? void 0 : _f.userIds) === null || _g === void 0 ? void 0 : _g.includes(EMPTY_GUID)) ? 'Secondary Reassigned' : 'Unassigned';\n\n          if (resp.succeeded) {\n            if (action.canFetchLeadList) this._store.dispatch(new FetchLeadList());\n\n            this._notificationService.success(msg);\n\n            return new SecondaryAssignLeadSuccess(resp);\n          }\n        } else {\n          this._store.dispatch(new FetchLeadList());\n\n          return new SecondaryAssignLeadSuccess(resp);\n        }\n\n        return new SecondaryAssignLeadSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkReassignLead$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_REASSIGN_LEAD), switchMap(action => {\n      return this.api.bulkReassignLead(action.payload).pipe(withLatestFrom(this._store.select(getFiltersPayload)), map(([resp, filtersData]) => {\n        var _a, _b, _c, _d, _e, _f;\n\n        const count = (_b = (_a = action.payload) === null || _a === void 0 ? void 0 : _a.leadIds) === null || _b === void 0 ? void 0 : _b.length;\n\n        if (count < 50) {\n          let msg = 'Lead' + (((_d = (_c = action.payload) === null || _c === void 0 ? void 0 : _c.leadIds) === null || _d === void 0 ? void 0 : _d.length) > 1 ? 's' : '') + ' Successfully ';\n          msg += (filtersData === null || filtersData === void 0 ? void 0 : filtersData.leadVisibility) === 3 ? 'Assigned' : ((_f = (_e = action.payload) === null || _e === void 0 ? void 0 : _e.userIds) === null || _f === void 0 ? void 0 : _f.toString()) !== EMPTY_GUID ? 'Reassigned' : 'Unassigned';\n\n          if (resp.succeeded) {\n            this._store.dispatch(new FetchLeadList());\n\n            this._notificationService.success(msg);\n\n            return new BulkReassignLeadSuccess(resp);\n          }\n        } else {\n          this._store.dispatch(new FetchLeadList());\n\n          return new BulkReassignLeadSuccess(resp);\n        }\n\n        return new BulkReassignLeadSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.addBulkLeadExcel$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.LEAD_EXCEL_UPLOAD), switchMap(action => {\n      return this.api.uploadExcel(action.file).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Leads Excel uploaded Successfully');\n\n          return new LeadExcelUploadSuccess(resp.data);\n        } else {\n          this._store.dispatch(new CloseModal());\n\n          this._notificationService.warn(`${resp.message}`);\n\n          return new FetchLeadList();\n        }\n      }), catchError(err => {\n        var _a, _b;\n\n        throwError(err);\n        Array.isArray((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.messages) ? this._notificationService.error(err.error.messages[0]) : this._notificationService.error((_b = err === null || err === void 0 ? void 0 : err.error) === null || _b === void 0 ? void 0 : _b.messages);\n        return throwError(() => err);\n      }));\n    })));\n    this.uploadMappedColumns$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPLOAD_MAPPED_COLUMNS), switchMap(action => {\n      return this.api.uploadMappedColumns(action.payload).pipe(map(resp => {\n        var _a, _b;\n\n        if (resp.succeeded) {\n          if (resp.data) {\n            if ((_a = resp.data) === null || _a === void 0 ? void 0 : _a.excelUrl) {\n              const dataCount = ((_b = resp.message) === null || _b === void 0 ? void 0 : _b.DataCount) || '';\n\n              this._notificationService.success(`${dataCount} Invalid Data Not Uploaded`);\n\n              return new ExcelUploadSuccess(resp.data);\n            } else {\n              this._notificationService.success('Excel Uploaded Successfully');\n\n              return new ExcelUploadSuccess(resp.data);\n            }\n          } else {\n            this._notificationService.error(resp.message);\n          }\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => {\n        var _a, _b;\n\n        Array.isArray((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.messages) ? this._notificationService.error(err.error.messages[0]) : this._notificationService.error((_b = err === null || err === void 0 ? void 0 : err.error) === null || _b === void 0 ? void 0 : _b.messages);\n        return throwError(() => err);\n      }));\n    })));\n    this.uploadMigrateMappedColumns$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPLOAD_MIGRATE_MAPPED_COLUMNS), switchMap(action => {\n      return this.api.uploadMigrateMappingColumns(action.payload).pipe(map(resp => {\n        var _a, _b;\n\n        if (resp.succeeded) {\n          if (resp.data) {\n            if ((_a = resp.data) === null || _a === void 0 ? void 0 : _a.excelUrl) {\n              const dataCount = ((_b = resp.message) === null || _b === void 0 ? void 0 : _b.DataCount) || '';\n\n              this._notificationService.success(`${dataCount} Invalid Data Not Uploaded`);\n\n              return new ExcelUploadSuccess(resp.data);\n            } else {\n              this._notificationService.success('Excel Uploaded Successfully');\n\n              return new ExcelUploadSuccess(resp.data);\n            }\n          } else {\n            this._notificationService.error(resp.message);\n          }\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => {\n        var _a, _b;\n\n        Array.isArray((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.messages) ? this._notificationService.error(err.error.messages[0]) : this._notificationService.error((_b = err === null || err === void 0 ? void 0 : err.error) === null || _b === void 0 ? void 0 : _b.messages);\n        return throwError(() => err);\n      }));\n    })));\n    this.getMigrateExcelUploadedList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST), map(action => action), switchMap(data => {\n      return this.api.getMigrateExcelUploadedList(data === null || data === void 0 ? void 0 : data.pageNumber, data === null || data === void 0 ? void 0 : data.pageSize).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchMigrateExcelUploadedSuccess(resp);\n        }\n\n        return new FetchMigrateExcelUploadedSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.doesLeadExists$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.HAS_LEAD_INFO), switchMap(action => {\n      return this.api.doesLeadExists(action.data).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new HasLeadInfoSuccess(resp.data);\n        }\n\n        return new HasLeadInfoSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.doesLeadAltExists$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.HAS_LEAD_ALT_INFO), switchMap(action => {\n      return this.api.doesLeadExists(action.data).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new HasLeadAltInfoSuccess(resp.data);\n        }\n\n        return new HasLeadAltInfoSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.uploadLeadDocument$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPLOAD_DOCUMENT), switchMap(action => {\n      return this.api.uploadLeadDocument(action.id, action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Document Added Successfully');\n\n          this._store.dispatch(new FetchLeadById(action.payload.leadId));\n\n          this._store.dispatch(new LeadPreviewChanged());\n\n          return new UploadLeadDocumentSuccess(action.payload.documents);\n        }\n\n        return new UploadLeadDocumentSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.deleteLeadDocument$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.DELETE_DOCUMENT), switchMap(action => {\n      return this.api.deletedLeadDocument(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Lead Document Deleted Successfully');\n\n          this._store.dispatch(new FetchLeadById(action.payload.leadId));\n        }\n      }), catchError(err => of(new OnError(err))));\n    })), {\n      dispatch: false\n    });\n    this.getProjectList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_PROJECT_LIST), map(action => action), switchMap(data => {\n      return this.api.getProjectList(data === null || data === void 0 ? void 0 : data.isWithArchive).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchProjectListSuccess(resp.data);\n        }\n\n        return new FetchProjectListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getQRProjectList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_QR_PROJECT_LIST), map(action => action), switchMap(data => {\n      return this.api.getQRProjectList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchQRProjectListSuccess(resp.data);\n        }\n\n        return new FetchQRProjectListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getPropertyList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_PROPERTY_LIST), map(action => action), switchMap(data => {\n      return this.api.getPropertyList(data === null || data === void 0 ? void 0 : data.isWithArchive).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchPropertyListSuccess(resp.data);\n        }\n\n        return new FetchPropertyListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getQRPropertyList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_QR_PROPERTY_LIST), map(action => action), switchMap(data => {\n      return this.api.getQRPropertyList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchQRPropertyListSuccess(resp.data);\n        }\n\n        return new FetchQRPropertyListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getExcelUploadedList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST), map(action => action), switchMap(data => {\n      return this.api.getExcelUploadedList(data === null || data === void 0 ? void 0 : data.pageNumber, data === null || data === void 0 ? void 0 : data.pageSize).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchExcelUploadedSuccess(resp);\n        }\n\n        return new FetchExcelUploadedSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getExportStatusList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_EXPORT_STATUS), map(action => action), switchMap(data => {\n      return this.api.getExportStatus(data.pageNumber, data.pageSize).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchExportStatusSuccess(resp);\n        }\n\n        return new FetchExportStatusSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadCurrency$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_CURRENCY_LIST), map(action => action), switchMap(data => {\n      return this.api.getCurrency().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCurrencySuccess(resp.data);\n        }\n\n        return new FetchLeadCurrencySuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.communicationCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.COMMUNICATION_COUNT), map(action => action), switchMap(data => {\n      this._store.dispatch(new CommunicationCountSuccess(data === null || data === void 0 ? void 0 : data.id, data === null || data === void 0 ? void 0 : data.payload));\n\n      return this.api.communicationCount(data.id, data.payload).pipe(map(resp => {}), catchError(err => of(new OnError(err))));\n    })), {\n      dispatch: false\n    });\n    this.communicationBulkCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.COMMUNICATION_BULK_COUNT), map(action => action), switchMap(data => {\n      this._store.dispatch(new CommunicationBulkCountSuccess(data === null || data === void 0 ? void 0 : data.payload));\n\n      return this.api.communicationBulkCount(data.payload).pipe(map(resp => {}), catchError(err => of(new OnError(err))));\n    })), {\n      dispatch: false\n    });\n    this.meetingOrVisitDone$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.MEETING_OR_VISIT_DONE), map(action => action), switchMap(data => {\n      return this.api.meetingOrVisitDone(data.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Updated Successfully');\n\n          this._store.dispatch(new LeadPreviewChanged());\n\n          return new MeetingOrVisitDoneSuccess();\n        }\n\n        this._store.dispatch(new MeetingOrVisitDoneSuccess());\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLocations$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LOCATIONS), map(action => action), switchMap(data => {\n      return this.api.getLocations().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLocationsSuccess(resp.data);\n        }\n\n        return new FetchLocationsSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadCities$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_CITIES), map(action => action), switchMap(data => {\n      return this.api.getLeadCities().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCitiesSuccess(resp.data);\n        }\n\n        return new FetchLeadCitiesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadStates$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_STATES), map(action => action), switchMap(data => {\n      return this.api.getLeadStates().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadStatesSuccess(resp.data);\n        }\n\n        return new FetchLeadStatesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadCountries$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_COUNTRIES), map(action => action), switchMap(data => {\n      return this.api.getLeadCountries().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCountriesSuccess(resp.data);\n        }\n\n        return new FetchLeadCountriesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadSubCommunities$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES), map(action => action), switchMap(data => {\n      return this.api.getLeadSubCommunities().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadSubCommunitiesSuccess(resp.data);\n        }\n\n        return new FetchLeadSubCommunitiesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadCommunities$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_COMMUNITIES), map(action => action), switchMap(data => {\n      return this.api.getLeadCommunities().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCommunitiesSuccess(resp.data);\n        }\n\n        return new FetchLeadCommunitiesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadTowerNames$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_TOWER_NAME), map(action => action), switchMap(data => {\n      return this.api.getLeadTowerNames().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadTowerNamesSuccess(resp.data);\n        }\n\n        return new FetchLeadTowerNamesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadLocalites$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_LOCALITES), map(action => action), switchMap(data => {\n      return this.api.getLocalites().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadLocalitesSuccess(resp.data);\n        }\n\n        return new FetchLeadLocalitesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadZones$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_ZONES), map(action => action), switchMap(data => {\n      return this.api.getLeadZones().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadZonesSuccess(resp.data);\n        }\n\n        return new FetchLeadZonesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.deleteLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.DELETE_LEADS), switchMap(action => this.api.deleteLeads(action.payload).pipe(switchMap(resp => {\n      if (resp.succeeded) {\n        this._notificationService.success('Deleted Successfully');\n\n        return [new DeleteLeadsSuccess(), new FetchLeadList(true, action.isInvoice)];\n      }\n\n      return of(new FetchLeadList());\n    }), catchError(err => {\n      this._notificationService.error('Failed to delete leads');\n\n      return of(new OnError(err));\n    })))));\n    this.restoreLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.RESTORE_LEADS), map(action => action), switchMap(data => {\n      return this.api.restoreLeads(data.payload).pipe(map(resp => {\n        var _a, _b;\n\n        if (resp.succeeded) {\n          this._notificationService.success('Restored Successfully');\n\n          this._store.dispatch(new RestoreLeadsSuccess());\n\n          return new FetchLeadList(true, (_a = location === null || location === void 0 ? void 0 : location.href) === null || _a === void 0 ? void 0 : _a.includes('/invoice'));\n        }\n\n        return new FetchLeadList(true, (_b = location === null || location === void 0 ? void 0 : location.href) === null || _b === void 0 ? void 0 : _b.includes('/invoice'));\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getMatchingPropertyProjectList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS), map(action => action), switchMap(data => this.commonService.getModuleList(data.payload).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchMatchingPropertyOrProjectListSuccess(resp);\n      }\n\n      return new FetchMatchingPropertyOrProjectListSuccess({});\n    }), catchError(err => of(new OnError(err)))))));\n    this.getSubSourceList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_SUB_SOURCE_LIST), map(action => action), switchMap(data => {\n      return this.api.getSubSourceList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchSubSourceListSuccess(resp.data);\n        }\n\n        return new FetchSubSourceListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkSource$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_SOURCE), switchMap(action => {\n      return this.api.bulkSource(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._store.dispatch(new FetchSubSourceList());\n\n          this._notificationService.success(`Lead Source Updated Successfully`);\n\n          this._store.dispatch(new BulkSourceSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkProjects$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_PROJECTS), switchMap(action => {\n      return this.api.bulkProjects(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Lead Project(s) Updated Successfully`);\n\n          this._store.dispatch(new BulkProjectsSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getAgencyNameList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST), map(action => action), switchMap(data => {\n      return this.api.getAgencyNames().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchAgencyNameListSuccess(resp.data);\n        }\n\n        return new FetchAgencyNameListSuccess({});\n      }), catchError(err => {\n        throwError(err);\n        return throwError(() => err);\n      }));\n    })));\n    this.getAgencyNameListAnonymous$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS), map(action => action), switchMap(data => {\n      return this.api.getAgencyNamesAnonymous().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchAgencyNameListAnonymousSuccess(resp.data);\n        }\n\n        return new FetchAgencyNameListAnonymousSuccess({});\n      }), catchError(err => {\n        throwError(err);\n        return throwError(() => err);\n      }));\n    })));\n    this.getChannelPartnerList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST), map(action => action), switchMap(data => {\n      return this.api.getChannelPartnerList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchChannelPartnerListSuccess(resp.data);\n        }\n\n        return new FetchChannelPartnerListSuccess({});\n      }), catchError(err => {\n        throwError(err);\n        return throwError(() => err);\n      }));\n    })));\n    this.getChannelPartnerListAnonymous$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS), map(action => action), switchMap(data => {\n      return this.api.getChannelPartnerListAnonymous().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchChannelPartnerListAnonymousSuccess(resp.data);\n        }\n\n        return new FetchChannelPartnerListAnonymousSuccess({});\n      }), catchError(err => {\n        throwError(err);\n        return throwError(() => err);\n      }));\n    })));\n    this.updateDuplicateAssign$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST), switchMap(action => {\n      return this.api.updateDuplicateAssign(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._store.dispatch(new FetchLeadList());\n\n          if (!resp.items.length) {\n            this._notificationService.success(`Lead Assigned Successfully`);\n          }\n\n          return new updateDuplicateAssignSuccess(resp);\n        }\n\n        return new updateDuplicateAssignSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getDuplicateFeature$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_DUPLICATE_FEATURE), map(action => action), switchMap(data => {\n      return this.api.getDuplicateFeature().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchDuplicateFeatureSuccess(resp.data);\n        }\n\n        return new FetchDuplicateFeatureSuccess({});\n      }), catchError(err => {\n        throwError(err);\n        return throwError(() => err);\n      }));\n    })));\n    this.getLeadsExport$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEADS_EXPORT), map(action => action.payload), switchMap(data => {\n      return this.commonService.getModuleList(data).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadExportSuccess(resp.data);\n        }\n\n        return new FetchLeadExportSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadNotInterestedCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_NOT_INTERESTED_COUNT), map(action => action.payload), switchMap(data => {\n      let filterPayload;\n\n      this._store.select(getFiltersPayload).subscribe(data => {\n        filterPayload = Object.assign(Object.assign({}, data), {\n          path: 'lead/counts/notInterested'\n        });\n      });\n\n      return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadNotInterestedCountSuccess(resp.data);\n        }\n\n        return new FetchLeadBaseFilterCountSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadDroppedCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_DROPPED_COUNT), map(action => action.payload), switchMap(data => {\n      let filterPayload;\n\n      this._store.select(getFiltersPayload).subscribe(data => {\n        filterPayload = Object.assign(Object.assign({}, data), {\n          path: 'lead/counts/dropped'\n        });\n      });\n\n      return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadDroppedCountSuccess(resp.data);\n        }\n\n        return new FetchLeadDroppedCountSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.exportLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.EXPORT_LEADS), switchMap(action => {\n      let isCustomStatusEnabled;\n\n      this._store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe(isEnabled => {\n        isCustomStatusEnabled = isEnabled;\n      });\n\n      return (window.location.pathname.includes('/invoice') ? this.api.exportLead(action.payload) : isCustomStatusEnabled && window.location.pathname.includes('/leads/manage-leads') ? this.api.customExportLead(action.payload) : this.api.exportLead(action.payload)).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Leads are being exported in excel format`);\n\n          return new ExportLeadsSuccess(resp);\n        }\n\n        return new ExportLeadsSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.communicationMessage$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.COMMUNICATION_MESSAGE), switchMap(action => {\n      return this.api.communicationMessage(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new CommunicationMessageSuccess(resp);\n        }\n\n        return new CommunicationMessageSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.communicationBulkMessage$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.COMMUNICATION_BULK_MESSAGE), switchMap(action => {\n      return this.api.communicationBulkMessage(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new CommunicationBulkMessageSuccess(resp);\n        }\n\n        return new CommunicationBulkMessageSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getAppointmentsByProjects$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS), switchMap(action => {\n      const payload = Object.assign({}, action === null || action === void 0 ? void 0 : action.payload);\n      payload.path = 'lead/getappointmentsbyprojects';\n      return this.commonService.getModuleListByAdvFilter(payload).pipe(map(resp => {\n        if (resp === null || resp === void 0 ? void 0 : resp.succeeded) {\n          return new FetchLeadAppointmentsByProjectsSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadAppointmentsByProjectsSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadCommunicationsById$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS), switchMap(action => {\n      const payload = Object.assign(Object.assign({}, action === null || action === void 0 ? void 0 : action.payload), {\n        path: 'lead/communications'\n      });\n\n      if (!payload.showCommunicationCount) {\n        return of({\n          type: '[Lead] Noop'\n        });\n      }\n\n      return this.commonService.getModuleListByAdvFilter(payload).pipe(map(resp => {\n        if (resp === null || resp === void 0 ? void 0 : resp.succeeded) {\n          return new FetchLeadsCommunicationByIdsSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadsCommunicationByIdsSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.addProjectsToLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.ADD_LEAD_PROJECTS), switchMap(action => {\n      return this.api.addProjects(action === null || action === void 0 ? void 0 : action.payload).pipe(map(resp => {\n        return new AddLeadProjectsSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.addLeadRotationGroup$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.ADD_LEAD_ROTATION_GROUP), switchMap(action => this.leadRotationApi.createLeadRotationGroup(action.payload).pipe(map(resp => {\n      if (resp.succeeded) {\n        this._notificationService.success('Lead Rotation Group added Successfully');\n      }\n\n      return new FetchLeadRotation();\n    }), catchError(error => of(new OnError(error)))))));\n    this.getLeadRotation = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_ROTATION), switchMap(action => this.leadRotationApi.getLeadRotation().pipe(map(resp => {\n      new FetchUsersListForReassignment();\n      return new FetchLeadRotationSuccess(resp);\n    }), catchError(error => of(new OnError(error)))))));\n    this.deleteLeadRotation$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.DELETE_LEAD_ROTATION), switchMap(action => this.leadRotationApi.deleteLeadRotation(action.id).pipe(map(resp => {\n      if (resp.succeeded) {\n        this._notificationService.success('Lead Rotation Group deleted Successfully');\n      }\n\n      return new FetchLeadRotation();\n    }), catchError(error => of(new OnError(error)))))));\n    this.updateLeadRotation$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_LEAD_ROTATION), switchMap(action => this.leadRotationApi.updateLeadRotation(action.payload).pipe(map(resp => {\n      if (resp.succeeded) {\n        this._notificationService.success('Lead Rotation Group updated Successfully');\n      }\n\n      return new FetchLeadRotation();\n    }), catchError(error => of(new OnError(error)))))));\n    this.getLeadCustomTopFiltersChildren$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN), map(action => action), switchMap(action => {\n      let filterPayload;\n\n      this._store.select(getFiltersPayload).subscribe(data => {\n        filterPayload = data;\n      });\n\n      let LeadVisibility = (filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.LeadVisibility) ? filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.LeadVisibility : 0;\n      let leadVisibility = (filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.leadVisibility) ? filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.leadVisibility : 0;\n      let ScheduledType = (filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.ScheduledType) ? filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.ScheduledType : 0;\n      let CustomFilterBaseIds = ((filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customThirdLevelFilterId) ? [filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customThirdLevelFilterId, filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customSecondLevelFilterId, filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customFirstLevelFilterId] : null) || ((filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customSecondLevelFilterId) ? [filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customSecondLevelFilterId, filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customFirstLevelFilterId] : null) || ((filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customFirstLevelFilterId) ? [filterPayload === null || filterPayload === void 0 ? void 0 : filterPayload.customFirstLevelFilterId] : null);\n      filterPayload = Object.assign(Object.assign({}, filterPayload), {\n        path: 'lead/custom-filters-count-level2',\n        ScheduledType,\n        LeadVisibility,\n        leadVisibility,\n        pageNumber: null,\n        pageSize: null,\n        CustomFilterBaseIds\n      });\n      return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCustomTopFiltersChildrenSuccess(resp.data);\n        }\n\n        return new FetchLeadCustomTopFiltersChildrenSuccess([]);\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.permanentDeleteLeads$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.PERMANENT_DELETE_LEADS), map(action => action), switchMap(data => {\n      return this.api.permanentdeleteLeads(data.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success('Deleted Successfully');\n\n          this._store.dispatch(new PermanentDeleteLeadsSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadList();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getBulkOperationList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_BULK_OPERATION), map(action => action), switchMap(data => {\n      return this.api.getBulkOperation(data.pageNumber, data.pageSize, data.moduleType).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchBulkOperationSuccess(resp);\n        }\n\n        return new FetchBulkOperationSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getUploadTypeNameList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_UPLOADTYPENAME_LIST), map(action => action), switchMap(data => {\n      return this.api.getUploadTypeNameList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchUploadTypeNameListSuccess(resp.data);\n        }\n\n        return new FetchUploadTypeNameListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLeadAuditHistory$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_HISTORY), map(action => action), switchMap(data => this.api.getAuditHistory(data.id).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchLeadHistoryListSuccess(resp.data);\n      }\n\n      return new FetchLeadHistoryListSuccess([]);\n    }), catchError(err => of(new OnError(err)))))));\n    this.getAdditionalProperty$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST), map(action => action), switchMap(data => {\n      return this.api.getAdditionalProperties().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchAdditionalPropertyListSuccess(resp.data);\n        }\n\n        return new FetchAdditionalPropertyListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getAdditionalPropertyValue$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE), map(action => action), switchMap(data => {\n      return this.api.getAPValues(data === null || data === void 0 ? void 0 : data.key).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchAdditionalPropertyValueSuccess(resp.data);\n        }\n\n        return new FetchAdditionalPropertyValueSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.navigateToLink$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.NAVIGATE_TO_LINK), map(action => action), switchMap(data => {\n      return this.api.navigateToLink(data.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new NavigateToLinkSuccess(data.payload);\n        }\n\n        return new NavigateToLinkSuccess({});\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getCampaignList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST), map(action => action), switchMap(data => {\n      return this.api.getCampaignList().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchCampaignListSuccess(resp.data);\n        }\n\n        return new FetchCampaignListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getCampaignListAnonymous$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS), map(action => action), switchMap(data => {\n      return this.api.getCampaignListAnonymous().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchCampaignListAnonymousSuccess(resp.data);\n        }\n\n        return new FetchCampaignListAnonymousSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.fetchCountryBasedCity$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_COUNTRY_BASED_CITY), map(action => action), switchMap(data => {\n      return this.api.getCountryBasedCity().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchCountryBasedCitySuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchCountryBasedCitySuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.customTopLevelFilters$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS), withLatestFrom(this._store.pipe(select(state => {\n      var _a;\n\n      return (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.filtersPayload;\n    }))), switchMap(([action, filtersFromState]) => {\n      console.log(filtersFromState);\n      let CustomFilterBaseIds = ((filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomThirdLevelFilter) ? [filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomThirdLevelFilter, filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomSecondLevelFilter, filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomFirstLevelFilter] : null) || ((filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomSecondLevelFilter) ? [filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomSecondLevelFilter, filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomFirstLevelFilter] : null) || ((filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomFirstLevelFilter) ? [filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.CustomFirstLevelFilter] : null) || null;\n      const filters = Object.assign(Object.assign({}, filtersFromState), {\n        path: 'lead/custom-filters-count-level1',\n        CustomFilterBaseIds\n      });\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(map(resp => {\n        if (resp.succeeded) {\n          localStorage.setItem('leadLevelFilter', JSON.stringify(resp === null || resp === void 0 ? void 0 : resp.data));\n          return new FetchLeadCustomTopFiltersSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        } else {\n          return new FetchLeadCustomTopFiltersSuccess([]);\n        }\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.filterBaseCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_BASEFILTER_COUNT), withLatestFrom(this._store.pipe(select(state => {\n      var _a, _b;\n\n      return window.location.pathname.includes('/invoice') ? (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.invoiceFiltersPayload : (_b = state === null || state === void 0 ? void 0 : state.lead) === null || _b === void 0 ? void 0 : _b.filtersPayload;\n    }))), switchMap(([action, filtersFromState]) => {\n      const fetchAction = action;\n      console.log('Action filters:', fetchAction.filtersPayload);\n      console.log('State filters:', filtersFromState); // Merge filtersFromState with any additional filters from action\n      // This preserves the existing state but allows overriding specific properties\n\n      const filters = Object.assign(Object.assign(Object.assign({}, filtersFromState), fetchAction.filtersPayload || {}), {\n        path: 'lead/new/counts/basefilter'\n      });\n      console.log('filters', filters);\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(map(resp => {\n        return new FetchLeadBaseFilterCountSuccess((resp === null || resp === void 0 ? void 0 : resp.data) || {});\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.fetchLeadList$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_LIST_V2), withLatestFrom(this._store.pipe(select(state => {\n      var _a, _b;\n\n      return window.location.pathname.includes('/invoice') ? (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.invoiceFiltersPayload : (_b = state === null || state === void 0 ? void 0 : state.lead) === null || _b === void 0 ? void 0 : _b.filtersPayload;\n    })), this._store.select(getIsLoadMore)), switchMap(([action, filtersFromState, isLoadmore]) => {\n      if (!isLoadmore && (filtersFromState === null || filtersFromState === void 0 ? void 0 : filtersFromState.pageNumber) === 1) {\n        this._store.dispatch(new ClearCardData());\n      }\n\n      ;\n\n      this._store.dispatch(new UpdateIsLoadMore(false));\n\n      let filters = Object.assign(Object.assign({}, filtersFromState), {\n        path: this.isCustomStatusEnabled && !window.location.pathname.includes('/invoice') ? 'lead/custom-filters' : 'lead/new/all'\n      });\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(tap(resp => this.dispatchLeadBatches(resp === null || resp === void 0 ? void 0 : resp.items, filters === null || filters === void 0 ? void 0 : filters.showCommunicationCount)), switchMap(resp => {\n        var _a;\n\n        if (((_a = resp === null || resp === void 0 ? void 0 : resp.items) === null || _a === void 0 ? void 0 : _a.length) === 0 && (filters === null || filters === void 0 ? void 0 : filters.pageNumber) > 1) {\n          filters = Object.assign(Object.assign({}, filters), {\n            pageNumber: (filters === null || filters === void 0 ? void 0 : filters.pageNumber) - 1\n          });\n\n          if (window.location.pathname.includes('/invoice')) {\n            this._store.dispatch(new UpdateInvoiceFilter(filters));\n          } else {\n            this._store.dispatch(new UpdateFilterPayload(filters));\n          }\n\n          return EMPTY;\n        }\n\n        return of(new FetchLeadListSuccess(resp), new UpdateCardData(resp === null || resp === void 0 ? void 0 : resp.items));\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.fetchLeadActiveCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_ACTIVE_COUNT), withLatestFrom(this._store.pipe(select(state => {\n      var _a, _b;\n\n      return window.location.pathname.includes('/invoice') ? (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.invoiceFiltersPayload : (_b = state === null || state === void 0 ? void 0 : state.lead) === null || _b === void 0 ? void 0 : _b.filtersPayload;\n    }))), switchMap(([action, filtersFromState]) => {\n      const fetchAction = action;\n      console.log('Active Count - Action filters:', fetchAction.filtersPayload);\n      console.log('Active Count - State filters:', filtersFromState); // Merge filtersFromState with any additional filters from action\n\n      const filters = Object.assign(Object.assign(Object.assign({}, filtersFromState), fetchAction.filtersPayload || {}), {\n        path: 'lead/counts/active'\n      });\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(map(resp => new FetchLeadActiveCountSuccess((resp === null || resp === void 0 ? void 0 : resp.data) || {})));\n    }), catchError(err => of(new OnError(err)))));\n    this.getLeadFlagCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_FLAGS_COUNT), withLatestFrom(this._store.pipe(select(state => {\n      var _a, _b;\n\n      return window.location.pathname.includes('/invoice') ? (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.invoiceFiltersPayload : (_b = state === null || state === void 0 ? void 0 : state.lead) === null || _b === void 0 ? void 0 : _b.filtersPayload;\n    }))), switchMap(([action, filtersFromState]) => {\n      const filters = Object.assign(Object.assign({}, filtersFromState), {\n        path: 'lead/counts/custom-flags'\n      });\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(map(resp => new FetchLeadFlagsCountSuccess((resp === null || resp === void 0 ? void 0 : resp.data) || [])));\n    }), catchError(err => of(new OnError(err)))));\n    this.fetchLeadStatusCount$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_STATUS_COUNT), withLatestFrom(this._store.pipe(select(state => {\n      var _a, _b;\n\n      return window.location.pathname.includes('/invoice') ? (_a = state === null || state === void 0 ? void 0 : state.lead) === null || _a === void 0 ? void 0 : _a.invoiceFiltersPayload : (_b = state === null || state === void 0 ? void 0 : state.lead) === null || _b === void 0 ? void 0 : _b.filtersPayload;\n    }))), switchMap(([action, filtersFromState]) => {\n      console.log('filtersFromState', filtersFromState);\n      const filters = Object.assign(Object.assign({}, filtersFromState), {\n        path: 'lead/counts/statuses'\n      });\n      return this.commonService.getModuleListByAdvFilter(filters).pipe(map(resp => {\n        var _a;\n\n        return new FetchLeadStatusCountSuccess(((_a = resp === null || resp === void 0 ? void 0 : resp.data) === null || _a === void 0 ? void 0 : _a.items) || []);\n      }));\n    }), catchError(err => of(new OnError(err)))));\n    this.updateFiltersPayload$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.UPDATE_FILTER_PAYLOAD, LeadActionTypes.UPDATE_INVOICE_FILTER), switchMap( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (action) {\n        var _a, _b;\n\n        _this.isCustomStatusEnabled = yield _this._store.select(getIsLeadCustomStatusEnabled).pipe(map(data => data), take(1)).toPromise();\n        const showFilterCount = (_a = action.filter) === null || _a === void 0 ? void 0 : _a.showFilterCount;\n        const dontCallApi = (_b = action.filter) === null || _b === void 0 ? void 0 : _b.dontCallApi;\n\n        if (!_this.isCustomStatusEnabled || window.location.pathname.includes('/invoice')) {\n          if (showFilterCount && !dontCallApi) {\n            _this._store.dispatch(new FetchActiveCount());\n\n            _this._store.dispatch(new FetchLeadStatusCount());\n          }\n        } else if (showFilterCount && !dontCallApi) {\n          _this._store.dispatch(new FetchLeadCustomTopFilters());\n        } // this._store.dispatch(new FetchLeadFlagsCount())\n\n\n        _this._store.dispatch(new FetchLeadListV2());\n\n        if (showFilterCount && !dontCallApi) {\n          return new FetchLeadBaseFilterCount();\n        }\n\n        return {\n          type: '[Lead] Noop'\n        };\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }())));\n    this.getNationality$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_NATIONALITY), map(action => action), switchMap(data => {\n      return this.api.getNationality().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadNationalitySuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadNationalitySuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getClusterNames$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_CLUSTER_NAME), map(action => action), switchMap(data => {\n      return this.api.getLeadClusterName().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadClusterNameSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadClusterNameSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getUnitNames$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_UNIT_NAME), map(action => action), switchMap(data => {\n      return this.api.getLeadUnitName().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadUnitNameSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadUnitNameSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getAltCountryCode$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_ALT_COUNTRY_CODE), map(action => action), switchMap(data => {\n      return this.api.getLeadAltCountryCode().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadAltCountryCodeSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadAltCountryCodeSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getCountryCode$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_COUNTRY_CODE), map(action => action), switchMap(data => {\n      return this.api.getLeadCountryCode().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadCountryCodeSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadCountryCodeSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getParentLeadById$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID), map(action => action.leadId), switchMap(data => this.api.getParentLeadData(data).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchAllParentLeadByIdSuccess(resp.data);\n      }\n\n      return new FetchAllParentLeadByIdSuccess(resp.data);\n    }), catchError(err => of(new OnError(err)))))));\n    this.getLeadByIdWithArchive$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE), map(action => action.leadId), switchMap(data => this.api.getLeadByIdWithArchive(data).pipe(map(resp => {\n      if (resp.succeeded) {\n        return new FetchLeadByIdWithArchiveSuccess(resp.data);\n      }\n\n      return new FetchLeadByIdWithArchiveSuccess(resp.data);\n    }), catchError(err => of(new OnError(err)))))));\n    this.getPostalCode$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_POSTAL_CODE), map(action => action), switchMap(data => {\n      return this.api.getLeadPostalCode().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadPostalCodeSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadPostalCodeSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getLandLine$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_LEAD_LANDLINE), map(action => action), switchMap(data => {\n      return this.api.getLeadLandLine().pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchLeadLandLineSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchLeadLandLineSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.getModuleWiseSearchProperties$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.FETCH_MODULE_WISE_SEARCH_PROPERTIES), map(action => action.moduleType), switchMap(data => {\n      return this.api.getModuleWiseSearchProperties(data).pipe(map(resp => {\n        if (resp.succeeded) {\n          return new FetchModuleWiseSearchPropertiesSuccess(resp === null || resp === void 0 ? void 0 : resp.data);\n        }\n\n        return new FetchModuleWiseSearchPropertiesSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkAgency$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_AGENCY), switchMap(action => {\n      return this.api.bulkUpdateAgency(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Lead Agency(s) Updated Successfully`);\n\n          this._store.dispatch(new BulkAgencySuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkChannelPartner$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_CHANNEL_PARTNER), switchMap(action => {\n      return this.api.bulkUpdateChannelPartner(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Lead Channel partner(s) Updated Successfully`);\n\n          this._store.dispatch(new BulkChannelPartnerSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.bulkCampaign$ = createEffect(() => this.actions$.pipe(ofType(LeadActionTypes.BULK_CAMPAIGN), switchMap(action => {\n      return this.api.bulkUpdateCampaign(action.payload).pipe(map(resp => {\n        if (resp.succeeded) {\n          this._notificationService.success(`Lead Campaign(s) Updated Successfully`);\n\n          this._store.dispatch(new BulkCampaignSuccess());\n\n          return new FetchLeadList();\n        }\n\n        return new FetchLeadListSuccess();\n      }), catchError(err => of(new OnError(err))));\n    })));\n    this.isCustomStatusEnabled = false;\n  }\n\n  dispatchLeadBatches(items = [], showCommunicationCount) {\n    if (!(items === null || items === void 0 ? void 0 : items.length)) return;\n    const leadIds = items.map(lead => lead === null || lead === void 0 ? void 0 : lead.id).filter(Boolean);\n    const batchSize = 50;\n\n    for (let i = 0; i < leadIds.length; i += batchSize) {\n      const payload = {\n        LeadIds: leadIds.slice(i, i + batchSize),\n        showCommunicationCount: showCommunicationCount\n      };\n\n      this._store.dispatch(new FetchLeadsCommunicationByIds(payload));\n    }\n  }\n\n}\n\nLeadEffects.ɵfac = function LeadEffects_Factory(t) {\n  return new (t || LeadEffects)(i0.ɵɵinject(i1.Actions), i0.ɵɵinject(i2.GetLeadsService), i0.ɵɵinject(i3.LeadsRotationService), i0.ɵɵinject(i4.NotificationsService), i0.ɵɵinject(i5.Store), i0.ɵɵinject(i6.CommonService), i0.ɵɵinject(i7.Router), i0.ɵɵinject(i8.BsModalService), i0.ɵɵinject(i8.BsModalRef));\n};\n\nLeadEffects.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: LeadEffects,\n  factory: LeadEffects.ɵfac\n});", "map": {"version": 3, "mappings": ";AAEA,SAAkBA,YAAlB,EAAgCC,MAAhC,QAA8C,eAA9C;AACA,SAAgBC,MAAhB,QAA8B,aAA9B;AAGA,SAASC,KAAT,EAAgBC,EAAhB,EAAoBC,UAApB,QAAsC,MAAtC;AACA,SACEC,UADF,EAEEC,GAFF,EAGEC,QAHF,EAIEC,SAJF,EAKEC,IALF,EAMEC,GANF,EAOEC,cAPF,QAQO,gBARP;AASA,SAASC,UAAT,EAAqBC,OAArB,QAAoC,qBAApC;AACA,SAASC,UAAT,QAA2B,uBAA3B;AAEA,SAASC,uBAAT,QAAwC,iFAAxC;AACA,SAGEC,sBAHF,EAKEC,cALF,EAOEC,gBAPF,EASEC,iBATF,EAWEC,mBAXF,EAaEC,yBAbF,EAeEC,mBAfF,EAiBEC,uBAjBF,EAmBEC,iBAnBF,EAoBEC,aApBF,EAsBEC,6BAtBF,EAwBEC,+BAxBF,EA0BEC,yBA1BF,EA4BEC,2BA5BF,EA+BEC,kBA/BF,EAgCEC,WAhCF,EAkCEC,kBAlCF,EAoCEC,kBApCF,EAqCEC,gBArCF,EAuCEC,kCAvCF,EAyCEC,mCAzCF,EA4CEC,mCA5CF,EA6CEC,0BA7CF,EA+CEC,6BA/CF,EAiDEC,yBAjDF,EAoDEC,iCApDF,EAqDEC,wBArDF,EAwDEC,uCAxDF,EAyDEC,8BAzDF,EA2DEC,4BA3DF,EA6DEC,4BA7DF,EA+DEC,yBA/DF,EAiEEC,wBAjEF,EAkEEC,2BAlEF,EAoEEC,8BApEF,EAsEEC,sCAtEF,EAuEEC,wBAvEF,EAwEEC,+BAxEF,EAyEEC,aAzEF,EA2EEC,+BA3EF,EA6EEC,sBA7EF,EA+EEC,2BA/EF,EAiFEC,2BAjFF,EAmFEC,yBAnFF,EAqFEC,2BArFF,EAuFEC,wBAvFF,EAwFEC,yBAxFF,EA0FEC,wCA1FF,EA2FEC,gCA3FF,EA6FEC,4BA7FF,EA+FEC,sBA/FF,EAgGEC,0BAhGF,EAkGEC,2BAlGF,EAmGEC,kBAnGF,EAqGEC,wBArGF,EAsGEC,aAtGF,EAuGEC,oBAvGF,EAwGEC,eAxGF,EA0GEC,yBA1GF,EA4GEC,2BA5GF,EA8GEC,kCA9GF,EAgHEC,yBAhHF,EAkHEC,0BAlHF,EAmHEC,iBAnHF,EAoHEC,wBApHF,EAsHEC,sBAtHF,EAuHEC,oBAvHF,EAwHEC,2BAxHF,EA0HEC,8BA1HF,EA4HEC,0BA5HF,EA8HEC,wBA9HF,EAgIEC,qBAhIF,EAiIEC,4BAjIF,EAkIEC,mCAlIF,EAoIEC,qBApIF,EAsIEC,yCAtIF,EAwIEC,gCAxIF,EA0IEC,sCA1IF,EA4IEC,uBA5IF,EA8IEC,wBA9IF,EAgJEC,yBAhJF,EAkJEC,0BAlJF,EAmJEC,kBAnJF,EAoJEC,yBApJF,EAsJEC,8BAtJF,EAwJEC,qBAxJF,EA0JEC,kBA1JF,EA2JEC,eA3JF,EA6JEC,sBA7JF,EA+JEC,yBA/JF,EAiKEC,qBAjKF,EAmKEC,2BAnKF,EAqKEC,mBArKF,EAuKEC,mBAvKF,EAyKEC,mBAzKF,EA2KEC,0BA3KF,EA4KEC,cA5KF,EA6KEC,mBA7KF,EA8KEC,mBA9KF,EA+KEC,gBA/KF,EAkLEC,sBAlLF,EAqLEC,uBArLF,EAsLEC,iBAtLF,EAyLEC,wBAzLF,EA0LEC,+BA1LF,EA4LEC,yBA5LF,EAgMEC,4BAhMF,QAkMO,oCAlMP;AAmMA,SACEC,iBADF,EAEEC,wBAFF,EAGEC,4BAHF,EAIEC,aAJF,QAKO,oCALP;AAMA,SACEC,kBADF,QAEO,wCAFP;AAGA,SAASC,6BAAT,QAA8C,sCAA9C;;;;;;;;;;AAMA,OAAM,MAAOC,WAAP,CAAkB;EAmsEtBC,YACUC,QADV,EAEUC,GAFV,EAGUC,eAHV,EAIUC,oBAJV,EAKUC,MALV,EAMUC,aANV,EAOUC,MAPV,EAQUC,YARV,EASUC,QATV,EAS8B;IAAA;;IARpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IA1sEV,oBAAe5I,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsC,eAAjB,CADR,EAEErI,SAAS,CAAC,MACR,KAAK+H,MAAL,CAAYtI,MAAZ,CAAmB6I,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IAAgDrB,wBAAhD,GAA2ED,iBAA9F,EAAiHiB,IAAjH,CACEnI,IAAI,CAAC,CAAD,CADN,EAEEH,GAAG,CAAE4I,aAAD,IAAwBJ,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IAAgD,IAAI/B,mBAAJ,CAAwBgC,aAAxB,CAAhD,GAAyF,IAAIjC,mBAAJ,CAAwBiC,aAAxB,CAAlH,CAFL,CAE+J;IAF/J,CADO,CAFX,CADyB,CAA3B;IAYA,gBAAWnJ,YAAY,CAAC,MACtB,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4C,QAAjB,CADR,EAEE7I,GAAG,CAAE8I,MAAD,IAAqBA,MAAM,CAACC,OAA7B,CAFL,EAGE7I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASmB,GAAT,CAAaD,IAAb,EAAmBV,IAAnB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,yBAAlC;;UACA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAI1I,cAAJ,EAArB;;UACA,OAAO,IAAIsD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CAPE,CADE,EASLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATL,CAAP;IAWD,CAZQ,CAHX,CADqB,CAAvB;IAoBA,kBAAa7J,YAAY,CAAC,MACxB,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsD,WAAjB,CADR,EAEErJ,SAAS,CAAE4I,MAAD,IAAsB;MAC9B,OAAO,KAAKhB,GAAL,CAAS0B,SAAT,CAAmBV,MAAM,CAACC,OAA1B,EAAmCD,MAAM,CAACW,UAA1C,EAAsDnB,IAAtD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB;UACA,IAAIO,YAAY,GAAQ;YACtBC,KAAK,EAAE,gDADe;YAEtBC,mBAAmB,EAAE;UAFC,CAAxB;UAIA,KAAKvB,QAAL,GAAgB,KAAKD,YAAL,CAAkByB,IAAlB,CACdpJ,uBADc,EAEdiJ,YAFc,CAAhB;UAIA,OAAO,IAAI9I,gBAAJ,EAAP;QACD;;QACD,OAAO,IAAIA,gBAAJ,EAAP;MACD,CAdE,CADE,EAgBLb,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAhBL,CAAP;IAkBD,CAnBQ,CAFX,CADuB,CAAzB;IA0BA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6D,YAAjB,CADR,EAEE5J,SAAS,CAAE4I,MAAD,IAAwB;MAChC,OAAO,KAAKhB,GAAL,CAASiC,WAAT,CAAqBjB,MAAM,CAACkB,IAA5B,EAAkC1B,IAAlC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,6BAAlC;;UACA,IAAInF,aAAJ;UACA,OAAO,IAAIvC,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;QACD,CAJD,MAIO;UACL,KAAKf,MAAL,CAAYoB,QAAZ,CAAqB,IAAI/I,UAAJ,EAArB;;UACA,KAAK0H,oBAAL,CAA0BiC,IAA1B,CAA+B,GAAGf,IAAI,CAACgB,OAAO,EAA9C;;UACA,IAAIjG,aAAJ;UACA,OAAO,IAAIvC,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;QACD;MACF,CAXE,CADE,EAaLjJ,UAAU,CAAEuJ,GAAD,IAAQ;;;QACjB,KAAKtB,oBAAL,CAA0BmC,KAA1B,CAAgC,eAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEA,KAAL,MAAU,IAAV,IAAUC,aAAV,GAAU,MAAV,GAAUA,GAAEC,OAAZ,MAAmB,IAAnB,IAAmBC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,KAAF,CAAQ,GAAR,EAAa,CAAb,CAAnD;;QACA,OAAO1K,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAT;MACD,CAHS,CAbL,CAAP;IAkBD,CAnBQ,CAFX,CAD0B,CAA5B;IA0BA,mBAAc7J,YAAY,CAAC,MACzB,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACuE,WAAjB,CADR,EAEEtK,SAAS,CAAE4I,MAAD,IAAuB;MAC/B,OAAO,KAAKhB,GAAL,CAAS2C,MAAT,CAAgB3B,MAAM,CAACC,OAAvB,EAAgCD,MAAM,CAAC4B,EAAvC,EAA2CpC,IAA3C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,2BAAlC;;UACA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIrC,iBAAJ,CAAsBkC,IAAI,CAACC,SAA3B,CAArB;;UACA,OAAO,IAAIlF,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CAPE,CADE,EASLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATL,CAAP;IAWD,CAZQ,CAFX,CADwB,CAA1B;IAmBA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0E,iBAAjB,CADR,EAEEzK,SAAS,CAAE4I,MAAD,IAA4B;MACpC,OAAO,KAAKhB,GAAL,CAAS8C,WAAT,CAAqB9B,MAAM,CAAC4B,EAA5B,EAAgC5B,MAAM,CAACC,OAAvC,EAAgDT,IAAhD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,iCADF;;UAGA,IAAIN,MAAM,CAAC+B,gBAAX,EAA6B;YAC3B;YACA,KAAK5C,MAAL,CAAYoB,QAAZ,CAAqB,IAAI5B,kBAAJ,EAArB;UACD;;UACD,MAAMqD,WAAW,GAAGC,YAAY,CAACC,OAAb,CAAqB,aAArB,CAApB;UACA,MAAMC,eAAe,GAAGH,WAAW,GAAGI,IAAI,CAACC,KAAL,CAAWL,WAAX,EAAwBM,UAAxB,GAAqC,GAArC,GAA2CF,IAAI,CAACC,KAAL,CAAWL,WAAX,EAAwBO,WAAnE,IAAkFH,IAAI,CAACC,KAAL,CAAWL,WAAX,EAAwBQ,kBAA1G,IAAgI,cAAnI,GAAoJ,cAAvL;UACA,MAAMC,OAAO,GAAG;YACdC,SAAS,EAAE,OADG;YAEdC,SAAS,EAAE,CAFG;YAGdC,QAAQ,EAAE,EAHI;YAIdC,QAAQ,EAAE7C,MAAM,CAACC,OAAP,CAAe6C,KAJX;YAKdC,SAAS,EAAEZ,eALG;YAMda,SAAS,EAAE,IAAIC,IAAJ,GAAWC,WAAX,EANG;YAOdC,eAAe,EAAE;UAPH,CAAhB;UASA,OAAO,IAAInF,sBAAJ,CAA2ByE,OAA3B,CAAP;QACD;;QACD,OAAO,IAAIhH,yBAAJ,CAA8B,EAA9B,CAAP;MACD,CAvBE,CADE,EAyBLxE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAzBL,CAAP;IA2BD,CA5BQ,CAFX,CAD6B,CAA/B;IAmCA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACiG,qBAAjB,CADR,EAEElM,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IACR,KAAKlB,GAAL,CAASqE,YAAT,CAAsBnD,IAAI,CAAC0B,EAA3B,EAA+BpC,IAA/B,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAI5E,yBAAJ,CAA8B2E,IAAI,CAACF,IAAnC,CAAP;MACD;;MACD,OAAO,IAAIzE,yBAAJ,CAA8B,EAA9B,CAAP;IACD,CALE,CADL,EAOExE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CAD0B,CAA5B;IAkBA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACmG,oBAAjB,CADR,EAEElM,SAAS,CAAE4I,MAAD,IAA+B;MACvC,IAAIA,MAAM,CAACuD,cAAX,EACE,KAAKpE,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpC,wBAAJ,EAArB;MACF,OAAO,KAAKa,GAAL,CAASwE,oBAAT,CAA8BxD,MAAM,CAACC,OAArC,EAA8CT,IAA9C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,4BAAlC;;UACA,IAAIN,MAAM,CAACuD,cAAX,EAA2B;YACzB,KAAKpE,MAAL,CAAYoB,QAAZ,CAAqB,IAAInC,+BAAJ,EAArB;UACD;;UACD,OAAO,IAAIjD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CATE,CADE,EAWLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAhBQ,CAFX,CADgC,CAAlC;IAuBA,oBAAe7J,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsG,gBAAjB,CADR,EAEEvM,GAAG,CAAE8I,MAAD,IAA2BA,MAAM,CAAC0D,MAAnC,CAFL,EAGEtM,SAAS,CAAE8I,IAAD,IACR,KAAKlB,GAAL,CAAS2E,GAAT,CAAazD,IAAb,EAAmBV,IAAnB,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAIpF,kBAAJ,CAAuBmF,IAAI,CAACF,IAA5B,CAAP;MACD;;MACD,OAAO,IAAIjF,kBAAJ,EAAP;IACD,CALE,CADL,EAOEhE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CADyB,CAA3B;IAkBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACyG,kBAAjB,CADR,EAEExM,SAAS,CAAE4I,MAAD,IAA6B;MACrC,OAAO,KAAKhB,GAAL,CAAS6E,gBAAT,CAA0B7D,MAAM,CAACC,OAAjC,EAA0CD,MAAM,CAAC0D,MAAjD,EAAyDlE,IAAzD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,kCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAItC,uBAAJ,EAArB;;UACA,KAAKkB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIrG,aAAJ,CAAkB8F,MAAM,CAAC0D,MAAzB,CAArB;;UACA,OAAO1D,MAAM,CAAC+B,gBAAP,GACH,IAAI5G,aAAJ,EADG,GAEH,IAAIxC,WAAJ,EAFJ;QAGD;;QACD,OAAO,IAAIyC,oBAAJ,EAAP;MACD,CAZE,CADE,EAcLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAdL,CAAP;IAgBD,CAjBQ,CAFX,CAD0B,CAA5B;IAwBA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2G,uBAAjB,CADR,EAEE1M,SAAS,CAAE4I,MAAD,IAAiC;MACzC,OAAO,KAAKhB,GAAL,CAAS+E,kBAAT,CAA4B/D,MAAM,CAAC0D,MAAnC,EAA2ClE,IAA3C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,0BAAlC;;UACA,OAAO,IAAInF,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CANE,CADE,EAQLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CARL,CAAP;IAUD,CAXQ,CAFX,CADkC,CAApC;IAkBA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6G,qBAAjB,CADR,EAEE7M,QAAQ,CAAE6I,MAAD,IAA+B;MACtC,OAAO,KAAKhB,GAAL,CAASiF,cAAT,CAAwBjE,MAAM,CAACC,OAA/B,EAAwCD,MAAM,CAAC4B,EAA/C,EAAmDpC,IAAnD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,qBAAqBF,IAAI,CAACgB,OAAO,EADnC;;UAGA,OAAOpB,MAAM,CAAC+B,gBAAP,GACH,IAAI5G,aAAJ,EADG,GAEH,IAAIxC,WAAJ,EAFJ;QAGD;;QACD,OAAO,IAAIyC,oBAAJ,EAAP;MACD,CAVE,CADE,EAYLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAZL,CAAP;IAcD,CAfO,CAFV,CADgC,CAAlC;IAsBA,oBAAe7J,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC+G,YAAjB,CADR,EAEE9M,SAAS,CAAE4I,MAAD,IAAwB;MAChC,OAAO,EAAP;IACD,CAFQ,CAFX,CADyB,CAA3B;IASA,qBAAgBrJ,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgH,aAAjB,CADR,EAEE/M,SAAS,CAAE4I,MAAD,IAAyB;MACjC,OAAO,KAAKhB,GAAL,CAASoF,YAAT,CAAsBpE,MAAM,CAACC,OAA7B,EAAsCT,IAAtC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;;;QAChB,IAAIiE,GAAG,GACL,UACC,aAAM,CAACpE,OAAP,CAAeqE,OAAf,MAAsB,IAAtB,IAAsBhD,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEiD,MAAxB,IAAiC,CAAjC,GAAqC,GAArC,GAA2C,EAD5C,IAEA,gBAHF;;QAIA,KAAKpF,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,EAAsCiG,SAAtC,CAAiDtE,IAAD,IAAc;;;UAC5DmE,GAAG,IACD,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEI,cAAN,MAAyB,CAAzB,GACI,UADJ,GAEI,mBAAM,CAACxE,OAAP,MAAc,IAAd,IAAcqB,aAAd,GAAc,MAAd,GAAcA,GAAEoD,OAAhB,MAAuB,IAAvB,IAAuBlD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE+C,MAAzB,KACA,EAAC,kBAAM,CAACtE,OAAP,MAAc,IAAd,IAAc0E,aAAd,GAAc,MAAd,GAAcA,GAAED,OAAhB,MAAuB,IAAvB,IAAuBE,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE/E,QAAF,CAAWnI,UAAX,CAAxB,CADA,GAEE,YAFF,GAGE,YANR;QAOD,CARD;;QASA,IAAI0I,IAAI,CAACC,SAAT,EAAoB;UAClB,IAAI,mBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEJ,OAAR,MAAe,IAAf,IAAeuB,aAAf,GAAe,MAAf,GAAeA,GAAE8C,OAAjB,MAAwB,IAAxB,IAAwBK,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEJ,MAA1B,MAAqC,CAAzC,EAA4C;YAC1C,KAAKpF,MAAL,CAAYoB,QAAZ,CACE,IAAIrG,aAAJ,CAAkB,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE+F,OAAR,MAAe,IAAf,IAAe2E,aAAf,GAAe,MAAf,GAAeA,GAAEN,OAAjB,MAAwB,IAAxB,IAAwBO,aAAxB,GAAwB,MAAxB,GAAwBA,GAAG,CAAH,CAA1C,CADF;UAGD;;UACD,IAAI7E,MAAM,CAAC+B,gBAAX,EACE,KAAK5C,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;UACF,KAAK+D,oBAAL,CAA0BoB,OAA1B,CAAkC+D,GAAlC;;UACA,OAAO,IAAI5G,mBAAJ,CAAwB2C,IAAxB,CAAP;QACD;;QACD,OAAO,IAAI3C,mBAAJ,EAAP;MACD,CA1BE,CADE,EA4BLxG,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CA5BL,CAAP;IA8BD,CA/BQ,CAFX,CAD0B,CAA5B;IAsCA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2H,aAAjB,CADR,EAEE1N,SAAS,CAAE4I,MAAD,IAAyB;MACjC,OAAO,KAAKhB,GAAL,CAAS+F,YAAT,CAAsB/E,MAAM,CAACC,OAA7B,EAAsCT,IAAtC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;;;QAChB,IAAIiE,GAAG,GACL,UACC,aAAM,CAACpE,OAAP,CAAeqE,OAAf,MAAsB,IAAtB,IAAsBhD,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEiD,MAAxB,IAAiC,CAAjC,GAAqC,GAArC,GAA2C,EAD5C,IAEA,mCAHF;;QAIA,IAAInE,IAAI,CAACC,SAAT,EAAoB;UAClB,IAAI,mBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEJ,OAAR,MAAe,IAAf,IAAeuB,aAAf,GAAe,MAAf,GAAeA,GAAE8C,OAAjB,MAAwB,IAAxB,IAAwBK,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEJ,MAA1B,MAAqC,CAAzC,EAA4C;YAC1C,KAAKpF,MAAL,CAAYoB,QAAZ,CACE,IAAIrG,aAAJ,CAAkB,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE+F,OAAR,MAAe,IAAf,IAAe2E,aAAf,GAAe,MAAf,GAAeA,GAAEN,OAAjB,MAAwB,IAAxB,IAAwBO,aAAxB,GAAwB,MAAxB,GAAwBA,GAAG,CAAH,CAA1C,CADF;UAGD;;UACD,IAAI7E,MAAM,CAAC+B,gBAAX,EACE,KAAK5C,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;UACF,KAAK+D,oBAAL,CAA0BoB,OAA1B,CAAkC+D,GAAlC;;UACA,OAAO,IAAI7G,mBAAJ,CAAwB4C,IAAxB,CAAP;QACD;;QACD,OAAO,IAAI5C,mBAAJ,EAAP;MACD,CAjBE,CADE,EAmBLvG,UAAU,CAAEuJ,GAAD,IAAa;;;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACAwE,KAAK,CAACC,OAAN,CAAc,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5D,KAAL,MAAU,IAAV,IAAUC,aAAV,GAAU,MAAV,GAAUA,GAAE4D,QAA1B,IACI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgCb,GAAG,CAACa,KAAJ,CAAU6D,QAAV,CAAmB,CAAnB,CAAhC,CADJ,GAEI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgC,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEA,KAAL,MAAU,IAAV,IAAUG,aAAV,GAAU,MAAV,GAAUA,GAAE0D,QAA5C,CAFJ;QAGA,OAAOlO,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CANS,CAnBL,CAAP;IA2BD,CA5BQ,CAFX,CAD0B,CAA5B;IAmCA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgI,qBAAjB,CADR,EAEE/N,SAAS,CAAE4I,MAAD,IAAgC;MACxC,OAAO,KAAKhB,GAAL,CAASoG,mBAAT,CAA6BpF,MAAM,CAACC,OAApC,EAA6CT,IAA7C,CACLjI,cAAc,CAAC,KAAK4H,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,CAAD,CADT,EAELrH,GAAG,CAAC,CAAC,CAACkJ,IAAD,EAAOiF,WAAP,CAAD,KAAoC;;;QACtC,MAAMC,KAAK,GAAG,kBAAM,CAACrF,OAAP,MAAc,IAAd,IAAcqB,aAAd,GAAc,MAAd,GAAcA,GAAEgD,OAAhB,MAAuB,IAAvB,IAAuB9C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE+C,MAAvC;;QACA,IAAIe,KAAK,GAAG,EAAZ,EAAgB;UACd,IAAIjB,GAAG,GACL,UACC,aAAM,CAACpE,OAAP,CAAeqE,OAAf,MAAsB,IAAtB,IAAsBK,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEJ,MAAxB,IAAiC,CAAjC,GAAqC,GAArC,GAA2C,EAD5C,IAEA,gBAHF;UAIAF,GAAG,IACD,YAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEI,cAAb,MAAgC,CAAhC,GACI,oBADJ,GAEI,mBAAM,CAACxE,OAAP,MAAc,IAAd,IAAc2E,aAAd,GAAc,MAAd,GAAcA,GAAEF,OAAhB,MAAuB,IAAvB,IAAuBG,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEN,MAAzB,KACA,EAAC,kBAAM,CAACtE,OAAP,MAAc,IAAd,IAAcsF,aAAd,GAAc,MAAd,GAAcA,GAAEb,OAAhB,MAAuB,IAAvB,IAAuBc,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE3F,QAAF,CAAWnI,UAAX,CAAxB,CADA,GAEE,sBAFF,GAGE,YANR;;UAOA,IAAI0I,IAAI,CAACC,SAAT,EAAoB;YAClB,IAAIL,MAAM,CAAC+B,gBAAX,EACE,KAAK5C,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;YACF,KAAK+D,oBAAL,CAA0BoB,OAA1B,CAAkC+D,GAAlC;;YACA,OAAO,IAAI1G,0BAAJ,CAA+ByC,IAA/B,CAAP;UACD;QACF,CAlBD,MAkBO;UACL,KAAKjB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;UACA,OAAO,IAAIwC,0BAAJ,CAA+ByC,IAA/B,CAAP;QACD;;QACD,OAAO,IAAIzC,0BAAJ,EAAP;MACD,CAzBE,CAFE,EA4BL1G,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CA5BL,CAAP;IA8BD,CA/BQ,CAFX,CADiC,CAAnC;IAsCA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsI,kBAAjB,CADR,EAEErO,SAAS,CAAE4I,MAAD,IAA6B;MACrC,OAAO,KAAKhB,GAAL,CAAS0G,gBAAT,CAA0B1F,MAAM,CAACC,OAAjC,EAA0CT,IAA1C,CACLjI,cAAc,CAAC,KAAK4H,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,CAAD,CADT,EAELrH,GAAG,CAAC,CAAC,CAACkJ,IAAD,EAAOiF,WAAP,CAAD,KAAoC;;;QACtC,MAAMC,KAAK,GAAG,kBAAM,CAACrF,OAAP,MAAc,IAAd,IAAcqB,aAAd,GAAc,MAAd,GAAcA,GAAEgD,OAAhB,MAAuB,IAAvB,IAAuB9C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE+C,MAAvC;;QACA,IAAIe,KAAK,GAAG,EAAZ,EAAgB;UACd,IAAIjB,GAAG,GACL,UACC,mBAAM,CAACpE,OAAP,MAAc,IAAd,IAAc0E,aAAd,GAAc,MAAd,GAAcA,GAAEL,OAAhB,MAAuB,IAAvB,IAAuBM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEL,MAAzB,IAAkC,CAAlC,GAAsC,GAAtC,GAA4C,EAD7C,IAEA,gBAHF;UAKAF,GAAG,IACD,YAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEI,cAAb,MAAgC,CAAhC,GACI,UADJ,GAEI,mBAAM,CAACxE,OAAP,MAAc,IAAd,IAAc4E,aAAd,GAAc,MAAd,GAAcA,GAAEH,OAAhB,MAAuB,IAAvB,IAAuBa,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEI,QAAF,EAAvB,MAAwCjO,UAAxC,GACE,YADF,GAEE,YALR;;UAOA,IAAI0I,IAAI,CAACC,SAAT,EAAoB;YAClB,KAAKlB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;YACA,KAAK+D,oBAAL,CAA0BoB,OAA1B,CAAkC+D,GAAlC;;YACA,OAAO,IAAIlM,uBAAJ,CAA4BiI,IAA5B,CAAP;UACD;QACF,CAlBD,MAkBO;UACL,KAAKjB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;UACA,OAAO,IAAIhD,uBAAJ,CAA4BiI,IAA5B,CAAP;QACD;;QACD,OAAO,IAAIjI,uBAAJ,EAAP;MACD,CAzBE,CAFE,EA4BLlB,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CA5BL,CAAP;IA8BD,CA/BQ,CAFX,CAD8B,CAAhC;IAsCA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACyI,iBAAjB,CADR,EAEExO,SAAS,CAAE4I,MAAD,IAA4B;MACpC,OAAO,KAAKhB,GAAL,CAASiC,WAAT,CAAqBjB,MAAM,CAACkB,IAA5B,EAAkC1B,IAAlC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,mCADF;;UAGA,OAAO,IAAIlD,sBAAJ,CAA2BgD,IAAI,CAACF,IAAhC,CAAP;QACD,CALD,MAKO;UACL,KAAKf,MAAL,CAAYoB,QAAZ,CAAqB,IAAI/I,UAAJ,EAArB;;UACA,KAAK0H,oBAAL,CAA0BiC,IAA1B,CAA+B,GAAGf,IAAI,CAACgB,OAAO,EAA9C;;UACA,OAAO,IAAIjG,aAAJ,EAAP;QACD;MACF,CAXE,CADE,EAaLlE,UAAU,CAAEuJ,GAAD,IAAa;;;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACAwE,KAAK,CAACC,OAAN,CAAc,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5D,KAAL,MAAU,IAAV,IAAUC,aAAV,GAAU,MAAV,GAAUA,GAAE4D,QAA1B,IACI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgCb,GAAG,CAACa,KAAJ,CAAU6D,QAAV,CAAmB,CAAnB,CAAhC,CADJ,GAEI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgC,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEA,KAAL,MAAU,IAAV,IAAUG,aAAV,GAAU,MAAV,GAAUA,GAAE0D,QAA5C,CAFJ;QAGA,OAAOlO,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CANS,CAbL,CAAP;IAqBD,CAtBQ,CAFX,CAD8B,CAAhC;IA6BA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0I,qBAAjB,CADR,EAEEzO,SAAS,CAAE4I,MAAD,IAAgC;MACxC,OAAO,KAAKhB,GAAL,CAAS8G,mBAAT,CAA6B9F,MAAM,CAACC,OAApC,EAA6CT,IAA7C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;;;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,IAAID,IAAI,CAACF,IAAT,EAAe;YACb,IAAI,UAAI,CAACA,IAAL,MAAS,IAAT,IAASoB,aAAT,GAAS,MAAT,GAASA,GAAEyE,QAAf,EAAyB;cACvB,MAAMC,SAAS,GAAG,WAAI,CAAC5E,OAAL,MAAY,IAAZ,IAAYI,aAAZ,GAAY,MAAZ,GAAYA,GAAEyE,SAAd,KAA2B,EAA7C;;cACA,KAAK/G,oBAAL,CAA0BoB,OAA1B,CACE,GAAG0F,SAAS,4BADd;;cAGA,OAAO,IAAIpN,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;YACD,CAND,MAMO;cACL,KAAKhB,oBAAL,CAA0BoB,OAA1B,CACE,6BADF;;cAGA,OAAO,IAAI1H,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;YACD;UACF,CAbD,MAaO;YACL,KAAKhB,oBAAL,CAA0BmC,KAA1B,CAAgCjB,IAAI,CAACgB,OAArC;UACD;;UACD,OAAO,IAAIjG,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CArBE,CADE,EAuBLnE,UAAU,CAAEuJ,GAAD,IAAa;;;QACtBwE,KAAK,CAACC,OAAN,CAAc,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5D,KAAL,MAAU,IAAV,IAAUC,aAAV,GAAU,MAAV,GAAUA,GAAE4D,QAA1B,IACI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgCb,GAAG,CAACa,KAAJ,CAAU6D,QAAV,CAAmB,CAAnB,CAAhC,CADJ,GAEI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgC,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEA,KAAL,MAAU,IAAV,IAAUG,aAAV,GAAU,MAAV,GAAUA,GAAE0D,QAA5C,CAFJ;QAGA,OAAOlO,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CALS,CAvBL,CAAP;IA8BD,CA/BQ,CAFX,CADiC,CAAnC;IAsCA,mCAA8B7J,YAAY,CAAC,MACzC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC+I,6BAAjB,CADR,EAEE9O,SAAS,CAAE4I,MAAD,IAAuC;MAC/C,OAAO,KAAKhB,GAAL,CAASmH,2BAAT,CAAqCnG,MAAM,CAACC,OAA5C,EAAqDT,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;;;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,IAAID,IAAI,CAACF,IAAT,EAAe;YACb,IAAI,UAAI,CAACA,IAAL,MAAS,IAAT,IAASoB,aAAT,GAAS,MAAT,GAASA,GAAEyE,QAAf,EAAyB;cACvB,MAAMC,SAAS,GAAG,WAAI,CAAC5E,OAAL,MAAY,IAAZ,IAAYI,aAAZ,GAAY,MAAZ,GAAYA,GAAEyE,SAAd,KAA2B,EAA7C;;cACA,KAAK/G,oBAAL,CAA0BoB,OAA1B,CACE,GAAG0F,SAAS,4BADd;;cAGA,OAAO,IAAIpN,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;YACD,CAND,MAMO;cACL,KAAKhB,oBAAL,CAA0BoB,OAA1B,CACE,6BADF;;cAGA,OAAO,IAAI1H,kBAAJ,CAAuBwH,IAAI,CAACF,IAA5B,CAAP;YACD;UACF,CAbD,MAaO;YACL,KAAKhB,oBAAL,CAA0BmC,KAA1B,CAAgCjB,IAAI,CAACgB,OAArC;UACD;;UACD,OAAO,IAAIjG,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CArBE,CADE,EAuBLnE,UAAU,CAAEuJ,GAAD,IAAa;;;QACtBwE,KAAK,CAACC,OAAN,CAAc,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5D,KAAL,MAAU,IAAV,IAAUC,aAAV,GAAU,MAAV,GAAUA,GAAE4D,QAA1B,IACI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgCb,GAAG,CAACa,KAAJ,CAAU6D,QAAV,CAAmB,CAAnB,CAAhC,CADJ,GAEI,KAAKhG,oBAAL,CAA0BmC,KAA1B,CAAgC,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEA,KAAL,MAAU,IAAV,IAAUG,aAAV,GAAU,MAAV,GAAUA,GAAE0D,QAA5C,CAFJ;QAGA,OAAOlO,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CALS,CAvBL,CAAP;IA8BD,CA/BQ,CAFX,CADwC,CAA1C;IAsCA,oCAA+B7J,YAAY,CAAC,MAC1C,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACiJ,iCAAjB,CADR,EAEElP,GAAG,CAAE8I,MAAD,IAA2CA,MAA5C,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CACJqH,2BADI,CACwBnG,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEoG,UAD9B,EAC0CpG,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEqG,QADhD,EAEJ/G,IAFI,CAGHtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI7D,gCAAJ,CAAqC4D,IAArC,CAAP;QACD;;QACD,OAAO,IAAI5D,gCAAJ,EAAP;MACD,CALE,CAHA,EASHvF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATP,CAAP;IAWD,CAZQ,CAHX,CADyC,CAA3C;IAoBA,uBAAkB7J,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACqJ,aAAjB,CADR,EAEEpP,SAAS,CAAE4I,MAAD,IAAwB;MAChC,OAAO,KAAKhB,GAAL,CAASyH,cAAT,CAAwBzG,MAAM,CAACE,IAA/B,EAAqCV,IAArC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAInD,kBAAJ,CAAuBkD,IAAI,CAACF,IAA5B,CAAP;QACD;;QACD,OAAO,IAAIhD,kBAAJ,EAAP;MACD,CALE,CADE,EAOLjG,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAFX,CAD4B,CAA9B;IAiBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACuJ,iBAAjB,CADR,EAEEtP,SAAS,CAAE4I,MAAD,IAA2B;MACnC,OAAO,KAAKhB,GAAL,CAASyH,cAAT,CAAwBzG,MAAM,CAACE,IAA/B,EAAqCV,IAArC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIpD,qBAAJ,CAA0BmD,IAAI,CAACF,IAA/B,CAAP;QACD;;QACD,OAAO,IAAIjD,qBAAJ,EAAP;MACD,CALE,CADE,EAOLhG,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAFX,CAD+B,CAAjC;IAiBA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwJ,eAAjB,CADR,EAEEvP,SAAS,CAAE4I,MAAD,IAA+B;MACvC,OAAO,KAAKhB,GAAL,CAAS4H,kBAAT,CAA4B5G,MAAM,CAAC4B,EAAnC,EAAuC5B,MAAM,CAACC,OAA9C,EAAuDT,IAAvD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,kCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIrG,aAAJ,CAAkB8F,MAAM,CAACC,OAAP,CAAeyD,MAAjC,CAArB;;UACA,KAAKvE,MAAL,CAAYoB,QAAZ,CAAqB,IAAI5B,kBAAJ,EAArB;;UACA,OAAO,IAAIN,yBAAJ,CAA8B2B,MAAM,CAACC,OAAP,CAAe4G,SAA7C,CAAP;QACD;;QACD,OAAO,IAAIxI,yBAAJ,EAAP;MACD,CAVE,CADE,EAYLpH,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAZL,CAAP;IAcD,CAfQ,CAFX,CADgC,CAAlC;IAsBA,2BAAsB7J,YAAY,CAChC,MACE,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2J,eAAjB,CADR,EAEE1P,SAAS,CAAE4I,MAAD,IAA+B;MACvC,OAAO,KAAKhB,GAAL,CAAS+H,mBAAT,CAA6B/G,MAAM,CAACC,OAApC,EAA6CT,IAA7C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,oCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIrG,aAAJ,CAAkB8F,MAAM,CAACC,OAAP,CAAeyD,MAAjC,CAArB;QACD;MACF,CAPE,CADE,EASLzM,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATL,CAAP;IAWD,CAZQ,CAFX,CAF8B,EAkBhC;MAAED,QAAQ,EAAE;IAAZ,CAlBgC,CAAlC;IAqBA,uBAAkB5J,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6J,kBAAjB,CADR,EAEE9P,GAAG,CAAE8I,MAAD,IAA8BA,MAA/B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASiI,cAAT,CAAwB/G,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEgH,aAA9B,EAA6C1H,IAA7C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI3D,uBAAJ,CAA4B0D,IAAI,CAACF,IAAjC,CAAP;QACD;;QACD,OAAO,IAAIxD,uBAAJ,EAAP;MACD,CALE,CADE,EAOLzF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD4B,CAA9B;IAkBA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgK,qBAAjB,CADR,EAEEjQ,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASoI,gBAAT,GAA4B5H,IAA5B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIzD,yBAAJ,CAA8BwD,IAAI,CAACF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAItD,yBAAJ,EAAP;MACD,CALE,CADE,EAOL3F,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD8B,CAAhC;IAkBA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkK,mBAAjB,CADR,EAEEnQ,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsI,eAAT,CAAyBpH,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEgH,aAA/B,EAA8C1H,IAA9C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI1D,wBAAJ,CAA6ByD,IAAI,CAACF,IAAlC,CAAP;QACD;;QACD,OAAO,IAAIvD,wBAAJ,EAAP;MACD,CALE,CADE,EAOL1F,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD6B,CAA/B;IAkBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoK,sBAAjB,CADR,EAEErQ,GAAG,CAAE8I,MAAD,IAAiCA,MAAlC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwI,iBAAT,GAA6BhI,IAA7B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIxD,0BAAJ,CAA+BuD,IAAI,CAACF,IAApC,CAAP;QACD;;QACD,OAAO,IAAIrD,0BAAJ,EAAP;MACD,CALE,CADE,EAOL5F,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD+B,CAAjC;IAkBA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsK,yBAAjB,CADR,EAEEvQ,GAAG,CAAE8I,MAAD,IAAoCA,MAArC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CACJ0I,oBADI,CACiBxH,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEoG,UADvB,EACmCpG,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEqG,QADzC,EAEJ/G,IAFI,CAGHtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI1G,yBAAJ,CAA8ByG,IAA9B,CAAP;QACD;;QACD,OAAO,IAAIzG,yBAAJ,EAAP;MACD,CALE,CAHA,EASH1C,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATP,CAAP;IAWD,CAZQ,CAHX,CADkC,CAApC;IAoBA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwK,mBAAjB,CADR,EAEEzQ,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS4I,eAAT,CAAyB1H,IAAI,CAACoG,UAA9B,EAA0CpG,IAAI,CAACqG,QAA/C,EAAyD/G,IAAzD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIzG,wBAAJ,CAA6BwG,IAA7B,CAAP;QACD;;QACD,OAAO,IAAIxG,wBAAJ,EAAP;MACD,CALE,CADE,EAOL3C,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADiC,CAAnC;IAkBA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0K,wBAAjB,CADR,EAEE3Q,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS8I,WAAT,GAAuBtI,IAAvB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI5F,wBAAJ,CAA6B2F,IAAI,CAACF,IAAlC,CAAP;QACD;;QACD,OAAO,IAAIzF,wBAAJ,EAAP;MACD,CALE,CADE,EAOLxD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD6B,CAA/B;IAkBA,2BAAsB7J,YAAY,CAChC,MACE,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4K,mBAAjB,CADR,EAEE7Q,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,KAAKf,MAAL,CAAYoB,QAAZ,CACE,IAAI/H,yBAAJ,CAA8B0H,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE0B,EAApC,EAAwC1B,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAED,OAA9C,CADF;;MAGA,OAAO,KAAKjB,GAAL,CAASgJ,kBAAT,CAA4B9H,IAAI,CAAC0B,EAAjC,EAAqC1B,IAAI,CAACD,OAA1C,EAAmDT,IAAnD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc,CAAI,CAAnB,CADE,EAELnJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAFL,CAAP;IAID,CARQ,CAHX,CAF8B,EAehC;MAAED,QAAQ,EAAE;IAAZ,CAfgC,CAAlC;IAkBA,+BAA0B5J,YAAY,CACpC,MACE,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC8K,wBAAjB,CADR,EAEE/Q,GAAG,CAAE8I,MAAD,IAAoCA,MAArC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,KAAKf,MAAL,CAAYoB,QAAZ,CACE,IAAIjI,6BAAJ,CAAkC4H,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAED,OAAxC,CADF;;MAGA,OAAO,KAAKjB,GAAL,CAASkJ,sBAAT,CAAgChI,IAAI,CAACD,OAArC,EAA8CT,IAA9C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc,CAAI,CAAnB,CADE,EAELnJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAFL,CAAP;IAID,CARQ,CAHX,CAFkC,EAepC;MAAED,QAAQ,EAAE;IAAZ,CAfoC,CAAtC;IAkBA,2BAAsB5J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgL,qBAAjB,CADR,EAEEjR,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASoJ,kBAAT,CAA4BlI,IAAI,CAACD,OAAjC,EAA0CT,IAA1C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,sBAAlC;;UACA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAI5B,kBAAJ,EAArB;;UACA,OAAO,IAAItB,yBAAJ,EAAP;QACD;;QACD,KAAK8B,MAAL,CAAYoB,QAAZ,CAAqB,IAAIlD,yBAAJ,EAArB;;QACA,OAAO,IAAIjC,oBAAJ,EAAP;MACD,CARE,CADE,EAULnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAVL,CAAP;IAYD,CAbQ,CAHX,CADgC,CAAlC;IAqBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkL,eAAjB,CADR,EAEEnR,GAAG,CAAE8I,MAAD,IAA4BA,MAA7B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsJ,YAAT,GAAwB9I,IAAxB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI/D,qBAAJ,CAA0B8D,IAAI,CAACF,IAA/B,CAAP;QACD;;QACD,OAAO,IAAI5D,qBAAJ,EAAP;MACD,CALE,CADE,EAOLrF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD0B,CAA5B;IAkBA,sBAAiB7J,YAAY,CAAC,MAC5B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoL,iBAAjB,CADR,EAEErR,GAAG,CAAE8I,MAAD,IAA6BA,MAA9B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwJ,aAAT,GAAyBhJ,IAAzB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIjG,sBAAJ,CAA2BgG,IAAI,CAACF,IAAhC,CAAP;QACD;;QACD,OAAO,IAAI9F,sBAAJ,EAAP;MACD,CALE,CADE,EAOLnD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD2B,CAA7B;IAkBA,sBAAiB7J,YAAY,CAAC,MAC5B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsL,iBAAjB,CADR,EAEEvR,GAAG,CAAE8I,MAAD,IAA6BA,MAA9B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS0J,aAAT,GAAyBlJ,IAAzB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIxE,sBAAJ,CAA2BuE,IAAI,CAACF,IAAhC,CAAP;QACD;;QACD,OAAO,IAAIrE,sBAAJ,EAAP;MACD,CALE,CADE,EAOL5E,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD2B,CAA7B;IAkBA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwL,oBAAjB,CADR,EAEEzR,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS4J,gBAAT,GAA4BpJ,IAA5B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI9F,yBAAJ,CAA8B6F,IAAI,CAACF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAI3F,yBAAJ,EAAP;MACD,CALE,CADE,EAOLtD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD8B,CAAhC;IAkBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0L,0BAAjB,CADR,EAEE3R,GAAG,CAAE8I,MAAD,IAAqCA,MAAtC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS8J,qBAAT,GAAiCtJ,IAAjC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIrE,8BAAJ,CAAmCoE,IAAI,CAACF,IAAxC,CAAP;QACD;;QACD,OAAO,IAAIlE,8BAAJ,EAAP;MACD,CALE,CADE,EAOL/E,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADmC,CAArC;IAkBA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4L,sBAAjB,CADR,EAEE7R,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASgK,kBAAT,GAA8BxJ,IAA9B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI/F,2BAAJ,CAAgC8F,IAAI,CAACF,IAArC,CAAP;QACD;;QACD,OAAO,IAAI5F,2BAAJ,EAAP;MACD,CALE,CADE,EAOLrD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADgC,CAAlC;IAkBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC8L,qBAAjB,CADR,EAEE/R,GAAG,CAAE8I,MAAD,IAAiCA,MAAlC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASkK,iBAAT,GAA6B1J,IAA7B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIpE,0BAAJ,CAA+BmE,IAAI,CAACF,IAApC,CAAP;QACD;;QACD,OAAO,IAAIjE,0BAAJ,EAAP;MACD,CALE,CADE,EAOLhF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD+B,CAAjC;IAkBA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgM,oBAAjB,CADR,EAEEjS,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASoK,YAAT,GAAwB5J,IAAxB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI/E,yBAAJ,CAA8B8E,IAAI,CAACF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAI5E,yBAAJ,EAAP;MACD,CALE,CADE,EAOLrE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD8B,CAAhC;IAkBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkM,gBAAjB,CADR,EAEEnS,GAAG,CAAE8I,MAAD,IAA4BA,MAA7B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsK,YAAT,GAAwB9J,IAAxB,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIlE,qBAAJ,CAA0BiE,IAAI,CAACF,IAA/B,CAAP;QACD;;QACD,OAAO,IAAI/D,qBAAJ,EAAP;MACD,CALE,CADE,EAOLlF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD0B,CAA5B;IAkBA,oBAAe7J,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoM,YAAjB,CADR,EAEEnS,SAAS,CAAE4I,MAAD,IACR,KAAKhB,GAAL,CAASwK,WAAT,CAAqBxJ,MAAM,CAACC,OAA5B,EAAqCT,IAArC,CACEpI,SAAS,CAAEgJ,IAAD,IAAc;MACtB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,sBAAlC;;QACA,OAAO,CACL,IAAI5H,kBAAJ,EADK,EAEL,IAAIyC,aAAJ,CAAkB,IAAlB,EAAwB6E,MAAM,CAACyJ,SAA/B,CAFK,CAAP;MAID;;MACD,OAAO1S,EAAE,CAAC,IAAIoE,aAAJ,EAAD,CAAT;IACD,CATQ,CADX,EAWElE,UAAU,CAAEuJ,GAAD,IAAQ;MACjB,KAAKtB,oBAAL,CAA0BmC,KAA1B,CAAgC,wBAAhC;;MACA,OAAOtK,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAT;IACD,CAHS,CAXZ,CADO,CAFX,CADyB,CAA3B;IAwBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACuM,aAAjB,CADR,EAEExS,GAAG,CAAE8I,MAAD,IAA0BA,MAA3B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS2K,YAAT,CAAsBzJ,IAAI,CAACD,OAA3B,EAAoCT,IAApC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;;;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,uBAAlC;;UACA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAI7C,mBAAJ,EAArB;;UACA,OAAO,IAAIvC,aAAJ,CACL,IADK,EAEL,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyO,IAAV,MAAc,IAAd,IAActI,aAAd,GAAc,MAAd,GAAcA,GAAEzB,QAAF,CAAW,UAAX,CAFT,CAAP;QAID;;QACD,OAAO,IAAI1E,aAAJ,CACL,IADK,EAEL,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyO,IAAV,MAAc,IAAd,IAAcpI,aAAd,GAAc,MAAd,GAAcA,GAAE3B,QAAF,CAAW,UAAX,CAFT,CAAP;MAID,CAbE,CADE,EAeL5I,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAfL,CAAP;IAiBD,CAlBQ,CAHX,CAD0B,CAA5B;IA0BA,uCAAkC7J,YAAY,CAAC,MAC7C,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0M,qCAAjB,CADR,EAEE3S,GAAG,CAAE8I,MAAD,IAAgDA,MAAjD,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IACR,KAAKd,aAAL,CAAmB0K,aAAnB,CAAiC5J,IAAI,CAACD,OAAtC,EAA+CT,IAA/C,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAI9D,yCAAJ,CAA8C6D,IAA9C,CAAP;MACD;;MACD,OAAO,IAAI7D,yCAAJ,CAA8C,EAA9C,CAAP;IACD,CALE,CADL,EAOEtF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CAD4C,CAA9C;IAkBA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4M,qBAAjB,CADR,EAEE7S,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASgL,gBAAT,GAA4BxK,IAA5B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAItD,yBAAJ,CAA8BqD,IAAI,CAACF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAInD,yBAAJ,EAAP;MACD,CALE,CADE,EAOL9F,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD8B,CAAhC;IAkBA,mBAAc7J,YAAY,CAAC,MACzB,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC8M,WAAjB,CADR,EAEE7S,SAAS,CAAE4I,MAAD,IAAuB;MAC/B,OAAO,KAAKhB,GAAL,CAASkL,UAAT,CAAoBlK,MAAM,CAACC,OAA3B,EAAoCT,IAApC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKlB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIzD,kBAAJ,EAArB;;UACA,KAAKoC,oBAAL,CAA0BoB,OAA1B,CACE,kCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAInI,iBAAJ,EAArB;;UACA,OAAO,IAAI+C,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CAVE,CADE,EAYLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAZL,CAAP;IAcD,CAfQ,CAFX,CADwB,CAA1B;IAsBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgN,aAAjB,CADR,EAEE/S,SAAS,CAAE4I,MAAD,IAAyB;MACjC,OAAO,KAAKhB,GAAL,CAASoL,YAAT,CAAsBpK,MAAM,CAACC,OAA7B,EAAsCT,IAAtC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,sCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIrI,mBAAJ,EAArB;;UACA,OAAO,IAAIiD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CATE,CADE,EAWLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAdQ,CAFX,CAD0B,CAA5B;IAqBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkN,sBAAjB,CADR,EAEEnT,GAAG,CAAE8I,MAAD,IAAiCA,MAAlC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsL,cAAT,GAA0B9K,IAA1B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAInH,0BAAJ,CAA+BkH,IAAI,CAACF,IAApC,CAAP;QACD;;QACD,OAAO,IAAIhH,0BAAJ,CAA+B,EAA/B,CAAP;MACD,CALE,CADE,EAOLjC,UAAU,CAAEuJ,GAAD,IAAa;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACA,OAAOxJ,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CAHS,CAPL,CAAP;IAYD,CAbQ,CAHX,CAD+B,CAAjC;IAqBA,mCAA8B7J,YAAY,CAAC,MACzC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoN,gCAAjB,CADR,EAEErT,GAAG,CAAE8I,MAAD,IAA0CA,MAA3C,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwL,uBAAT,GAAmChL,IAAnC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIpH,mCAAJ,CAAwCmH,IAAI,CAACF,IAA7C,CAAP;QACD;;QACD,OAAO,IAAIjH,mCAAJ,CAAwC,EAAxC,CAAP;MACD,CALE,CADE,EAOLhC,UAAU,CAAEuJ,GAAD,IAAa;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACA,OAAOxJ,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CAHS,CAPL,CAAP;IAYD,CAbQ,CAHX,CADwC,CAA1C;IAqBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsN,0BAAjB,CADR,EAEEvT,GAAG,CAAE8I,MAAD,IAAqCA,MAAtC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS0L,qBAAT,GAAiClL,IAAjC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI7G,8BAAJ,CAAmC4G,IAAI,CAACF,IAAxC,CAAP;QACD;;QACD,OAAO,IAAI1G,8BAAJ,CAAmC,EAAnC,CAAP;MACD,CALE,CADE,EAOLvC,UAAU,CAAEuJ,GAAD,IAAa;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACA,OAAOxJ,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CAHS,CAPL,CAAP;IAYD,CAbQ,CAHX,CADmC,CAArC;IAqBA,uCAAkC7J,YAAY,CAAC,MAC7C,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwN,oCAAjB,CADR,EAEEzT,GAAG,CAAE8I,MAAD,IAA8CA,MAA/C,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS4L,8BAAT,GAA0CpL,IAA1C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI9G,uCAAJ,CAA4C6G,IAAI,CAACF,IAAjD,CAAP;QACD;;QACD,OAAO,IAAI3G,uCAAJ,CAA4C,EAA5C,CAAP;MACD,CALE,CADE,EAOLtC,UAAU,CAAEuJ,GAAD,IAAa;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACA,OAAOxJ,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CAHS,CAPL,CAAP;IAYD,CAbQ,CAHX,CAD4C,CAA9C;IAqBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0N,gCAAjB,CADR,EAEEzT,SAAS,CAAE4I,MAAD,IAAkC;MAC1C,OAAO,KAAKhB,GAAL,CAAS8L,qBAAT,CAA+B9K,MAAM,CAACC,OAAtC,EAA+CT,IAA/C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKlB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIpF,aAAJ,EAArB;;UACA,IAAI,CAACiF,IAAI,CAAC2K,KAAL,CAAWxG,MAAhB,EAAwB;YACtB,KAAKrF,oBAAL,CAA0BoB,OAA1B,CAAkC,4BAAlC;UACD;;UACD,OAAO,IAAIhC,4BAAJ,CAAiC8B,IAAjC,CAAP;QACD;;QACD,OAAO,IAAI9B,4BAAJ,EAAP;MACD,CATE,CADE,EAWLrH,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAdQ,CAFX,CADmC,CAArC;IAqBA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6N,uBAAjB,CADR,EAEE9T,GAAG,CAAE8I,MAAD,IAAmCA,MAApC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASiM,mBAAT,GAA+BzL,IAA/B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI3G,4BAAJ,CAAiC0G,IAAI,CAACF,IAAtC,CAAP;QACD;;QACD,OAAO,IAAIxG,4BAAJ,CAAiC,EAAjC,CAAP;MACD,CALE,CADE,EAOLzC,UAAU,CAAEuJ,GAAD,IAAa;QACtBxJ,UAAU,CAACwJ,GAAD,CAAV;QACA,OAAOxJ,UAAU,CAAC,MAAMwJ,GAAP,CAAjB;MACD,CAHS,CAPL,CAAP;IAYD,CAbQ,CAHX,CADiC,CAAnC;IAqBA,uBAAkB7J,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC+N,kBAAjB,CADR,EAEEhU,GAAG,CAAE8I,MAAD,IAA6BA,MAAM,CAACC,OAArC,CAFL,EAGE7I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKd,aAAL,CAAmB0K,aAAnB,CAAiC5J,IAAjC,EAAuCV,IAAvC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIvF,sBAAJ,CAA2BsF,IAAI,CAACF,IAAhC,CAAP;QACD;;QACD,OAAO,IAAIpF,sBAAJ,EAAP;MACD,CALE,CADE,EAOL7D,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD4B,CAA9B;IAkBA,kCAA6B7J,YAAY,CAAC,MACxC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgO,0BAAjB,CADR,EAEEjU,GAAG,CAAE8I,MAAD,IAAyCA,MAAM,CAACC,OAAjD,CAFL,EAGE7I,SAAS,CAAE8I,IAAD,IAAc;MACtB,IAAIJ,aAAJ;;MACA,KAAKX,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,EAAsCiG,SAAtC,CAAiDtE,IAAD,IAAc;QAC5DJ,aAAa,mCACRI,IADQ,GACJ;UACPkL,IAAI,EAAE;QADC,CADI,CAAb;MAID,CALD;;MAMA,OAAO,KAAKhM,aAAL,CAAmBiM,wBAAnB,CAA4CvL,aAA5C,EAA2DN,IAA3D,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI7E,kCAAJ,CAAuC4E,IAAI,CAACF,IAA5C,CAAP;QACD;;QACD,OAAO,IAAIjG,+BAAJ,EAAP;MACD,CALE,CADE,EAOLhD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAjBQ,CAHX,CADuC,CAAzC;IAyBA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACmO,mBAAjB,CADR,EAEEpU,GAAG,CAAE8I,MAAD,IAAmCA,MAAM,CAACC,OAA3C,CAFL,EAGE7I,SAAS,CAAE8I,IAAD,IAAc;MACtB,IAAIJ,aAAJ;;MACA,KAAKX,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,EAAsCiG,SAAtC,CAAiDtE,IAAD,IAAc;QAC5DJ,aAAa,mCACRI,IADQ,GACJ;UACPkL,IAAI,EAAE;QADC,CADI,CAAb;MAID,CALD;;MAMA,OAAO,KAAKhM,aAAL,CAAmBiM,wBAAnB,CAA4CvL,aAA5C,EAA2DN,IAA3D,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIxF,4BAAJ,CAAiCuF,IAAI,CAACF,IAAtC,CAAP;QACD;;QACD,OAAO,IAAIrF,4BAAJ,EAAP;MACD,CALE,CADE,EAOL5D,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAjBQ,CAHX,CADiC,CAAnC;IAyBA,oBAAe7J,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoO,YAAjB,CADR,EAEEnU,SAAS,CAAE4I,MAAD,IAAwB;MAChC,IAAIwL,qBAAJ;;MACA,KAAKrM,MAAL,CACGtI,MADH,CACU4H,4BADV,EAEGe,IAFH,CAEQnI,IAAI,CAAC,CAAD,CAFZ,EAGGmN,SAHH,CAGciH,SAAD,IAAmB;QAC5BD,qBAAqB,GAAGC,SAAxB;MACD,CALH;;MAMA,OAAO,CACL/L,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IACI,KAAKb,GAAL,CAAS0M,UAAT,CAAoB1L,MAAM,CAACC,OAA3B,CADJ,GAEIuL,qBAAqB,IACrB9L,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,qBAAlC,CADA,GAEE,KAAKb,GAAL,CAAS2M,gBAAT,CAA0B3L,MAAM,CAACC,OAAjC,CAFF,GAGE,KAAKjB,GAAL,CAAS0M,UAAT,CAAoB1L,MAAM,CAACC,OAA3B,CAND,EAOLT,IAPK,CAQLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,0CADF;;UAGA,OAAO,IAAIzH,kBAAJ,CAAuBuH,IAAvB,CAAP;QACD;;QACD,OAAO,IAAIvH,kBAAJ,EAAP;MACD,CARE,CARE,EAiBL5B,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAjBL,CAAP;IAmBD,CA3BQ,CAFX,CADyB,CAA3B;IAkCA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACyO,qBAAjB,CADR,EAEExU,SAAS,CAAE4I,MAAD,IAAiC;MACzC,OAAO,KAAKhB,GAAL,CAAS6M,oBAAT,CAA8B7L,MAAM,CAACC,OAArC,EAA8CT,IAA9C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI5H,2BAAJ,CAAgC2H,IAAhC,CAAP;QACD;;QACD,OAAO,IAAI3H,2BAAJ,EAAP;MACD,CALE,CADE,EAOLxB,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAFX,CADkC,CAApC;IAiBA,iCAA4B7J,YAAY,CAAC,MACvC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2O,0BAAjB,CADR,EAEE1U,SAAS,CAAE4I,MAAD,IAAqC;MAC7C,OAAO,KAAKhB,GAAL,CAAS+M,wBAAT,CAAkC/L,MAAM,CAACC,OAAzC,EAAkDT,IAAlD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI9H,+BAAJ,CAAoC6H,IAApC,CAAP;QACD;;QACD,OAAO,IAAI7H,+BAAJ,EAAP;MACD,CALE,CADE,EAOLtB,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAFX,CADsC,CAAxC;IAiBA,kCAA6B7J,YAAY,CAAC,MACxC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6O,mCAAjB,CADR,EAEE5U,SAAS,CAAE4I,MAAD,IAA4C;MACpD,MAAMC,OAAO,qBAAQD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,OAAhB,CAAb;MACAA,OAAO,CAACmL,IAAR,GAAe,gCAAf;MACA,OAAO,KAAKhM,aAAL,CAAmBiM,wBAAnB,CAA4CpL,OAA5C,EAAqDT,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,SAAV,EAAqB;UACnB,OAAO,IAAItG,sCAAJ,CAA2CqG,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAjD,CAAP;QACD;;QACD,OAAO,IAAInG,sCAAJ,EAAP;MACD,CALE,CADE,EAOL9C,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAZQ,CAFX,CADuC,CAAzC;IAmBA,kCAA6B7J,YAAY,CAAC,MACxC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC8O,gCAAjB,CADR,EAEE7U,SAAS,CAAE4I,MAAD,IAAyC;MACjD,MAAMC,OAAO,mCAIJD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,OAJJ,GAIW;QAAEmL,IAAI,EAAE;MAAR,CAJX,CAAb;;MAMA,IAAI,CAACnL,OAAO,CAACiM,sBAAb,EAAqC;QACnC,OAAOnV,EAAE,CAAC;UAAEoV,IAAI,EAAE;QAAR,CAAD,CAAT;MACD;;MAED,OAAO,KAAK/M,aAAL,CAAmBiM,wBAAnB,CAA4CpL,OAA5C,EAAqDT,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,SAAV,EAAqB;UACnB,OAAO,IAAIhE,mCAAJ,CAAwC+D,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAA9C,CAAP;QACD;;QACD,OAAO,IAAI7D,mCAAJ,EAAP;MACD,CALE,CADE,EAOLpF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CApBQ,CAFX,CADuC,CAAzC;IA2BA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACiP,iBAAjB,CADR,EAEEhV,SAAS,CAAE4I,MAAD,IAA4B;MACpC,OAAO,KAAKhB,GAAL,CAASqN,WAAT,CAAqBrM,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,OAA7B,EAAsCT,IAAtC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,OAAO,IAAIxI,sBAAJ,EAAP;MACD,CAFE,CADE,EAILX,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAJL,CAAP;IAMD,CAPQ,CAFX,CADgC,CAAlC;IAcA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACmP,uBAAjB,CADR,EAEElV,SAAS,CAAE4I,MAAD,IACR,KAAKf,eAAL,CAAqBsN,uBAArB,CAA6CvM,MAAM,CAACC,OAApD,EAA6DT,IAA7D,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,wCADF;MAGD;;MACD,OAAO,IAAI3E,iBAAJ,EAAP;IACD,CAPE,CADL,EASE1E,UAAU,CAAEoK,KAAD,IAAWtK,EAAE,CAAC,IAAIU,OAAJ,CAAY4J,KAAZ,CAAD,CAAd,CATZ,CADO,CAFX,CADkC,CAApC;IAmBA,uBAAkB1K,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACqP,mBAAjB,CADR,EAEEpV,SAAS,CAAE4I,MAAD,IACR,KAAKf,eAAL,CAAqBwN,eAArB,GAAuCjN,IAAvC,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIxB,6BAAJ;MACA,OAAO,IAAIhD,wBAAJ,CAA6BwE,IAA7B,CAAP;IACD,CAHE,CADL,EAKEnJ,UAAU,CAAEoK,KAAD,IAAWtK,EAAE,CAAC,IAAIU,OAAJ,CAAY4J,KAAZ,CAAD,CAAd,CALZ,CADO,CAFX,CAD4B,CAA9B;IAeA,2BAAsB1K,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACuP,oBAAjB,CADR,EAEEtV,SAAS,CAAE4I,MAAD,IACR,KAAKf,eAAL,CAAqB0N,kBAArB,CAAwC3M,MAAM,CAAC4B,EAA/C,EAAmDpC,IAAnD,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,0CADF;MAGD;;MACD,OAAO,IAAI3E,iBAAJ,EAAP;IACD,CAPE,CADL,EASE1E,UAAU,CAAEoK,KAAD,IAAWtK,EAAE,CAAC,IAAIU,OAAJ,CAAY4J,KAAZ,CAAD,CAAd,CATZ,CADO,CAFX,CADgC,CAAlC;IAmBA,2BAAsB1K,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACyP,oBAAjB,CADR,EAEExV,SAAS,CAAE4I,MAAD,IACR,KAAKf,eAAL,CAAqB4N,kBAArB,CAAwC7M,MAAM,CAACC,OAA/C,EAAwDT,IAAxD,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,0CADF;MAGD;;MACD,OAAO,IAAI3E,iBAAJ,EAAP;IACD,CAPE,CADL,EASE1E,UAAU,CAAEoK,KAAD,IAAWtK,EAAE,CAAC,IAAIU,OAAJ,CAAY4J,KAAZ,CAAD,CAAd,CATZ,CADO,CAFX,CADgC,CAAlC;IAmBA,wCAAmC1K,YAAY,CAAC,MAC9C,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2P,sCAAjB,CADR,EAEE5V,GAAG,CAAE8I,MAAD,IAA+CA,MAAhD,CAFL,EAGE5I,SAAS,CAAE4I,MAAD,IAAgB;MACxB,IAAIF,aAAJ;;MACA,KAAKX,MAAL,CAAYtI,MAAZ,CAAmB0H,iBAAnB,EAAsCiG,SAAtC,CAAiDtE,IAAD,IAAc;QAC5DJ,aAAa,GAAGI,IAAhB;MACD,CAFD;;MAGA,IAAI6M,cAAc,GAAQ,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEA,cAAf,IACtBjN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEiN,cADO,GAEtB,CAFJ;MAGA,IAAItI,cAAc,GAAQ,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEA,cAAf,IACtB3E,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAE2E,cADO,GAEtB,CAFJ;MAGA,IAAIuI,aAAa,GAAQ,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEA,aAAf,IACrBlN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEkN,aADM,GAErB,CAFJ;MAGA,IAAIC,mBAAmB,GACrB,CAAC,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEC,wBAAf,IACG,CACApN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEoN,wBADf,EAEApN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEqN,yBAFf,EAGArN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEsN,wBAHf,CADH,GAMG,IANJ,MAOC,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAED,yBAAf,IACG,CACArN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEqN,yBADf,EAEArN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEsN,wBAFf,CADH,GAKG,IAZJ,MAaC,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEA,wBAAf,IACG,CAACtN,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAEsN,wBAAhB,CADH,GAEG,IAfJ,CADF;MAiBAtN,aAAa,mCACRA,aADQ,GACK;QAChBsL,IAAI,EAAE,kCADU;QAEhB4B,aAFgB;QAGhBD,cAHgB;QAIhBtI,cAJgB;QAKhB6B,UAAU,EAAE,IALI;QAMhBC,QAAQ,EAAE,IANM;QAOhB0G;MAPgB,CADL,CAAb;MAUA,OAAO,KAAK7N,aAAL,CAAmBiM,wBAAnB,CAA4CvL,aAA5C,EAA2DN,IAA3D,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI1F,wCAAJ,CAA6CyF,IAAI,CAACF,IAAlD,CAAP;QACD;;QACD,OAAO,IAAIvF,wCAAJ,CAA6C,EAA7C,CAAP;MACD,CALE,CADE,EAOL1D,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAlDQ,CAHX,CAD6C,CAA/C;IA0DA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkQ,sBAAjB,CADR,EAEEnW,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsO,oBAAT,CAA8BpN,IAAI,CAACD,OAAnC,EAA4CT,IAA5C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CAAkC,sBAAlC;;UACA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIhD,2BAAJ,EAArB;;UACA,OAAO,IAAIpC,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIA,aAAJ,EAAP;MACD,CAPE,CADE,EASLlE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATL,CAAP;IAWD,CAZQ,CAHX,CADkC,CAApC;IAoBA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoQ,oBAAjB,CADR,EAEErW,GAAG,CAAE8I,MAAD,IAAgCA,MAAjC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CACJwO,gBADI,CACatN,IAAI,CAACoG,UADlB,EAC8BpG,IAAI,CAACqG,QADnC,EAC6CrG,IAAI,CAACuN,UADlD,EAEJjO,IAFI,CAGHtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIjH,yBAAJ,CAA8BgH,IAA9B,CAAP;QACD;;QACD,OAAO,IAAIhH,yBAAJ,EAAP;MACD,CALE,CAHA,EASHnC,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATP,CAAP;IAWD,CAZQ,CAHX,CADkC,CAApC;IAoBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACuQ,yBAAjB,CADR,EAEExW,GAAG,CAAE8I,MAAD,IAAqCA,MAAtC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS2O,qBAAT,GAAiCnO,IAAjC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIrD,8BAAJ,CAAmCoD,IAAI,CAACF,IAAxC,CAAP;QACD;;QACD,OAAO,IAAIlD,8BAAJ,EAAP;MACD,CALE,CADE,EAOL/F,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADmC,CAArC;IAkBA,4BAAuB7J,YAAY,CAAC,MAClC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACyQ,kBAAjB,CADR,EAEE1W,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IACR,KAAKlB,GAAL,CAAS6O,eAAT,CAAyB3N,IAAI,CAAC0B,EAA9B,EAAkCpC,IAAlC,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAIrF,2BAAJ,CAAgCoF,IAAI,CAACF,IAArC,CAAP;MACD;;MACD,OAAO,IAAIlF,2BAAJ,CAAgC,EAAhC,CAAP;IACD,CALE,CADL,EAOE/D,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CADiC,CAAnC;IAkBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2Q,8BAAjB,CADR,EAEE5W,GAAG,CAAE8I,MAAD,IAAyCA,MAA1C,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS+O,uBAAT,GAAmCvO,IAAnC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAItH,kCAAJ,CAAuCqH,IAAI,CAACF,IAA5C,CAAP;QACD;;QACD,OAAO,IAAInH,kCAAJ,EAAP;MACD,CALE,CADE,EAOL9B,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADmC,CAArC;IAkBA,mCAA8B7J,YAAY,CAAC,MACzC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6Q,+BAAjB,CADR,EAEE9W,GAAG,CAAE8I,MAAD,IAA0CA,MAA3C,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASiP,WAAT,CAAqB/N,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEgO,GAA3B,EAAgC1O,IAAhC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIrH,mCAAJ,CAAwCoH,IAAI,CAACF,IAA7C,CAAP;QACD;;QACD,OAAO,IAAIlH,mCAAJ,EAAP;MACD,CALE,CADE,EAOL/B,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADwC,CAA1C;IAkBA,uBAAkB7J,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgR,gBAAjB,CADR,EAEEjX,GAAG,CAAE8I,MAAD,IAA4BA,MAA7B,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASoP,cAAT,CAAwBlO,IAAI,CAACD,OAA7B,EAAsCT,IAAtC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI/C,qBAAJ,CAA0B4C,IAAI,CAACD,OAA/B,CAAP;QACD;;QACD,OAAO,IAAI3C,qBAAJ,CAA0B,EAA1B,CAAP;MACD,CALE,CADE,EAOLrG,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD4B,CAA9B;IAkBA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkR,mBAAjB,CADR,EAEEnX,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsP,eAAT,GAA2B9O,IAA3B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI/G,wBAAJ,CAA6B8G,IAAI,CAACF,IAAlC,CAAP;QACD;;QACD,OAAO,IAAI5G,wBAAJ,EAAP;MACD,CALE,CADE,EAOLrC,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD6B,CAA/B;IAkBA,iCAA4B7J,YAAY,CAAC,MACvC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoR,6BAAjB,CADR,EAEErX,GAAG,CAAE8I,MAAD,IAAwCA,MAAzC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwP,wBAAT,GAAoChP,IAApC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIhH,iCAAJ,CAAsC+G,IAAI,CAACF,IAA3C,CAAP;QACD;;QACD,OAAO,IAAI7G,iCAAJ,EAAP;MACD,CALE,CADE,EAOLpC,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADsC,CAAxC;IAkBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsR,wBAAjB,CADR,EAEEvX,GAAG,CAAE8I,MAAD,IAAmCA,MAApC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS0P,mBAAT,GAA+BlP,IAA/B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI5G,4BAAJ,CAAiC2G,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAvC,CAAP;QACD;;QACD,OAAO,IAAIzG,4BAAJ,EAAP;MACD,CALE,CADE,EAOLxC,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADmC,CAArC;IAkBA,8BAAyB7J,YAAY,CAAC,MACpC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwR,6BAAjB,CADR,EAEEpX,cAAc,CACZ,KAAK4H,MAAL,CAAYK,IAAZ,CAAiB3I,MAAM,CAAE+X,KAAD,IAAe;MAAA;;MAAC,kBAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEC,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEwN,cAAb;IAA2B,CAA5C,CAAvB,CADY,CAFhB,EAKE1X,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,CAAD,KAA+B;MACvCC,OAAO,CAACC,GAAR,CAAYF,gBAAZ;MAEA,IAAI9B,mBAAmB,GACrB,CAAC,iBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEiC,sBAAlB,IACG,CACAH,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEG,sBADlB,EAEAH,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEI,uBAFlB,EAGAJ,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEK,sBAHlB,CADH,GAMG,IANJ,MAOC,iBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAED,uBAAlB,IACG,CACAJ,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEI,uBADlB,EAEAJ,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEK,sBAFlB,CADH,GAKG,IAZJ,MAaC,iBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEA,sBAAlB,IACG,CAACL,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEK,sBAAnB,CADH,GAEG,IAfJ,KAgBA,IAjBF;MAkBA,MAAMC,OAAO,mCACRN,gBADQ,GACQ;QACnB3D,IAAI,EAAE,kCADa;QAEnB6B;MAFmB,CADR,CAAb;MAMA,OAAO,KAAK7N,aAAL,CAAmBiM,wBAAnB,CAA4CgE,OAA5C,EAAqD7P,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB4B,YAAY,CAACqN,OAAb,CAAqB,iBAArB,EAAwClN,IAAI,CAACmN,SAAL,CAAenP,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAArB,CAAxC;UACA,OAAO,IAAItF,gCAAJ,CAAqCwF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAA3C,CAAP;QACD,CAHD,MAGO;UACL,OAAO,IAAItF,gCAAJ,CAAqC,EAArC,CAAP;QACD;MACF,CAPE,CADE,EASL3D,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CATL,CAAP;IAWD,CAtCQ,CALX,CADmC,CAArC;IAgDA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACqS,sBAAjB,CADR,EAEEjY,cAAc,CACZ,KAAK4H,MAAL,CAAYK,IAAZ,CACE3I,MAAM,CAAE+X,KAAD,IAAe;MAAA;;MAAC,aAAM,CAACjP,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IAAgD,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEgP,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEmO,qBAA7D,GAAqF,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,IAAP,MAAW,IAAX,IAAWrN,aAAX,GAAW,MAAX,GAAWA,GAAEsN,cAAlG;IAAgH,CAAjI,CADR,CADY,CAFhB,EAOE1X,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,CAAD,KAA+B;MACvC,MAAMW,WAAW,GAAG1P,MAApB;MACAgP,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BS,WAAW,CAACZ,cAA3C;MACAE,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BF,gBAA9B,EAHuC,CAKvC;MACA;;MACA,MAAMM,OAAO,iDACRN,gBADQ,GAEPW,WAAW,CAACZ,cAAZ,IAA8B,EAFvB,GAE0B;QACrC1D,IAAI,EAAE;MAD+B,CAF1B,CAAb;MAKR4D,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuBI,OAAvB;MAEQ,OAAO,KAAKjQ,aAAL,CAAmBiM,wBAAnB,CAA4CgE,OAA5C,EAAqD7P,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,OAAO,IAAInG,+BAAJ,CAAoC,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEiG,IAAN,KAAc,EAAlD,CAAP;MACD,CAFE,CADE,EAILjJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAJL,CAAP;IAMD,CApBQ,CAPX,CAD6B,CAA/B;IAgCA,sBAAiB7J,YAAY,CAAC,MAC5B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwS,kBAAjB,CADR,EAEEpY,cAAc,CAAC,KAAK4H,MAAL,CAAYK,IAAZ,CAAiB3I,MAAM,CAAE+X,KAAD,IAAe;MAAA;;MAAC,aAAM,CAACjP,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IAAgD,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEgP,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEmO,qBAA7D,GAAqF,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,IAAP,MAAW,IAAX,IAAWrN,aAAX,GAAW,MAAX,GAAWA,GAAEsN,cAAlG;IAAgH,CAAjI,CAAvB,CAAD,EAA6J,KAAK3P,MAAL,CAAYtI,MAAZ,CAAmB6H,aAAnB,CAA7J,CAFhB,EAGEtH,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,EAA2Ba,UAA3B,CAAD,KAA2C;MACnD,IAAI,CAACA,UAAD,IAAe,iBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAEtJ,UAAlB,MAAiC,CAApD,EAAuD;QACrD,KAAKnH,MAAL,CAAYoB,QAAZ,CAAqB,IAAIlI,aAAJ,EAArB;MACD;;MAAA;;MACD,KAAK8G,MAAL,CAAYoB,QAAZ,CAAqB,IAAIxC,gBAAJ,CAAqB,KAArB,CAArB;;MAEA,IAAIsR,OAAO,mCACNN,gBADM,GACU;QACnB3D,IAAI,EACF,KAAKI,qBAAL,IACE,CAAC9L,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,CADH,GAEI,qBAFJ,GAGI;MALa,CADV,CAAX;MASA,OAAO,KAAKT,aAAL,CAAmBiM,wBAAnB,CAA4CgE,OAA5C,EAAqD7P,IAArD,CACLlI,GAAG,CAAE8I,IAAD,IAAe,KAAKyP,mBAAL,CAAyBzP,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE2K,KAA/B,EAAsCsE,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnD,sBAA/C,CAAhB,CADE,EAEL9U,SAAS,CAAEgJ,IAAD,IAAc;;;QACtB,IAAI,WAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE2K,KAAN,MAAW,IAAX,IAAWzJ,aAAX,GAAW,MAAX,GAAWA,GAAEiD,MAAb,MAAwB,CAAxB,IAA6B,QAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE+B,UAAT,IAAsB,CAAvD,EAA0D;UACxD+I,OAAO,mCACFA,OADE,GACK;YACV/I,UAAU,EAAE,QAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEA,UAAT,IAAsB;UADxB,CADL,CAAP;;UAIA,IAAI5G,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,CAAJ,EAAmD;YACjD,KAAKV,MAAL,CAAYoB,QAAZ,CAAqB,IAAIzC,mBAAJ,CAAwBuR,OAAxB,CAArB;UACD,CAFD,MAEO;YACL,KAAKlQ,MAAL,CAAYoB,QAAZ,CAAqB,IAAI1C,mBAAJ,CAAwBwR,OAAxB,CAArB;UACD;;UACD,OAAOvY,KAAP;QACD;;QACD,OAAOC,EAAE,CACP,IAAIqE,oBAAJ,CAAyBgF,IAAzB,CADO,EAEP,IAAIxC,cAAJ,CAAmBwC,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE2K,KAAzB,CAFO,CAAT;MAID,CAjBQ,CAFJ,EAqBL9T,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CArBL,CAAP;IAuBD,CAtCQ,CAHX,CAD2B,CAA7B;IA6DA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC2S,kBAAjB,CADR,EAEEvY,cAAc,CACZ,KAAK4H,MAAL,CAAYK,IAAZ,CACE3I,MAAM,CAAE+X,KAAD,IAAe;MAAA;;MAAC,aAAM,CAACjP,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IAAgD,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEgP,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEmO,qBAA7D,GAAqF,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,IAAP,MAAW,IAAX,IAAWrN,aAAX,GAAW,MAAX,GAAWA,GAAEsN,cAAlG;IAAgH,CAAjI,CADR,CADY,CAFhB,EAOE1X,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,CAAD,KAA+B;MACvC,MAAMW,WAAW,GAAG1P,MAApB;MACAgP,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CS,WAAW,CAACZ,cAA1D;MACAE,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6CF,gBAA7C,EAHuC,CAKvC;;MACA,MAAMM,OAAO,iDACRN,gBADQ,GAEPW,WAAW,CAACZ,cAAZ,IAA8B,EAFvB,GAE0B;QACrC1D,IAAI,EAAE;MAD+B,CAF1B,CAAb;MAKA,OAAO,KAAKhM,aAAL,CAAmBiM,wBAAnB,CAA4CgE,OAA5C,EAAqD7P,IAArD,CACLtI,GAAG,CAAEkJ,IAAD,IAAe,IAAIvG,2BAAJ,CAAgC,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEqG,IAAN,KAAc,EAA9C,CAAhB,CADE,CAAP;IAGD,CAdQ,CAPX,EAqBMjJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CArBhB,CADkC,CAApC;IA0BA,yBAAoB7J,YAAY,CAAC,MAC/B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4S,iBAAjB,CADR,EAEExY,cAAc,CACZ,KAAK4H,MAAL,CAAYK,IAAZ,CACE3I,MAAM,CAAE+X,KAAD,IAAe;;;MACpB,aAAM,CAACjP,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IACI,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEgP,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEmO,qBADjB,GAEI,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,IAAP,MAAW,IAAX,IAAWrN,aAAX,GAAW,MAAX,GAAWA,GAAEsN,cAFjB;IAE+B,CAH3B,CADR,CADY,CAFhB,EAWE1X,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,CAAD,KAA+B;MACvC,MAAMM,OAAO,mCACRN,gBADQ,GACQ;QACnB3D,IAAI,EAAE;MADa,CADR,CAAb;MAIA,OAAO,KAAKhM,aAAL,CACJiM,wBADI,CACqBgE,OADrB,EAEJ7P,IAFI,CAGHtI,GAAG,CAAEkJ,IAAD,IAAe,IAAIrF,0BAAJ,CAA+B,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEmF,IAAN,KAAc,EAA7C,CAAhB,CAHA,CAAP;IAKD,CAVQ,CAXX,EAsBEjJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAtBZ,CAD8B,CAAhC;IA2BA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC6S,uBAAjB,CADR,EAEEzY,cAAc,CACZ,KAAK4H,MAAL,CAAYK,IAAZ,CACE3I,MAAM,CAAE+X,KAAD,IAAe;;;MACpB,aAAM,CAACjP,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,IACI,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEgP,IAAP,MAAW,IAAX,IAAWvN,aAAX,GAAW,MAAX,GAAWA,GAAEmO,qBADjB,GAEI,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,IAAP,MAAW,IAAX,IAAWrN,aAAX,GAAW,MAAX,GAAWA,GAAEsN,cAFjB;IAE+B,CAH3B,CADR,CADY,CAFhB,EAWE1X,SAAS,CAAC,CAAC,CAAC4I,MAAD,EAAS+O,gBAAT,CAAD,KAA+B;MACvCC,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCF,gBAAhC;MACA,MAAMM,OAAO,mCACRN,gBADQ,GACQ;QACnB3D,IAAI,EAAE;MADa,CADR,CAAb;MAIA,OAAO,KAAKhM,aAAL,CACJiM,wBADI,CACqBgE,OADrB,EAEJ7P,IAFI,CAGHtI,GAAG,CACAkJ,IAAD,IAAc;QAAA;;QACZ,WAAIrE,2BAAJ,CAAgC,WAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEmE,IAAN,MAAU,IAAV,IAAUoB,aAAV,GAAU,MAAV,GAAUA,GAAEyJ,KAAZ,KAAqB,EAArD;MAAwD,CAFzD,CAHA,CAAP;IAQD,CAdQ,CAXX,EA0BE9T,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CA1BZ,CADkC,CAApC;IA+BA,6BAAwB7J,YAAY,CAAC,MACnC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CACJuG,eAAe,CAAC8S,qBADZ,EAEJ9S,eAAe,CAAC+S,qBAFZ,CADR,EAKE9Y,SAAS;MAAA,6BAAC,WAAO4I,MAAP,EAAsB;;;QAC9B,KAAI,CAACwL,qBAAL,SAAmC,KAAI,CAACrM,MAAL,CAChCtI,MADgC,CACzB4H,4BADyB,EAEhCe,IAFgC,CAG/BtI,GAAG,CAAEgJ,IAAD,IAAeA,IAAhB,CAH4B,EAI/B7I,IAAI,CAAC,CAAD,CAJ2B,EAMhC8Y,SANgC,EAAnC;QAOA,MAAMC,eAAe,GAAG,YAAM,CAACC,MAAP,MAAa,IAAb,IAAa/O,aAAb,GAAa,MAAb,GAAaA,GAAE8O,eAAvC;QACA,MAAME,WAAW,GAAG,YAAM,CAACD,MAAP,MAAa,IAAb,IAAa7O,aAAb,GAAa,MAAb,GAAaA,GAAE8O,WAAnC;;QAEA,IACE,CAAC,KAAI,CAAC9E,qBAAN,IACA9L,MAAM,CAACC,QAAP,CAAgBC,QAAhB,CAAyBC,QAAzB,CAAkC,UAAlC,CAFF,EAGE;UACA,IAAIuQ,eAAe,IAAK,CAACE,WAAzB,EAAuC;YACrC,KAAI,CAACnR,MAAL,CAAYoB,QAAZ,CAAqB,IAAIzH,gBAAJ,EAArB;;YACA,KAAI,CAACqG,MAAL,CAAYoB,QAAZ,CAAqB,IAAIzE,oBAAJ,EAArB;UACD;QACF,CARD,MAQO,IAAIsU,eAAe,IAAK,CAACE,WAAzB,EAAuC;UAC5C,KAAI,CAACnR,MAAL,CAAYoB,QAAZ,CAAqB,IAAI7F,yBAAJ,EAArB;QACD,CArB6B,CAsB9B;;;QACA,KAAI,CAACyE,MAAL,CAAYoB,QAAZ,CAAqB,IAAIlF,eAAJ,EAArB;;QACA,IAAI+U,eAAe,IAAK,CAACE,WAAzB,EAAuC;UACrC,OAAO,IAAItW,wBAAJ,EAAP;QACD;;QACD,OAAO;UAAEmS,IAAI,EAAE;QAAR,CAAP;MACD,CA5BQ;;MAAA;QAAA;MAAA;IAAA,IALX,CADkC,CAApC;IAsCA,uBAAkBxV,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoT,sBAAjB,CADR,EAEErZ,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwR,cAAT,GAA0BhR,IAA1B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI9E,2BAAJ,CAAgC6E,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAtC,CAAP;QACD;;QACD,OAAO,IAAI3E,2BAAJ,EAAP;MACD,CALE,CADE,EAOLtE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD4B,CAA9B;IAkBA,wBAAmB7J,YAAY,CAAC,MAC9B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsT,uBAAjB,CADR,EAEEvZ,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS0R,kBAAT,GAA8BlR,IAA9B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIhG,2BAAJ,CAAgC+F,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAtC,CAAP;QACD;;QACD,OAAO,IAAI7F,2BAAJ,EAAP;MACD,CALE,CADE,EAOLpD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD6B,CAA/B;IAkBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwT,oBAAjB,CADR,EAEEzZ,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS4R,eAAT,GAA2BpR,IAA3B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAInE,wBAAJ,CAA6BkE,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAIhE,wBAAJ,EAAP;MACD,CALE,CADE,EAOLjF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD0B,CAA5B;IAkBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0T,2BAAjB,CADR,EAEE3Z,GAAG,CAAE8I,MAAD,IAAqCA,MAAtC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS8R,qBAAT,GAAiCtR,IAAjC,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAIvG,8BAAJ,CAAmCsG,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAzC,CAAP;QACD;;QACD,OAAO,IAAIpG,8BAAJ,EAAP;MACD,CALE,CADE,EAOL7C,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD+B,CAAjC;IAkBA,uBAAkB7J,YAAY,CAAC,MAC7B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4T,uBAAjB,CADR,EAEE7Z,GAAG,CAAE8I,MAAD,IAAkCA,MAAnC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASgS,kBAAT,GAA8BxR,IAA9B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI7F,2BAAJ,CAAgC4F,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAtC,CAAP;QACD;;QACD,OAAO,IAAI1F,2BAAJ,EAAP;MACD,CALE,CADE,EAOLvD,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD4B,CAA9B;IAkBA,0BAAqB7J,YAAY,CAAC,MAChC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC8T,2BAAjB,CADR,EAEE/Z,GAAG,CAAE8I,MAAD,IAAoCA,MAAM,CAAC0D,MAA5C,CAFL,EAGEtM,SAAS,CAAE8I,IAAD,IACR,KAAKlB,GAAL,CAASkS,iBAAT,CAA2BhR,IAA3B,EAAiCV,IAAjC,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAIlH,6BAAJ,CAAkCiH,IAAI,CAACF,IAAvC,CAAP;MACD;;MACD,OAAO,IAAI/G,6BAAJ,CAAkCiH,IAAI,CAACF,IAAvC,CAAP;IACD,CALE,CADL,EAOEjJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CAD+B,CAAjC;IAkBA,+BAA0B7J,YAAY,CAAC,MACrC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACgU,6BAAjB,CADR,EAEEja,GAAG,CAAE8I,MAAD,IAAsCA,MAAM,CAAC0D,MAA9C,CAFL,EAGEtM,SAAS,CAAE8I,IAAD,IACR,KAAKlB,GAAL,CAASoS,sBAAT,CAAgClR,IAAhC,EAAsCV,IAAtC,CACEtI,GAAG,CAAEkJ,IAAD,IAAc;MAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;QAClB,OAAO,IAAIlG,+BAAJ,CAAoCiG,IAAI,CAACF,IAAzC,CAAP;MACD;;MACD,OAAO,IAAI/F,+BAAJ,CAAoCiG,IAAI,CAACF,IAAzC,CAAP;IACD,CALE,CADL,EAOEjJ,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPZ,CADO,CAHX,CADoC,CAAtC;IAkBA,sBAAiB7J,YAAY,CAAC,MAC5B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACkU,sBAAjB,CADR,EAEEna,GAAG,CAAE8I,MAAD,IAAiCA,MAAlC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASsS,iBAAT,GAA6B9R,IAA7B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI3E,0BAAJ,CAA+B0E,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAArC,CAAP;QACD;;QACD,OAAO,IAAIxE,0BAAJ,EAAP;MACD,CALE,CADE,EAOLzE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD2B,CAA7B;IAkBA,oBAAe7J,YAAY,CAAC,MAC1B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACoU,mBAAjB,CADR,EAEEra,GAAG,CAAE8I,MAAD,IAA+BA,MAAhC,CAFL,EAGE5I,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAASwS,eAAT,GAA2BhS,IAA3B,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAInF,wBAAJ,CAA6BkF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAnC,CAAP;QACD;;QACD,OAAO,IAAIhF,wBAAJ,EAAP;MACD,CALE,CADE,EAOLjE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CADyB,CAA3B;IAkBA,sCAAiC7J,YAAY,CAAC,MAC5C,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACsU,mCAAjB,CADR,EAEEva,GAAG,CAAE8I,MAAD,IAA6CA,MAAM,CAACyN,UAArD,CAFL,EAGErW,SAAS,CAAE8I,IAAD,IAAc;MACtB,OAAO,KAAKlB,GAAL,CAAS0S,6BAAT,CAAuCxR,IAAvC,EAA6CV,IAA7C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,OAAO,IAAI5D,sCAAJ,CAA2C2D,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,IAAjD,CAAP;QACD;;QACD,OAAO,IAAIzD,sCAAJ,EAAP;MACD,CALE,CADE,EAOLxF,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAPL,CAAP;IASD,CAVQ,CAHX,CAD2C,CAA7C;IAkBA,mBAAc7J,YAAY,CAAC,MACzB,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAACwU,WAAjB,CADR,EAEEva,SAAS,CAAE4I,MAAD,IAAuB;MAC/B,OAAO,KAAKhB,GAAL,CAAS4S,gBAAT,CAA0B5R,MAAM,CAACC,OAAjC,EAA0CT,IAA1C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,qCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIxI,iBAAJ,EAArB;;UACA,OAAO,IAAIoD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CATE,CADE,EAWLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAdQ,CAFX,CADwB,CAA1B;IAqBA,2BAAsB7J,YAAY,CAAC,MACjC,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC0U,oBAAjB,CADR,EAEEza,SAAS,CAAE4I,MAAD,IAA+B;MACvC,OAAO,KAAKhB,GAAL,CAAS8S,wBAAT,CAAkC9R,MAAM,CAACC,OAAzC,EAAkDT,IAAlD,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,8CADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAItI,yBAAJ,EAArB;;UACA,OAAO,IAAIkD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CATE,CADE,EAWLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAdQ,CAFX,CADgC,CAAlC;IAqBA,qBAAgB7J,YAAY,CAAC,MAC3B,KAAKoI,QAAL,CAAcS,IAAd,CACE5I,MAAM,CAACuG,eAAe,CAAC4U,aAAjB,CADR,EAEE3a,SAAS,CAAE4I,MAAD,IAAyB;MACjC,OAAO,KAAKhB,GAAL,CAASgT,kBAAT,CAA4BhS,MAAM,CAACC,OAAnC,EAA4CT,IAA5C,CACLtI,GAAG,CAAEkJ,IAAD,IAAc;QAChB,IAAIA,IAAI,CAACC,SAAT,EAAoB;UAClB,KAAKnB,oBAAL,CAA0BoB,OAA1B,CACE,uCADF;;UAGA,KAAKnB,MAAL,CAAYoB,QAAZ,CAAqB,IAAIvI,mBAAJ,EAArB;;UACA,OAAO,IAAImD,aAAJ,EAAP;QACD;;QACD,OAAO,IAAIC,oBAAJ,EAAP;MACD,CATE,CADE,EAWLnE,UAAU,CAAEuJ,GAAD,IAASzJ,EAAE,CAAC,IAAIU,OAAJ,CAAY+I,GAAZ,CAAD,CAAZ,CAXL,CAAP;IAaD,CAdQ,CAFX,CAD0B,CAA5B;IAqBA,6BAAiC,KAAjC;EAYK;;EAxYGqP,mBAAmB,CAAC9E,QAAe,EAAhB,EAAoBmB,sBAApB,EAA+C;IACxE,IAAI,EAACnB,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAExG,MAAR,CAAJ,EAAoB;IAEpB,MAAMD,OAAO,GAAGyG,KAAK,CAAC7T,GAAN,CAAW2X,IAAD,IAAeA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEjN,EAA/B,EAAmCyO,MAAnC,CAA0C4B,OAA1C,CAAhB;IACA,MAAMC,SAAS,GAAG,EAAlB;;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG7N,OAAO,CAACC,MAA5B,EAAoC4N,CAAC,IAAID,SAAzC,EAAoD;MAClD,MAAMjS,OAAO,GAAG;QACdmS,OAAO,EAAE9N,OAAO,CAAC+N,KAAR,CAAcF,CAAd,EAAiBA,CAAC,GAAGD,SAArB,CADK;QAEdhG,sBAAsB,EAAEA;MAFV,CAAhB;;MAIA,KAAK/M,MAAL,CAAYoB,QAAZ,CAAqB,IAAInE,4BAAJ,CAAiC6D,OAAjC,CAArB;IACD;EACF;;AAl1DqB;;;mBAAXpB,aAAWyT;AAAA;;;SAAXzT;EAAW0T,SAAX1T,WAAW", "names": ["createEffect", "ofType", "select", "EMPTY", "of", "throwError", "catchError", "map", "mergeMap", "switchMap", "take", "tap", "withLatestFrom", "CloseModal", "OnError", "EMPTY_GUID", "QRConfirmationComponent", "AddLeadProjectsSuccess", "AddLeadSuccess", "AddQRLeadSuccess", "BulkAgencySuccess", "BulkCampaignSuccess", "BulkChannelPartnerSuccess", "BulkProjectsSuccess", "BulkReassignLeadSuccess", "BulkSourceSuccess", "ClearCardData", "CommunicationBulkCountSuccess", "CommunicationBulkMessageSuccess", "CommunicationCountSuccess", "CommunicationMessageSuccess", "DeleteLeadsSuccess", "EmptyEffect", "ExcelUploadSuccess", "ExportLeadsSuccess", "FetchActiveCount", "FetchAdditionalPropertyListSuccess", "FetchAdditionalPropertyValueSuccess", "FetchAgencyNameListAnonymousSuccess", "FetchAgencyNameListSuccess", "FetchAllParentLeadByIdSuccess", "FetchBulkOperationSuccess", "FetchCampaignListAnonymousSuccess", "FetchCampaignListSuccess", "FetchChannelPartnerListAnonymousSuccess", "FetchChannelPartnerListSuccess", "FetchCountryBasedCitySuccess", "FetchDuplicateFeatureSuccess", "FetchExcelUploadedSuccess", "FetchExportStatusSuccess", "FetchLeadActiveCountSuccess", "FetchLeadAltCountryCodeSuccess", "FetchLeadAppointmentsByProjectsSuccess", "FetchLeadBaseFilterCount", "FetchLeadBaseFilterCountSuccess", "FetchLeadById", "FetchLeadByIdWithArchiveSuccess", "FetchLeadCitiesSuccess", "FetchLeadClusterNameSuccess", "FetchLeadCommunitiesSuccess", "FetchLeadCountriesSuccess", "FetchLeadCountryCodeSuccess", "FetchLeadCurrencySuccess", "FetchLeadCustomTopFilters", "FetchLeadCustomTopFiltersChildrenSuccess", "FetchLeadCustomTopFiltersSuccess", "FetchLeadDroppedCountSuccess", "FetchLeadExportSuccess", "FetchLeadFlagsCountSuccess", "FetchLeadHistoryListSuccess", "FetchLeadIdSuccess", "FetchLeadLandLineSuccess", "FetchLeadList", "FetchLeadListSuccess", "FetchLeadListV2", "FetchLeadLocalitesSuccess", "FetchLeadNationalitySuccess", "FetchLeadNotInterestedCountSuccess", "FetchLeadNotesListSuccess", "FetchLeadPostalCodeSuccess", "FetchLeadRotation", "FetchLeadRotationSuccess", "FetchLeadStatesSuccess", "FetchLeadStatusCount", "FetchLeadStatusCountSuccess", "FetchLeadSubCommunitiesSuccess", "FetchLeadTowerNamesSuccess", "FetchLeadUnitNameSuccess", "FetchLeadZonesSuccess", "FetchLeadsCommunicationByIds", "FetchLeadsCommunicationByIdsSuccess", "FetchLocationsSuccess", "FetchMatchingPropertyOrProjectListSuccess", "FetchMigrateExcelUploadedSuccess", "FetchModuleWiseSearchPropertiesSuccess", "FetchProjectListSuccess", "FetchPropertyListSuccess", "FetchQRProjectListSuccess", "FetchQRPropertyListSuccess", "FetchSubSourceList", "FetchSubSourceListSuccess", "FetchUploadTypeNameListSuccess", "HasLeadAltInfoSuccess", "HasLeadInfoSuccess", "LeadActionTypes", "LeadExcelUploadSuccess", "MeetingOrVisitDoneSuccess", "NavigateToLinkSuccess", "PermanentDeleteLeadsSuccess", "ReassignBothSuccess", "ReassignLeadSuccess", "RestoreLeadsSuccess", "SecondaryAssignLeadSuccess", "UpdateCardData", "UpdateFilterPayload", "UpdateInvoiceFilter", "UpdateIsLoadMore", "UpdateLeadNotesSuccess", "UpdateLeadStatusSuccess", "UpdateLeadSuccess", "UpdateMultipleLeadStatus", "UpdateMultipleLeadStatusSuccess", "UploadLeadDocumentSuccess", "updateDuplicateAssignSuccess", "getFiltersPayload", "getInvoiceFiltersPayload", "getIsLeadCustomStatusEnabled", "getIsLoadMore", "LeadPreviewChanged", "FetchUsersListForReassignment", "LeadEffects", "constructor", "actions$", "api", "leadRotationApi", "_notificationService", "_store", "commonService", "router", "modalService", "modalRef", "pipe", "FETCH_LEAD_LIST", "window", "location", "pathname", "includes", "filterPayload", "ADD_LEAD", "action", "payload", "data", "add", "resp", "succeeded", "success", "dispatch", "err", "ADD_QR_LEAD", "addQRLead", "templateId", "initialState", "class", "ignoreBackdropClick", "show", "EXCEL_UPLOAD", "uploadExcel", "file", "warn", "message", "error", "_a", "Message", "_b", "split", "UPDATE_LEAD", "update", "id", "UPDATE_LEAD_NOTES", "updateNotes", "canFetchLeadList", "userDetails", "localStorage", "getItem", "currentUserName", "JSON", "parse", "given_name", "family_name", "preferred_username", "newNote", "fieldName", "<PERSON><PERSON><PERSON>", "oldValue", "newValue", "notes", "updatedBy", "updatedOn", "Date", "toISOString", "auditActionType", "FETCH_LEAD_NOTES_LIST", "getNotesList", "UPDATE_MULTIPLE_LEAD", "isUpdateStatus", "updateBulkLeadStatus", "FETCH_LEAD_BY_ID", "leadId", "get", "UPDATE_LEAD_STATUS", "updateLeadStatus", "UPDATE_LEAD_SHARE_COUNT", "increaseShareCount", "UPDATE_LEADS_TAG_INFO", "updateLeadTags", "EMPTY_EFFECT", "REASSIGN_LEAD", "reassignLead", "msg", "leadIds", "length", "subscribe", "leadVisibility", "userIds", "_c", "_d", "_e", "REASSIGN_BOTH", "reassignBoth", "Array", "isArray", "messages", "SECONDARY_ASSIGN_LEAD", "secondaryAssignLead", "filtersData", "count", "_f", "_g", "BULK_REASSIGN_LEAD", "bulkReassignLead", "toString", "LEAD_EXCEL_UPLOAD", "UPLOAD_MAPPED_COLUMNS", "uploadMappedColumns", "excelUrl", "dataCount", "DataCount", "UPLOAD_MIGRATE_MAPPED_COLUMNS", "uploadMigrateMappingColumns", "FETCH_MIGRATE_EXCEL_UPLOADED_LIST", "getMigrateExcelUploadedList", "pageNumber", "pageSize", "HAS_LEAD_INFO", "doesLeadExists", "HAS_LEAD_ALT_INFO", "UPLOAD_DOCUMENT", "uploadLeadDocument", "documents", "DELETE_DOCUMENT", "deletedLeadDocument", "FETCH_PROJECT_LIST", "getProjectList", "isWithArchive", "FETCH_QR_PROJECT_LIST", "getQRProjectList", "FETCH_PROPERTY_LIST", "getPropertyList", "FETCH_QR_PROPERTY_LIST", "getQRPropertyList", "FETCH_EXCEL_UPLOADED_LIST", "getExcelUploadedList", "FETCH_EXPORT_STATUS", "getExportStatus", "FETCH_LEAD_CURRENCY_LIST", "getCurrency", "COMMUNICATION_COUNT", "communicationCount", "COMMUNICATION_BULK_COUNT", "communicationBulkCount", "MEETING_OR_VISIT_DONE", "meetingOrVisitDone", "FETCH_LOCATIONS", "getLocations", "FETCH_LEAD_CITIES", "getLeadCities", "FETCH_LEAD_STATES", "getLeadStates", "FETCH_LEAD_COUNTRIES", "getLeadCountries", "FETCH_LEAD_SUB_COMMUNITIES", "getLeadSubCommunities", "FETCH_LEAD_COMMUNITIES", "getLeadCommunities", "FETCH_LEAD_TOWER_NAME", "getLeadTowerNames", "FETCH_LEAD_LOCALITES", "getLocalites", "FETCH_LEAD_ZONES", "getLeadZones", "DELETE_LEADS", "deleteLeads", "isInvoice", "RESTORE_LEADS", "restoreLeads", "href", "FETCH_MATCHING_PROPERTIES_OR_PROJECTS", "getModuleList", "FETCH_SUB_SOURCE_LIST", "getSubSourceList", "BULK_SOURCE", "bulkSource", "BULK_PROJECTS", "bulkProjects", "FETCH_AGENCY_NAME_LIST", "getAgencyNames", "FETCH_AGENCY_NAME_LIST_ANONYMOUS", "getAgencyNamesAnonymous", "FETCH_CHANNEL_PARTNER_LIST", "getChannelPartnerList", "FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS", "getChannelPartnerListAnonymous", "UPDATE_DUPLICATE_ASSIGNMENT_LIST", "updateDuplicateAssign", "items", "FETCH_DUPLICATE_FEATURE", "getDuplicateFeature", "FETCH_LEADS_EXPORT", "FETCH_NOT_INTERESTED_COUNT", "path", "getModuleListByAdvFilter", "FETCH_DROPPED_COUNT", "EXPORT_LEADS", "isCustomStatusEnabled", "isEnabled", "exportLead", "customExportLead", "COMMUNICATION_MESSAGE", "communicationMessage", "COMMUNICATION_BULK_MESSAGE", "communicationBulkMessage", "FETCH_LEAD_APPOINTMENTS_BY_PROJECTS", "FETCH_LEADS_COMMUNICATION_BY_IDS", "showCommunicationCount", "type", "ADD_LEAD_PROJECTS", "addProjects", "ADD_LEAD_ROTATION_GROUP", "createLeadRotationGroup", "FETCH_LEAD_ROTATION", "getLeadRotation", "DELETE_LEAD_ROTATION", "deleteLeadRotation", "UPDATE_LEAD_ROTATION", "updateLeadRotation", "FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN", "LeadVisibility", "ScheduledType", "CustomFilterBaseIds", "customThirdLevelFilterId", "customSecondLevelFilterId", "customFirstLevelFilterId", "PERMANENT_DELETE_LEADS", "permanentdeleteLeads", "FETCH_BULK_OPERATION", "getBulkOperation", "moduleType", "FETCH_UPLOADTYPENAME_LIST", "getUploadTypeNameList", "FETCH_LEAD_HISTORY", "getAuditHistory", "FETCH_ADDITIONAL_PROPERTY_LIST", "getAdditionalProperties", "FETCH_ADDITIONAL_PROPERTY_VALUE", "getAPValues", "key", "NAVIGATE_TO_LINK", "navigateToLink", "FETCH_CAMPAIGN_LIST", "getCampaignList", "FETCH_CAMPAIGN_LIST_ANONYMOUS", "getCampaignListAnonymous", "FETCH_COUNTRY_BASED_CITY", "getCountryBasedCity", "FETCH_LEAD_CUSTOM_TOP_FILTERS", "state", "lead", "filtersPayload", "filtersFromState", "console", "log", "CustomThirdLevelFilter", "CustomSecondLevelFilter", "CustomFirstLevelFilter", "filters", "setItem", "stringify", "FETCH_BASEFILTER_COUNT", "invoiceFiltersPayload", "fetchAction", "FETCH_LEAD_LIST_V2", "isLoadmore", "dispatchLeadBatches", "FETCH_ACTIVE_COUNT", "FETCH_FLAGS_COUNT", "FETCH_LEAD_STATUS_COUNT", "UPDATE_FILTER_PAYLOAD", "UPDATE_INVOICE_FILTER", "to<PERSON>romise", "showFilterCount", "filter", "dontCallApi", "FETCH_LEAD_NATIONALITY", "getNationality", "FETCH_LEAD_CLUSTER_NAME", "getLeadClusterName", "FETCH_LEAD_UNIT_NAME", "getLeadUnitName", "FETCH_LEAD_ALT_COUNTRY_CODE", "getLeadAltCountryCode", "FETCH_LEAD_COUNTRY_CODE", "getLeadCountryCode", "FETCH_ALL_PARENT_LEAD_BY_ID", "getParentLeadData", "FETCH_LEAD_BY_ID_WITH_ARCHIVE", "getLeadByIdWithArchive", "FETCH_LEAD_POSTAL_CODE", "getLeadPostalCode", "FETCH_LEAD_LANDLINE", "getLeadLandLine", "FETCH_MODULE_WISE_SEARCH_PROPERTIES", "getModuleWiseSearchProperties", "BULK_AGENCY", "bulkUpdateAgency", "BULK_CHANNEL_PARTNER", "bulkUpdateChannelPartner", "BULK_CAMPAIGN", "bulkUpdateCampaign", "Boolean", "batchSize", "i", "LeadIds", "slice", "i0", "factory"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Desktop\\reports\\Leadrat-Black-Web\\src\\app\\reducers\\lead\\lead.effects.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { Actions, createEffect, ofType } from '@ngrx/effects';\r\nimport { Store, select } from '@ngrx/store';\r\nimport { NotificationsService } from 'angular2-notifications';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\nimport { EMPTY, of, throwError } from 'rxjs';\r\nimport {\r\n  catchError,\r\n  map,\r\n  mergeMap,\r\n  switchMap,\r\n  take,\r\n  tap,\r\n  withLatestFrom,\r\n} from 'rxjs/operators';\r\nimport { CloseModal, OnError } from 'src/app/app.actions';\r\nimport { EMPTY_GUID } from 'src/app/app.constants';\r\nimport { AppState } from 'src/app/app.reducer';\r\nimport { QRConfirmationComponent } from 'src/app/features/no-auth/lead-qr-form/qr-confirmation/qr-confirmation.component';\r\nimport {\r\n  AddLead,\r\n  AddLeadProjects,\r\n  AddLeadProjectsSuccess,\r\n  AddLeadRotationGroup,\r\n  AddLeadSuccess,\r\n  AddQRLead,\r\n  AddQRLeadSuccess,\r\n  BulkAgency,\r\n  BulkAgencySuccess,\r\n  BulkCampaign,\r\n  BulkCampaignSuccess,\r\n  BulkChannelPartner,\r\n  BulkChannelPartnerSuccess,\r\n  BulkProjects,\r\n  BulkProjectsSuccess,\r\n  BulkReassignLead,\r\n  BulkReassignLeadSuccess,\r\n  BulkSource,\r\n  BulkSourceSuccess,\r\n  ClearCardData,\r\n  CommunicationBulkCount,\r\n  CommunicationBulkCountSuccess,\r\n  CommunicationBulkMessage,\r\n  CommunicationBulkMessageSuccess,\r\n  CommunicationCount,\r\n  CommunicationCountSuccess,\r\n  CommunicationMessage,\r\n  CommunicationMessageSuccess,\r\n  DeleteLeadRotation,\r\n  DeleteLeads,\r\n  DeleteLeadsSuccess,\r\n  EmptyEffect,\r\n  ExcelUpload,\r\n  ExcelUploadSuccess,\r\n  ExportLeads,\r\n  ExportLeadsSuccess,\r\n  FetchActiveCount,\r\n  FetchAdditionalPropertyList,\r\n  FetchAdditionalPropertyListSuccess,\r\n  FetchAdditionalPropertyValue,\r\n  FetchAdditionalPropertyValueSuccess,\r\n  FetchAgencyNameList,\r\n  FetchAgencyNameListAnonymous,\r\n  FetchAgencyNameListAnonymousSuccess,\r\n  FetchAgencyNameListSuccess,\r\n  FetchAllParentLeadById,\r\n  FetchAllParentLeadByIdSuccess,\r\n  FetchBulkOperation,\r\n  FetchBulkOperationSuccess,\r\n  FetchCampaignList,\r\n  FetchCampaignListAnonymous,\r\n  FetchCampaignListAnonymousSuccess,\r\n  FetchCampaignListSuccess,\r\n  FetchChannelPartnerList,\r\n  FetchChannelPartnerListAnonymous,\r\n  FetchChannelPartnerListAnonymousSuccess,\r\n  FetchChannelPartnerListSuccess,\r\n  FetchCountryBasedCity,\r\n  FetchCountryBasedCitySuccess,\r\n  FetchDuplicateFeature,\r\n  FetchDuplicateFeatureSuccess,\r\n  FetchExcelUploadedList,\r\n  FetchExcelUploadedSuccess,\r\n  FetchExportStatus,\r\n  FetchExportStatusSuccess,\r\n  FetchLeadActiveCountSuccess,\r\n  FetchLeadAltCountryCode,\r\n  FetchLeadAltCountryCodeSuccess,\r\n  FetchLeadAppointmentsByProjects,\r\n  FetchLeadAppointmentsByProjectsSuccess,\r\n  FetchLeadBaseFilterCount,\r\n  FetchLeadBaseFilterCountSuccess,\r\n  FetchLeadById,\r\n  FetchLeadByIdWithArchive,\r\n  FetchLeadByIdWithArchiveSuccess,\r\n  FetchLeadCities,\r\n  FetchLeadCitiesSuccess,\r\n  FetchLeadClusterName,\r\n  FetchLeadClusterNameSuccess,\r\n  FetchLeadCommunities,\r\n  FetchLeadCommunitiesSuccess,\r\n  FetchLeadCountries,\r\n  FetchLeadCountriesSuccess,\r\n  FetchLeadCountryCode,\r\n  FetchLeadCountryCodeSuccess,\r\n  FetchLeadCurrency,\r\n  FetchLeadCurrencySuccess,\r\n  FetchLeadCustomTopFilters,\r\n  FetchLeadCustomTopFiltersChildren,\r\n  FetchLeadCustomTopFiltersChildrenSuccess,\r\n  FetchLeadCustomTopFiltersSuccess,\r\n  FetchLeadDroppedCount,\r\n  FetchLeadDroppedCountSuccess,\r\n  FetchLeadExport,\r\n  FetchLeadExportSuccess,\r\n  FetchLeadFlagsCountSuccess,\r\n  FetchLeadHistoryList,\r\n  FetchLeadHistoryListSuccess,\r\n  FetchLeadIdSuccess,\r\n  FetchLeadLandLine,\r\n  FetchLeadLandLineSuccess,\r\n  FetchLeadList,\r\n  FetchLeadListSuccess,\r\n  FetchLeadListV2,\r\n  FetchLeadLocalites,\r\n  FetchLeadLocalitesSuccess,\r\n  FetchLeadNationality,\r\n  FetchLeadNationalitySuccess,\r\n  FetchLeadNotInterestedCount,\r\n  FetchLeadNotInterestedCountSuccess,\r\n  FetchLeadNotesList,\r\n  FetchLeadNotesListSuccess,\r\n  FetchLeadPostalCode,\r\n  FetchLeadPostalCodeSuccess,\r\n  FetchLeadRotation,\r\n  FetchLeadRotationSuccess,\r\n  FetchLeadStates,\r\n  FetchLeadStatesSuccess,\r\n  FetchLeadStatusCount,\r\n  FetchLeadStatusCountSuccess,\r\n  FetchLeadSubCommunities,\r\n  FetchLeadSubCommunitiesSuccess,\r\n  FetchLeadTowerNames,\r\n  FetchLeadTowerNamesSuccess,\r\n  FetchLeadUnitName,\r\n  FetchLeadUnitNameSuccess,\r\n  FetchLeadZones,\r\n  FetchLeadZonesSuccess,\r\n  FetchLeadsCommunicationByIds,\r\n  FetchLeadsCommunicationByIdsSuccess,\r\n  FetchLocations,\r\n  FetchLocationsSuccess,\r\n  FetchMatchingPropertyOrProjectList,\r\n  FetchMatchingPropertyOrProjectListSuccess,\r\n  FetchMigrateExcelUploadedList,\r\n  FetchMigrateExcelUploadedSuccess,\r\n  FetchModuleWiseSearchProperties,\r\n  FetchModuleWiseSearchPropertiesSuccess,\r\n  FetchProjectList,\r\n  FetchProjectListSuccess,\r\n  FetchPropertyList,\r\n  FetchPropertyListSuccess,\r\n  FetchQRProjectList,\r\n  FetchQRProjectListSuccess,\r\n  FetchQRPropertyList,\r\n  FetchQRPropertyListSuccess,\r\n  FetchSubSourceList,\r\n  FetchSubSourceListSuccess,\r\n  FetchUploadTypeNameList,\r\n  FetchUploadTypeNameListSuccess,\r\n  HasLeadAltInfo,\r\n  HasLeadAltInfoSuccess,\r\n  HasLeadInfo,\r\n  HasLeadInfoSuccess,\r\n  LeadActionTypes,\r\n  LeadExcelUpload,\r\n  LeadExcelUploadSuccess,\r\n  MeetingOrVisitDone,\r\n  MeetingOrVisitDoneSuccess,\r\n  NavigateToLink,\r\n  NavigateToLinkSuccess,\r\n  PermanentDeleteLeads,\r\n  PermanentDeleteLeadsSuccess,\r\n  ReassignBoth,\r\n  ReassignBothSuccess,\r\n  ReassignLead,\r\n  ReassignLeadSuccess,\r\n  RestoreLeads,\r\n  RestoreLeadsSuccess,\r\n  SecondaryAssignLead,\r\n  SecondaryAssignLeadSuccess,\r\n  UpdateCardData,\r\n  UpdateFilterPayload,\r\n  UpdateInvoiceFilter,\r\n  UpdateIsLoadMore,\r\n  UpdateLead,\r\n  UpdateLeadNotes,\r\n  UpdateLeadNotesSuccess,\r\n  UpdateLeadShareCount,\r\n  UpdateLeadStatus,\r\n  UpdateLeadStatusSuccess,\r\n  UpdateLeadSuccess,\r\n  UpdateLeadsTagInfo,\r\n  UpdateMultipleLead,\r\n  UpdateMultipleLeadStatus,\r\n  UpdateMultipleLeadStatusSuccess,\r\n  UploadLeadDocument,\r\n  UploadLeadDocumentSuccess,\r\n  UploadMappedColumns,\r\n  UploadMigrateMappedColumns,\r\n  updateDuplicateAssign,\r\n  updateDuplicateAssignSuccess,\r\n  updateLeadRotation\r\n} from 'src/app/reducers/lead/lead.actions';\r\nimport {\r\n  getFiltersPayload,\r\n  getInvoiceFiltersPayload,\r\n  getIsLeadCustomStatusEnabled,\r\n  getIsLoadMore\r\n} from 'src/app/reducers/lead/lead.reducer';\r\nimport {\r\n  LeadPreviewChanged\r\n} from 'src/app/reducers/loader/loader.actions';\r\nimport { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';\r\nimport { LeadsRotationService } from 'src/app/services/controllers/leads-rotation.service';\r\nimport { GetLeadsService } from 'src/app/services/controllers/leads.service';\r\nimport { CommonService } from 'src/app/services/shared/common.service';\r\n\r\n@Injectable()\r\nexport class LeadEffects {\r\n\r\n  getLeadList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_LIST),\r\n      switchMap(() =>\r\n        this._store.select(window.location.pathname.includes('/invoice') ? getInvoiceFiltersPayload : getFiltersPayload).pipe(\r\n          take(1),\r\n          map((filterPayload: any) => window.location.pathname.includes('/invoice') ? new UpdateInvoiceFilter(filterPayload) : new UpdateFilterPayload(filterPayload)) // Properly return the action\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  addLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.ADD_LEAD),\r\n      map((action: AddLead) => action.payload),\r\n      switchMap((data: any) => {\r\n        return this.api.add(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Lead added Successfully');\r\n              this._store.dispatch(new AddLeadSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  addQRLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.ADD_QR_LEAD),\r\n      switchMap((action: AddQRLead) => {\r\n        return this.api.addQRLead(action.payload, action.templateId).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              // this._notificationService.success('Lead added Successfully');\r\n              let initialState: any = {\r\n                class: 'modal-450 modal-dialog-centered ph-modal-unset',\r\n                ignoreBackdropClick: false,\r\n              };\r\n              this.modalRef = this.modalService.show(\r\n                QRConfirmationComponent,\r\n                initialState\r\n              );\r\n              return new AddQRLeadSuccess();\r\n            }\r\n            return new AddQRLeadSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  addBulkLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.EXCEL_UPLOAD),\r\n      switchMap((action: ExcelUpload) => {\r\n        return this.api.uploadExcel(action.file).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Excel uploaded Successfully');\r\n              new FetchLeadList();\r\n              return new ExcelUploadSuccess(resp.data);\r\n            } else {\r\n              this._store.dispatch(new CloseModal());\r\n              this._notificationService.warn(`${resp.message}`);\r\n              new FetchLeadList();\r\n              return new ExcelUploadSuccess(resp.data);\r\n            }\r\n          }),\r\n          catchError((err) => {\r\n            this._notificationService.error(err?.error?.Message?.split(',')[0]);\r\n            return of(new OnError(err));\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  updateLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEAD),\r\n      switchMap((action: UpdateLead) => {\r\n        return this.api.update(action.payload, action.id).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Lead Updated Successfully');\r\n              this._store.dispatch(new UpdateLeadSuccess(resp.succeeded));\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  updateLeadNotes$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEAD_NOTES),\r\n      switchMap((action: UpdateLeadNotes) => {\r\n        return this.api.updateNotes(action.id, action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Notes Updated Successfully'\r\n              );\r\n              if (action.canFetchLeadList) {\r\n                // this._store.dispatch(new FetchLeadList());\r\n                this._store.dispatch(new LeadPreviewChanged());\r\n              }\r\n              const userDetails = localStorage.getItem('userDetails');\r\n              const currentUserName = userDetails ? JSON.parse(userDetails).given_name + ' ' + JSON.parse(userDetails).family_name || JSON.parse(userDetails).preferred_username || \"Current User\" : \"Current User\";\r\n              const newNote = {\r\n                fieldName: \"Notes\",\r\n                filterKey: 2,\r\n                oldValue: \"\",\r\n                newValue: action.payload.notes,\r\n                updatedBy: currentUserName,\r\n                updatedOn: new Date().toISOString(),\r\n                auditActionType: \"Updated\"\r\n              };\r\n              return new UpdateLeadNotesSuccess(newNote);\r\n            }\r\n            return new FetchLeadNotesListSuccess([]);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadNotes$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_NOTES_LIST),\r\n      map((action: FetchLeadNotesList) => action),\r\n      switchMap((data: any) =>\r\n        this.api.getNotesList(data.id).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadNotesListSuccess(resp.data);\r\n            }\r\n            return new FetchLeadNotesListSuccess([]);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  updateMultipleLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_MULTIPLE_LEAD),\r\n      switchMap((action: UpdateMultipleLead) => {\r\n        if (action.isUpdateStatus)\r\n          this._store.dispatch(new UpdateMultipleLeadStatus());\r\n        return this.api.updateBulkLeadStatus(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Leads updated Successfully');\r\n              if (action.isUpdateStatus) {\r\n                this._store.dispatch(new UpdateMultipleLeadStatusSuccess());\r\n              }\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadById$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_BY_ID),\r\n      map((action: FetchLeadById) => action.leadId),\r\n      switchMap((data: any) =>\r\n        this.api.get(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadIdSuccess(resp.data);\r\n            }\r\n            return new FetchLeadIdSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  updateStatus$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEAD_STATUS),\r\n      switchMap((action: UpdateLeadStatus) => {\r\n        return this.api.updateLeadStatus(action.payload, action.leadId).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Status Updated Successfully'\r\n              );\r\n              this._store.dispatch(new UpdateLeadStatusSuccess());\r\n              this._store.dispatch(new FetchLeadById(action.leadId));\r\n              return action.canFetchLeadList\r\n                ? new FetchLeadList()\r\n                : new EmptyEffect();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  updateLeadShareCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEAD_SHARE_COUNT),\r\n      switchMap((action: UpdateLeadShareCount) => {\r\n        return this.api.increaseShareCount(action.leadId).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Lead Successfully Shared');\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  updateLeadsTagInfo$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEADS_TAG_INFO),\r\n      mergeMap((action: UpdateLeadsTagInfo) => {\r\n        return this.api.updateLeadTags(action.payload, action.id).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Lead Successfully ${resp.message}`\r\n              );\r\n              return action.canFetchLeadList\r\n                ? new FetchLeadList()\r\n                : new EmptyEffect();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  emptyEffect$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.EMPTY_EFFECT),\r\n      switchMap((action: EmptyEffect) => {\r\n        return [];\r\n      })\r\n    )\r\n  );\r\n\r\n  reassignLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.REASSIGN_LEAD),\r\n      switchMap((action: ReassignLead) => {\r\n        return this.api.reassignLead(action.payload).pipe(\r\n          map((resp: any) => {\r\n            let msg =\r\n              'Lead' +\r\n              (action.payload.leadIds?.length > 1 ? 's' : '') +\r\n              ' Successfully ';\r\n            this._store.select(getFiltersPayload).subscribe((data: any) => {\r\n              msg +=\r\n                data?.leadVisibility === 3\r\n                  ? 'Assigned'\r\n                  : action.payload?.userIds?.length &&\r\n                    !action.payload?.userIds?.includes(EMPTY_GUID)\r\n                    ? 'Reassigned'\r\n                    : 'Unassigned';\r\n            });\r\n            if (resp.succeeded) {\r\n              if (action?.payload?.leadIds?.length === 1) {\r\n                this._store.dispatch(\r\n                  new FetchLeadById(action?.payload?.leadIds?.[0])\r\n                );\r\n              }\r\n              if (action.canFetchLeadList)\r\n                this._store.dispatch(new FetchLeadList());\r\n              this._notificationService.success(msg);\r\n              return new ReassignLeadSuccess(resp);\r\n            }\r\n            return new ReassignLeadSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  reassignBoth$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.REASSIGN_BOTH),\r\n      switchMap((action: ReassignBoth) => {\r\n        return this.api.reassignBoth(action.payload).pipe(\r\n          map((resp: any) => {\r\n            let msg =\r\n              'Lead' +\r\n              (action.payload.leadIds?.length > 1 ? 's' : '') +\r\n              ' Assignment Updated Successfully ';\r\n            if (resp.succeeded) {\r\n              if (action?.payload?.leadIds?.length === 1) {\r\n                this._store.dispatch(\r\n                  new FetchLeadById(action?.payload?.leadIds?.[0])\r\n                );\r\n              }\r\n              if (action.canFetchLeadList)\r\n                this._store.dispatch(new FetchLeadList());\r\n              this._notificationService.success(msg);\r\n              return new ReassignBothSuccess(resp);\r\n            }\r\n            return new ReassignBothSuccess();\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            Array.isArray(err?.error?.messages)\r\n              ? this._notificationService.error(err.error.messages[0])\r\n              : this._notificationService.error(err?.error?.messages);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  secondaryAssignLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.SECONDARY_ASSIGN_LEAD),\r\n      switchMap((action: SecondaryAssignLead) => {\r\n        return this.api.secondaryAssignLead(action.payload).pipe(\r\n          withLatestFrom(this._store.select(getFiltersPayload)),\r\n          map(([resp, filtersData]: [any, any]) => {\r\n            const count = action.payload?.leadIds?.length;\r\n            if (count < 50) {\r\n              let msg =\r\n                'Lead' +\r\n                (action.payload.leadIds?.length > 1 ? 's' : '') +\r\n                ' Successfully ';\r\n              msg +=\r\n                filtersData?.leadVisibility === 3\r\n                  ? 'Secondary Assigned'\r\n                  : action.payload?.userIds?.length &&\r\n                    !action.payload?.userIds?.includes(EMPTY_GUID)\r\n                    ? 'Secondary Reassigned'\r\n                    : 'Unassigned';\r\n              if (resp.succeeded) {\r\n                if (action.canFetchLeadList)\r\n                  this._store.dispatch(new FetchLeadList());\r\n                this._notificationService.success(msg);\r\n                return new SecondaryAssignLeadSuccess(resp);\r\n              }\r\n            } else {\r\n              this._store.dispatch(new FetchLeadList());\r\n              return new SecondaryAssignLeadSuccess(resp);\r\n            }\r\n            return new SecondaryAssignLeadSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkReassignLead$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_REASSIGN_LEAD),\r\n      switchMap((action: BulkReassignLead) => {\r\n        return this.api.bulkReassignLead(action.payload).pipe(\r\n          withLatestFrom(this._store.select(getFiltersPayload)),\r\n          map(([resp, filtersData]: [any, any]) => {\r\n            const count = action.payload?.leadIds?.length;\r\n            if (count < 50) {\r\n              let msg =\r\n                'Lead' +\r\n                (action.payload?.leadIds?.length > 1 ? 's' : '') +\r\n                ' Successfully ';\r\n\r\n              msg +=\r\n                filtersData?.leadVisibility === 3\r\n                  ? 'Assigned'\r\n                  : action.payload?.userIds?.toString() !== EMPTY_GUID\r\n                    ? 'Reassigned'\r\n                    : 'Unassigned';\r\n\r\n              if (resp.succeeded) {\r\n                this._store.dispatch(new FetchLeadList());\r\n                this._notificationService.success(msg);\r\n                return new BulkReassignLeadSuccess(resp);\r\n              }\r\n            } else {\r\n              this._store.dispatch(new FetchLeadList());\r\n              return new BulkReassignLeadSuccess(resp);\r\n            }\r\n            return new BulkReassignLeadSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  addBulkLeadExcel$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.LEAD_EXCEL_UPLOAD),\r\n      switchMap((action: LeadExcelUpload) => {\r\n        return this.api.uploadExcel(action.file).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Leads Excel uploaded Successfully'\r\n              );\r\n              return new LeadExcelUploadSuccess(resp.data);\r\n            } else {\r\n              this._store.dispatch(new CloseModal());\r\n              this._notificationService.warn(`${resp.message}`);\r\n              return new FetchLeadList();\r\n            }\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            Array.isArray(err?.error?.messages)\r\n              ? this._notificationService.error(err.error.messages[0])\r\n              : this._notificationService.error(err?.error?.messages);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  uploadMappedColumns$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPLOAD_MAPPED_COLUMNS),\r\n      switchMap((action: UploadMappedColumns) => {\r\n        return this.api.uploadMappedColumns(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              if (resp.data) {\r\n                if (resp.data?.excelUrl) {\r\n                  const dataCount = resp.message?.DataCount || '';\r\n                  this._notificationService.success(\r\n                    `${dataCount} Invalid Data Not Uploaded`\r\n                  );\r\n                  return new ExcelUploadSuccess(resp.data);\r\n                } else {\r\n                  this._notificationService.success(\r\n                    'Excel Uploaded Successfully'\r\n                  );\r\n                  return new ExcelUploadSuccess(resp.data);\r\n                }\r\n              } else {\r\n                this._notificationService.error(resp.message);\r\n              }\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err: any) => {\r\n            Array.isArray(err?.error?.messages)\r\n              ? this._notificationService.error(err.error.messages[0])\r\n              : this._notificationService.error(err?.error?.messages);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  uploadMigrateMappedColumns$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPLOAD_MIGRATE_MAPPED_COLUMNS),\r\n      switchMap((action: UploadMigrateMappedColumns) => {\r\n        return this.api.uploadMigrateMappingColumns(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              if (resp.data) {\r\n                if (resp.data?.excelUrl) {\r\n                  const dataCount = resp.message?.DataCount || '';\r\n                  this._notificationService.success(\r\n                    `${dataCount} Invalid Data Not Uploaded`\r\n                  );\r\n                  return new ExcelUploadSuccess(resp.data);\r\n                } else {\r\n                  this._notificationService.success(\r\n                    'Excel Uploaded Successfully'\r\n                  );\r\n                  return new ExcelUploadSuccess(resp.data);\r\n                }\r\n              } else {\r\n                this._notificationService.error(resp.message);\r\n              }\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err: any) => {\r\n            Array.isArray(err?.error?.messages)\r\n              ? this._notificationService.error(err.error.messages[0])\r\n              : this._notificationService.error(err?.error?.messages);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getMigrateExcelUploadedList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST),\r\n      map((action: FetchMigrateExcelUploadedList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api\r\n          .getMigrateExcelUploadedList(data?.pageNumber, data?.pageSize)\r\n          .pipe(\r\n            map((resp: any) => {\r\n              if (resp.succeeded) {\r\n                return new FetchMigrateExcelUploadedSuccess(resp);\r\n              }\r\n              return new FetchMigrateExcelUploadedSuccess();\r\n            }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n      })\r\n    )\r\n  );\r\n\r\n  doesLeadExists$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.HAS_LEAD_INFO),\r\n      switchMap((action: HasLeadInfo) => {\r\n        return this.api.doesLeadExists(action.data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new HasLeadInfoSuccess(resp.data);\r\n            }\r\n            return new HasLeadInfoSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  doesLeadAltExists$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.HAS_LEAD_ALT_INFO),\r\n      switchMap((action: HasLeadAltInfo) => {\r\n        return this.api.doesLeadExists(action.data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new HasLeadAltInfoSuccess(resp.data);\r\n            }\r\n            return new HasLeadAltInfoSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  uploadLeadDocument$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPLOAD_DOCUMENT),\r\n      switchMap((action: UploadLeadDocument) => {\r\n        return this.api.uploadLeadDocument(action.id, action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Document Added Successfully'\r\n              );\r\n              this._store.dispatch(new FetchLeadById(action.payload.leadId));\r\n              this._store.dispatch(new LeadPreviewChanged());\r\n              return new UploadLeadDocumentSuccess(action.payload.documents);\r\n            }\r\n            return new UploadLeadDocumentSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  deleteLeadDocument$ = createEffect(\r\n    () =>\r\n      this.actions$.pipe(\r\n        ofType(LeadActionTypes.DELETE_DOCUMENT),\r\n        switchMap((action: UploadLeadDocument) => {\r\n          return this.api.deletedLeadDocument(action.payload).pipe(\r\n            map((resp: any) => {\r\n              if (resp.succeeded) {\r\n                this._notificationService.success(\r\n                  'Lead Document Deleted Successfully'\r\n                );\r\n                this._store.dispatch(new FetchLeadById(action.payload.leadId));\r\n              }\r\n            }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n        })\r\n      ),\r\n    { dispatch: false }\r\n  );\r\n\r\n  getProjectList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_PROJECT_LIST),\r\n      map((action: FetchProjectList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getProjectList(data?.isWithArchive).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchProjectListSuccess(resp.data);\r\n            }\r\n            return new FetchProjectListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getQRProjectList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_QR_PROJECT_LIST),\r\n      map((action: FetchQRProjectList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getQRProjectList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchQRProjectListSuccess(resp.data);\r\n            }\r\n            return new FetchQRProjectListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getPropertyList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_PROPERTY_LIST),\r\n      map((action: FetchPropertyList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getPropertyList(data?.isWithArchive).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchPropertyListSuccess(resp.data);\r\n            }\r\n            return new FetchPropertyListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getQRPropertyList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_QR_PROPERTY_LIST),\r\n      map((action: FetchQRPropertyList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getQRPropertyList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchQRPropertyListSuccess(resp.data);\r\n            }\r\n            return new FetchQRPropertyListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getExcelUploadedList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_EXCEL_UPLOADED_LIST),\r\n      map((action: FetchExcelUploadedList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api\r\n          .getExcelUploadedList(data?.pageNumber, data?.pageSize)\r\n          .pipe(\r\n            map((resp: any) => {\r\n              if (resp.succeeded) {\r\n                return new FetchExcelUploadedSuccess(resp);\r\n              }\r\n              return new FetchExcelUploadedSuccess();\r\n            }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n      })\r\n    )\r\n  );\r\n\r\n  getExportStatusList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_EXPORT_STATUS),\r\n      map((action: FetchExportStatus) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getExportStatus(data.pageNumber, data.pageSize).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchExportStatusSuccess(resp);\r\n            }\r\n            return new FetchExportStatusSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadCurrency$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_CURRENCY_LIST),\r\n      map((action: FetchLeadCurrency) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getCurrency().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCurrencySuccess(resp.data);\r\n            }\r\n            return new FetchLeadCurrencySuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  communicationCount$ = createEffect(\r\n    () =>\r\n      this.actions$.pipe(\r\n        ofType(LeadActionTypes.COMMUNICATION_COUNT),\r\n        map((action: CommunicationCount) => action),\r\n        switchMap((data: any) => {\r\n          this._store.dispatch(\r\n            new CommunicationCountSuccess(data?.id, data?.payload)\r\n          );\r\n          return this.api.communicationCount(data.id, data.payload).pipe(\r\n            map((resp: any) => { }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n        })\r\n      ),\r\n    { dispatch: false }\r\n  );\r\n\r\n  communicationBulkCount$ = createEffect(\r\n    () =>\r\n      this.actions$.pipe(\r\n        ofType(LeadActionTypes.COMMUNICATION_BULK_COUNT),\r\n        map((action: CommunicationBulkCount) => action),\r\n        switchMap((data: any) => {\r\n          this._store.dispatch(\r\n            new CommunicationBulkCountSuccess(data?.payload)\r\n          );\r\n          return this.api.communicationBulkCount(data.payload).pipe(\r\n            map((resp: any) => { }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n        })\r\n      ),\r\n    { dispatch: false }\r\n  );\r\n\r\n  meetingOrVisitDone$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.MEETING_OR_VISIT_DONE),\r\n      map((action: MeetingOrVisitDone) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.meetingOrVisitDone(data.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Updated Successfully');\r\n              this._store.dispatch(new LeadPreviewChanged());\r\n              return new MeetingOrVisitDoneSuccess();\r\n            }\r\n            this._store.dispatch(new MeetingOrVisitDoneSuccess());\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLocations$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LOCATIONS),\r\n      map((action: FetchLocations) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLocations().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLocationsSuccess(resp.data);\r\n            }\r\n            return new FetchLocationsSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadCities$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_CITIES),\r\n      map((action: FetchLeadCities) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadCities().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCitiesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadCitiesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadStates$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_STATES),\r\n      map((action: FetchLeadStates) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadStates().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadStatesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadStatesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadCountries$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_COUNTRIES),\r\n      map((action: FetchLeadCountries) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadCountries().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCountriesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadCountriesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadSubCommunities$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_SUB_COMMUNITIES),\r\n      map((action: FetchLeadSubCommunities) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadSubCommunities().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadSubCommunitiesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadSubCommunitiesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadCommunities$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_COMMUNITIES),\r\n      map((action: FetchLeadCommunities) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadCommunities().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCommunitiesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadCommunitiesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadTowerNames$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_TOWER_NAME),\r\n      map((action: FetchLeadTowerNames) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadTowerNames().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadTowerNamesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadTowerNamesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadLocalites$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_LOCALITES),\r\n      map((action: FetchLeadLocalites) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLocalites().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadLocalitesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadLocalitesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadZones$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_ZONES),\r\n      map((action: FetchLeadZones) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadZones().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadZonesSuccess(resp.data);\r\n            }\r\n            return new FetchLeadZonesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  deleteLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.DELETE_LEADS),\r\n      switchMap((action: DeleteLeads) =>\r\n        this.api.deleteLeads(action.payload).pipe(\r\n          switchMap((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Deleted Successfully');\r\n              return [\r\n                new DeleteLeadsSuccess(),\r\n                new FetchLeadList(true, action.isInvoice),\r\n              ];\r\n            }\r\n            return of(new FetchLeadList());\r\n          }),\r\n          catchError((err) => {\r\n            this._notificationService.error('Failed to delete leads');\r\n            return of(new OnError(err));\r\n          })\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  restoreLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.RESTORE_LEADS),\r\n      map((action: RestoreLeads) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.restoreLeads(data.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Restored Successfully');\r\n              this._store.dispatch(new RestoreLeadsSuccess());\r\n              return new FetchLeadList(\r\n                true,\r\n                location?.href?.includes('/invoice')\r\n              );\r\n            }\r\n            return new FetchLeadList(\r\n              true,\r\n              location?.href?.includes('/invoice')\r\n            );\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getMatchingPropertyProjectList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_MATCHING_PROPERTIES_OR_PROJECTS),\r\n      map((action: FetchMatchingPropertyOrProjectList) => action),\r\n      switchMap((data: any) =>\r\n        this.commonService.getModuleList(data.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchMatchingPropertyOrProjectListSuccess(resp);\r\n            }\r\n            return new FetchMatchingPropertyOrProjectListSuccess({});\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getSubSourceList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_SUB_SOURCE_LIST),\r\n      map((action: FetchSubSourceList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getSubSourceList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchSubSourceListSuccess(resp.data);\r\n            }\r\n            return new FetchSubSourceListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkSource$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_SOURCE),\r\n      switchMap((action: BulkSource) => {\r\n        return this.api.bulkSource(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._store.dispatch(new FetchSubSourceList());\r\n              this._notificationService.success(\r\n                `Lead Source Updated Successfully`\r\n              );\r\n              this._store.dispatch(new BulkSourceSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkProjects$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_PROJECTS),\r\n      switchMap((action: BulkProjects) => {\r\n        return this.api.bulkProjects(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Lead Project(s) Updated Successfully`\r\n              );\r\n              this._store.dispatch(new BulkProjectsSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getAgencyNameList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST),\r\n      map((action: FetchAgencyNameList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getAgencyNames().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchAgencyNameListSuccess(resp.data);\r\n            }\r\n            return new FetchAgencyNameListSuccess({});\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getAgencyNameListAnonymous$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_AGENCY_NAME_LIST_ANONYMOUS),\r\n      map((action: FetchAgencyNameListAnonymous) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getAgencyNamesAnonymous().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchAgencyNameListAnonymousSuccess(resp.data);\r\n            }\r\n            return new FetchAgencyNameListAnonymousSuccess({});\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getChannelPartnerList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST),\r\n      map((action: FetchChannelPartnerList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getChannelPartnerList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchChannelPartnerListSuccess(resp.data);\r\n            }\r\n            return new FetchChannelPartnerListSuccess({});\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getChannelPartnerListAnonymous$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_CHANNEL_PARTNER_LIST_ANONYMOUS),\r\n      map((action: FetchChannelPartnerListAnonymous) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getChannelPartnerListAnonymous().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchChannelPartnerListAnonymousSuccess(resp.data);\r\n            }\r\n            return new FetchChannelPartnerListAnonymousSuccess({});\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  updateDuplicateAssign$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_DUPLICATE_ASSIGNMENT_LIST),\r\n      switchMap((action: updateDuplicateAssign) => {\r\n        return this.api.updateDuplicateAssign(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._store.dispatch(new FetchLeadList());\r\n              if (!resp.items.length) {\r\n                this._notificationService.success(`Lead Assigned Successfully`);\r\n              }\r\n              return new updateDuplicateAssignSuccess(resp);\r\n            }\r\n            return new updateDuplicateAssignSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getDuplicateFeature$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_DUPLICATE_FEATURE),\r\n      map((action: FetchDuplicateFeature) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getDuplicateFeature().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchDuplicateFeatureSuccess(resp.data);\r\n            }\r\n            return new FetchDuplicateFeatureSuccess({});\r\n          }),\r\n          catchError((err: any) => {\r\n            throwError(err);\r\n            return throwError(() => err);\r\n          })\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadsExport$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEADS_EXPORT),\r\n      map((action: FetchLeadExport) => action.payload),\r\n      switchMap((data: any) => {\r\n        return this.commonService.getModuleList(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadExportSuccess(resp.data);\r\n            }\r\n            return new FetchLeadExportSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadNotInterestedCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_NOT_INTERESTED_COUNT),\r\n      map((action: FetchLeadNotInterestedCount) => action.payload),\r\n      switchMap((data: any) => {\r\n        let filterPayload;\r\n        this._store.select(getFiltersPayload).subscribe((data: any) => {\r\n          filterPayload = {\r\n            ...data,\r\n            path: 'lead/counts/notInterested',\r\n          };\r\n        });\r\n        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadNotInterestedCountSuccess(resp.data);\r\n            }\r\n            return new FetchLeadBaseFilterCountSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadDroppedCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_DROPPED_COUNT),\r\n      map((action: FetchLeadDroppedCount) => action.payload),\r\n      switchMap((data: any) => {\r\n        let filterPayload;\r\n        this._store.select(getFiltersPayload).subscribe((data: any) => {\r\n          filterPayload = {\r\n            ...data,\r\n            path: 'lead/counts/dropped',\r\n          };\r\n        });\r\n        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadDroppedCountSuccess(resp.data);\r\n            }\r\n            return new FetchLeadDroppedCountSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  exportLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.EXPORT_LEADS),\r\n      switchMap((action: ExportLeads) => {\r\n        let isCustomStatusEnabled;\r\n        this._store\r\n          .select(getIsLeadCustomStatusEnabled)\r\n          .pipe(take(1))\r\n          .subscribe((isEnabled: any) => {\r\n            isCustomStatusEnabled = isEnabled;\r\n          });\r\n        return (\r\n          window.location.pathname.includes('/invoice')\r\n            ? this.api.exportLead(action.payload)\r\n            : isCustomStatusEnabled &&\r\n              window.location.pathname.includes('/leads/manage-leads')\r\n              ? this.api.customExportLead(action.payload)\r\n              : this.api.exportLead(action.payload)\r\n        ).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Leads are being exported in excel format`\r\n              );\r\n              return new ExportLeadsSuccess(resp);\r\n            }\r\n            return new ExportLeadsSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  communicationMessage$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.COMMUNICATION_MESSAGE),\r\n      switchMap((action: CommunicationMessage) => {\r\n        return this.api.communicationMessage(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new CommunicationMessageSuccess(resp);\r\n            }\r\n            return new CommunicationMessageSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  communicationBulkMessage$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.COMMUNICATION_BULK_MESSAGE),\r\n      switchMap((action: CommunicationBulkMessage) => {\r\n        return this.api.communicationBulkMessage(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new CommunicationBulkMessageSuccess(resp);\r\n            }\r\n            return new CommunicationBulkMessageSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getAppointmentsByProjects$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_APPOINTMENTS_BY_PROJECTS),\r\n      switchMap((action: FetchLeadAppointmentsByProjects) => {\r\n        const payload = { ...action?.payload };\r\n        payload.path = 'lead/getappointmentsbyprojects';\r\n        return this.commonService.getModuleListByAdvFilter(payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp?.succeeded) {\r\n              return new FetchLeadAppointmentsByProjectsSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadAppointmentsByProjectsSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadCommunicationsById$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEADS_COMMUNICATION_BY_IDS),\r\n      switchMap((action: FetchLeadsCommunicationByIds) => {\r\n        const payload: {\r\n          LeadIds: string[];\r\n          path: string;\r\n          showCommunicationCount?: boolean;\r\n        } = { ...action?.payload, path: 'lead/communications' };\r\n\r\n        if (!payload.showCommunicationCount) {\r\n          return of({ type: '[Lead] Noop' });\r\n        }\r\n\r\n        return this.commonService.getModuleListByAdvFilter(payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp?.succeeded) {\r\n              return new FetchLeadsCommunicationByIdsSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadsCommunicationByIdsSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  addProjectsToLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.ADD_LEAD_PROJECTS),\r\n      switchMap((action: AddLeadProjects) => {\r\n        return this.api.addProjects(action?.payload).pipe(\r\n          map((resp: any) => {\r\n            return new AddLeadProjectsSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  addLeadRotationGroup$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.ADD_LEAD_ROTATION_GROUP),\r\n      switchMap((action: AddLeadRotationGroup) =>\r\n        this.leadRotationApi.createLeadRotationGroup(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Rotation Group added Successfully'\r\n              );\r\n            }\r\n            return new FetchLeadRotation();\r\n          }),\r\n          catchError((error) => of(new OnError(error)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getLeadRotation = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_ROTATION),\r\n      switchMap((action: FetchLeadRotation) =>\r\n        this.leadRotationApi.getLeadRotation().pipe(\r\n          map((resp: any) => {\r\n            new FetchUsersListForReassignment();\r\n            return new FetchLeadRotationSuccess(resp);\r\n          }),\r\n          catchError((error) => of(new OnError(error)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  deleteLeadRotation$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.DELETE_LEAD_ROTATION),\r\n      switchMap((action: DeleteLeadRotation) =>\r\n        this.leadRotationApi.deleteLeadRotation(action.id).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Rotation Group deleted Successfully'\r\n              );\r\n            }\r\n            return new FetchLeadRotation();\r\n          }),\r\n          catchError((error) => of(new OnError(error)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  updateLeadRotation$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.UPDATE_LEAD_ROTATION),\r\n      switchMap((action: updateLeadRotation) =>\r\n        this.leadRotationApi.updateLeadRotation(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                'Lead Rotation Group updated Successfully'\r\n              );\r\n            }\r\n            return new FetchLeadRotation();\r\n          }),\r\n          catchError((error) => of(new OnError(error)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getLeadCustomTopFiltersChildren$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS_CHILDREN),\r\n      map((action: FetchLeadCustomTopFiltersChildren) => action),\r\n      switchMap((action: any) => {\r\n        let filterPayload: any;\r\n        this._store.select(getFiltersPayload).subscribe((data: any) => {\r\n          filterPayload = data;\r\n        });\r\n        let LeadVisibility: any = filterPayload?.LeadVisibility\r\n          ? filterPayload?.LeadVisibility\r\n          : 0;\r\n        let leadVisibility: any = filterPayload?.leadVisibility\r\n          ? filterPayload?.leadVisibility\r\n          : 0;\r\n        let ScheduledType: any = filterPayload?.ScheduledType\r\n          ? filterPayload?.ScheduledType\r\n          : 0;\r\n        let CustomFilterBaseIds: any =\r\n          (filterPayload?.customThirdLevelFilterId\r\n            ? [\r\n              filterPayload?.customThirdLevelFilterId,\r\n              filterPayload?.customSecondLevelFilterId,\r\n              filterPayload?.customFirstLevelFilterId,\r\n            ]\r\n            : null) ||\r\n          (filterPayload?.customSecondLevelFilterId\r\n            ? [\r\n              filterPayload?.customSecondLevelFilterId,\r\n              filterPayload?.customFirstLevelFilterId,\r\n            ]\r\n            : null) ||\r\n          (filterPayload?.customFirstLevelFilterId\r\n            ? [filterPayload?.customFirstLevelFilterId]\r\n            : null);\r\n        filterPayload = {\r\n          ...filterPayload,\r\n          path: 'lead/custom-filters-count-level2',\r\n          ScheduledType,\r\n          LeadVisibility,\r\n          leadVisibility,\r\n          pageNumber: null,\r\n          pageSize: null,\r\n          CustomFilterBaseIds,\r\n        };\r\n        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCustomTopFiltersChildrenSuccess(resp.data);\r\n            }\r\n            return new FetchLeadCustomTopFiltersChildrenSuccess([]);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  permanentDeleteLeads$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.PERMANENT_DELETE_LEADS),\r\n      map((action: PermanentDeleteLeads) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.permanentdeleteLeads(data.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success('Deleted Successfully');\r\n              this._store.dispatch(new PermanentDeleteLeadsSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadList();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getBulkOperationList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_BULK_OPERATION),\r\n      map((action: FetchBulkOperation) => action),\r\n      switchMap((data: any) => {\r\n        return this.api\r\n          .getBulkOperation(data.pageNumber, data.pageSize, data.moduleType)\r\n          .pipe(\r\n            map((resp: any) => {\r\n              if (resp.succeeded) {\r\n                return new FetchBulkOperationSuccess(resp);\r\n              }\r\n              return new FetchBulkOperationSuccess();\r\n            }),\r\n            catchError((err) => of(new OnError(err)))\r\n          );\r\n      })\r\n    )\r\n  );\r\n\r\n  getUploadTypeNameList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_UPLOADTYPENAME_LIST),\r\n      map((action: FetchUploadTypeNameList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getUploadTypeNameList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchUploadTypeNameListSuccess(resp.data);\r\n            }\r\n            return new FetchUploadTypeNameListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLeadAuditHistory$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_HISTORY),\r\n      map((action: FetchLeadHistoryList) => action),\r\n      switchMap((data: any) =>\r\n        this.api.getAuditHistory(data.id).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadHistoryListSuccess(resp.data);\r\n            }\r\n            return new FetchLeadHistoryListSuccess([]);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getAdditionalProperty$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_LIST),\r\n      map((action: FetchAdditionalPropertyList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getAdditionalProperties().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchAdditionalPropertyListSuccess(resp.data);\r\n            }\r\n            return new FetchAdditionalPropertyListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getAdditionalPropertyValue$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_ADDITIONAL_PROPERTY_VALUE),\r\n      map((action: FetchAdditionalPropertyValue) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getAPValues(data?.key).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchAdditionalPropertyValueSuccess(resp.data);\r\n            }\r\n            return new FetchAdditionalPropertyValueSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  navigateToLink$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.NAVIGATE_TO_LINK),\r\n      map((action: NavigateToLink) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.navigateToLink(data.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new NavigateToLinkSuccess(data.payload);\r\n            }\r\n            return new NavigateToLinkSuccess({});\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getCampaignList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST),\r\n      map((action: FetchCampaignList) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getCampaignList().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchCampaignListSuccess(resp.data);\r\n            }\r\n            return new FetchCampaignListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getCampaignListAnonymous$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_CAMPAIGN_LIST_ANONYMOUS),\r\n      map((action: FetchCampaignListAnonymous) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getCampaignListAnonymous().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchCampaignListAnonymousSuccess(resp.data);\r\n            }\r\n            return new FetchCampaignListAnonymousSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  fetchCountryBasedCity$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_COUNTRY_BASED_CITY),\r\n      map((action: FetchCountryBasedCity) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getCountryBasedCity().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchCountryBasedCitySuccess(resp?.data);\r\n            }\r\n            return new FetchCountryBasedCitySuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  customTopLevelFilters$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_CUSTOM_TOP_FILTERS),\r\n      withLatestFrom(\r\n        this._store.pipe(select((state: any) => state?.lead?.filtersPayload))\r\n      ),\r\n      switchMap(([action, filtersFromState]) => {\r\n        console.log(filtersFromState);\r\n\r\n        let CustomFilterBaseIds: any =\r\n          (filtersFromState?.CustomThirdLevelFilter\r\n            ? [\r\n              filtersFromState?.CustomThirdLevelFilter,\r\n              filtersFromState?.CustomSecondLevelFilter,\r\n              filtersFromState?.CustomFirstLevelFilter,\r\n            ]\r\n            : null) ||\r\n          (filtersFromState?.CustomSecondLevelFilter\r\n            ? [\r\n              filtersFromState?.CustomSecondLevelFilter,\r\n              filtersFromState?.CustomFirstLevelFilter,\r\n            ]\r\n            : null) ||\r\n          (filtersFromState?.CustomFirstLevelFilter\r\n            ? [filtersFromState?.CustomFirstLevelFilter]\r\n            : null) ||\r\n          null;\r\n        const filters = {\r\n          ...filtersFromState,\r\n          path: 'lead/custom-filters-count-level1',\r\n          CustomFilterBaseIds,\r\n        };\r\n\r\n        return this.commonService.getModuleListByAdvFilter(filters).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              localStorage.setItem('leadLevelFilter', JSON.stringify(resp?.data));\r\n              return new FetchLeadCustomTopFiltersSuccess(resp?.data);\r\n            } else {\r\n              return new FetchLeadCustomTopFiltersSuccess([]);\r\n            }\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  filterBaseCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_BASEFILTER_COUNT),\r\n      withLatestFrom(\r\n        this._store.pipe(\r\n          select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)\r\n        )\r\n      ),\r\n      switchMap(([action, filtersFromState]) => {\r\n        const fetchAction = action as FetchLeadBaseFilterCount;\r\n        console.log('Action filters:', fetchAction.filtersPayload);\r\n        console.log('State filters:', filtersFromState);\r\n\r\n        // Merge filtersFromState with any additional filters from action\r\n        // This preserves the existing state but allows overriding specific properties\r\n        const filters = {\r\n          ...filtersFromState,\r\n          ...(fetchAction.filtersPayload || {}),\r\n          path: 'lead/new/counts/basefilter',\r\n        };\r\nconsole.log('filters', filters);\r\n\r\n        return this.commonService.getModuleListByAdvFilter(filters).pipe(\r\n          map((resp: any) => {\r\n            return new FetchLeadBaseFilterCountSuccess(resp?.data || {});\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  fetchLeadList$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_LIST_V2),\r\n      withLatestFrom(this._store.pipe(select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)), this._store.select(getIsLoadMore)),\r\n      switchMap(([action, filtersFromState, isLoadmore]) => {\r\n        if (!isLoadmore && filtersFromState?.pageNumber === 1) {\r\n          this._store.dispatch(new ClearCardData());\r\n        };\r\n        this._store.dispatch(new UpdateIsLoadMore(false));\r\n\r\n        let filters = {\r\n          ...filtersFromState,\r\n          path:\r\n            this.isCustomStatusEnabled &&\r\n              !window.location.pathname.includes('/invoice')\r\n              ? 'lead/custom-filters'\r\n              : 'lead/new/all',\r\n        };\r\n\r\n        return this.commonService.getModuleListByAdvFilter(filters).pipe(\r\n          tap((resp: any) => this.dispatchLeadBatches(resp?.items, filters?.showCommunicationCount)),\r\n          switchMap((resp: any) => {\r\n            if (resp?.items?.length === 0 && filters?.pageNumber > 1) {\r\n              filters = {\r\n                ...filters,\r\n                pageNumber: filters?.pageNumber - 1\r\n              }\r\n              if (window.location.pathname.includes('/invoice')) {\r\n                this._store.dispatch(new UpdateInvoiceFilter(filters));\r\n              } else {\r\n                this._store.dispatch(new UpdateFilterPayload(filters));\r\n              }\r\n              return EMPTY\r\n            }\r\n            return of(\r\n              new FetchLeadListSuccess(resp),\r\n              new UpdateCardData(resp?.items)\r\n            )\r\n          }\r\n          ),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  private dispatchLeadBatches(items: any[] = [], showCommunicationCount: any): void {\r\n    if (!items?.length) return;\r\n\r\n    const leadIds = items.map((lead: any) => lead?.id).filter(Boolean);\r\n    const batchSize = 50;\r\n\r\n    for (let i = 0; i < leadIds.length; i += batchSize) {\r\n      const payload = {\r\n        LeadIds: leadIds.slice(i, i + batchSize),\r\n        showCommunicationCount: showCommunicationCount\r\n      };\r\n      this._store.dispatch(new FetchLeadsCommunicationByIds(payload));\r\n    }\r\n  }\r\n\r\n  fetchLeadActiveCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_ACTIVE_COUNT),\r\n      withLatestFrom(\r\n        this._store.pipe(\r\n          select((state: any) => window.location.pathname.includes('/invoice') ? state?.lead?.invoiceFiltersPayload : state?.lead?.filtersPayload)\r\n        )\r\n      ),\r\n      switchMap(([action, filtersFromState]) => {\r\n        const fetchAction = action as FetchActiveCount;\r\n        console.log('Active Count - Action filters:', fetchAction.filtersPayload);\r\n        console.log('Active Count - State filters:', filtersFromState);\r\n\r\n        // Merge filtersFromState with any additional filters from action\r\n        const filters = {\r\n          ...filtersFromState,\r\n          ...(fetchAction.filtersPayload || {}),\r\n          path: 'lead/counts/active',\r\n        };\r\n        return this.commonService.getModuleListByAdvFilter(filters).pipe(\r\n          map((resp: any) => new FetchLeadActiveCountSuccess(resp?.data || {}))\r\n        )\r\n      }), catchError((err) => of(new OnError(err)))\r\n    )\r\n  );\r\n\r\n  getLeadFlagCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_FLAGS_COUNT),\r\n      withLatestFrom(\r\n        this._store.pipe(\r\n          select((state: any) =>\r\n            window.location.pathname.includes('/invoice')\r\n              ? state?.lead?.invoiceFiltersPayload\r\n              : state?.lead?.filtersPayload\r\n          )\r\n        )\r\n      ),\r\n      switchMap(([action, filtersFromState]) => {\r\n        const filters = {\r\n          ...filtersFromState,\r\n          path: 'lead/counts/custom-flags',\r\n        };\r\n        return this.commonService\r\n          .getModuleListByAdvFilter(filters)\r\n          .pipe(\r\n            map((resp: any) => new FetchLeadFlagsCountSuccess(resp?.data || []))\r\n          );\r\n      }),\r\n      catchError((err) => of(new OnError(err)))\r\n    )\r\n  );\r\n\r\n  fetchLeadStatusCount$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_STATUS_COUNT),\r\n      withLatestFrom(\r\n        this._store.pipe(\r\n          select((state: any) =>\r\n            window.location.pathname.includes('/invoice')\r\n              ? state?.lead?.invoiceFiltersPayload\r\n              : state?.lead?.filtersPayload\r\n          )\r\n        )\r\n      ),\r\n      switchMap(([action, filtersFromState]) => {\r\n        console.log('filtersFromState', filtersFromState);\r\n        const filters = {\r\n          ...filtersFromState,\r\n          path: 'lead/counts/statuses',\r\n        };\r\n        return this.commonService\r\n          .getModuleListByAdvFilter(filters)\r\n          .pipe(\r\n            map(\r\n              (resp: any) =>\r\n                new FetchLeadStatusCountSuccess(resp?.data?.items || [])\r\n            )\r\n          );\r\n      }),\r\n      catchError((err) => of(new OnError(err)))\r\n    )\r\n  );\r\n\r\n  updateFiltersPayload$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(\r\n        LeadActionTypes.UPDATE_FILTER_PAYLOAD,\r\n        LeadActionTypes.UPDATE_INVOICE_FILTER\r\n      ),\r\n      switchMap(async (action: any) => {\r\n        this.isCustomStatusEnabled = await this._store\r\n          .select(getIsLeadCustomStatusEnabled)\r\n          .pipe(\r\n            map((data: any) => data),\r\n            take(1)\r\n          )\r\n          .toPromise();\r\n        const showFilterCount = action.filter?.showFilterCount;\r\n        const dontCallApi = action.filter?.dontCallApi;\r\n\r\n        if (\r\n          !this.isCustomStatusEnabled ||\r\n          window.location.pathname.includes('/invoice')\r\n        ) {\r\n          if (showFilterCount && (!dontCallApi)) {\r\n            this._store.dispatch(new FetchActiveCount());\r\n            this._store.dispatch(new FetchLeadStatusCount());\r\n          }\r\n        } else if (showFilterCount && (!dontCallApi)) {\r\n          this._store.dispatch(new FetchLeadCustomTopFilters());\r\n        }\r\n        // this._store.dispatch(new FetchLeadFlagsCount())\r\n        this._store.dispatch(new FetchLeadListV2());\r\n        if (showFilterCount && (!dontCallApi)) {\r\n          return new FetchLeadBaseFilterCount();\r\n        }\r\n        return { type: '[Lead] Noop' };\r\n      })\r\n    )\r\n  );\r\n\r\n  getNationality$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_NATIONALITY),\r\n      map((action: FetchLeadNationality) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getNationality().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadNationalitySuccess(resp?.data);\r\n            }\r\n            return new FetchLeadNationalitySuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getClusterNames$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_CLUSTER_NAME),\r\n      map((action: FetchLeadClusterName) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadClusterName().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadClusterNameSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadClusterNameSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getUnitNames$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_UNIT_NAME),\r\n      map((action: FetchLeadUnitName) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadUnitName().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadUnitNameSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadUnitNameSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getAltCountryCode$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_ALT_COUNTRY_CODE),\r\n      map((action: FetchLeadAltCountryCode) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadAltCountryCode().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadAltCountryCodeSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadAltCountryCodeSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getCountryCode$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_COUNTRY_CODE),\r\n      map((action: FetchLeadCountryCode) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadCountryCode().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadCountryCodeSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadCountryCodeSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getParentLeadById$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_ALL_PARENT_LEAD_BY_ID),\r\n      map((action: FetchAllParentLeadById) => action.leadId),\r\n      switchMap((data: any) =>\r\n        this.api.getParentLeadData(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchAllParentLeadByIdSuccess(resp.data);\r\n            }\r\n            return new FetchAllParentLeadByIdSuccess(resp.data);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getLeadByIdWithArchive$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_BY_ID_WITH_ARCHIVE),\r\n      map((action: FetchLeadByIdWithArchive) => action.leadId),\r\n      switchMap((data: any) =>\r\n        this.api.getLeadByIdWithArchive(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadByIdWithArchiveSuccess(resp.data);\r\n            }\r\n            return new FetchLeadByIdWithArchiveSuccess(resp.data);\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        )\r\n      )\r\n    )\r\n  );\r\n\r\n  getPostalCode$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_POSTAL_CODE),\r\n      map((action: FetchLeadPostalCode) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadPostalCode().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadPostalCodeSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadPostalCodeSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getLandLine$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_LEAD_LANDLINE),\r\n      map((action: FetchLeadLandLine) => action),\r\n      switchMap((data: any) => {\r\n        return this.api.getLeadLandLine().pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchLeadLandLineSuccess(resp?.data);\r\n            }\r\n            return new FetchLeadLandLineSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  getModuleWiseSearchProperties$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.FETCH_MODULE_WISE_SEARCH_PROPERTIES),\r\n      map((action: FetchModuleWiseSearchProperties) => action.moduleType),\r\n      switchMap((data: any) => {\r\n        return this.api.getModuleWiseSearchProperties(data).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              return new FetchModuleWiseSearchPropertiesSuccess(resp?.data);\r\n            }\r\n            return new FetchModuleWiseSearchPropertiesSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkAgency$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_AGENCY),\r\n      switchMap((action: BulkAgency) => {\r\n        return this.api.bulkUpdateAgency(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Lead Agency(s) Updated Successfully`\r\n              );\r\n              this._store.dispatch(new BulkAgencySuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkChannelPartner$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_CHANNEL_PARTNER),\r\n      switchMap((action: BulkChannelPartner) => {\r\n        return this.api.bulkUpdateChannelPartner(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Lead Channel partner(s) Updated Successfully`\r\n              );\r\n              this._store.dispatch(new BulkChannelPartnerSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  bulkCampaign$ = createEffect(() =>\r\n    this.actions$.pipe(\r\n      ofType(LeadActionTypes.BULK_CAMPAIGN),\r\n      switchMap((action: BulkCampaign) => {\r\n        return this.api.bulkUpdateCampaign(action.payload).pipe(\r\n          map((resp: any) => {\r\n            if (resp.succeeded) {\r\n              this._notificationService.success(\r\n                `Lead Campaign(s) Updated Successfully`\r\n              );\r\n              this._store.dispatch(new BulkCampaignSuccess());\r\n              return new FetchLeadList();\r\n            }\r\n            return new FetchLeadListSuccess();\r\n          }),\r\n          catchError((err) => of(new OnError(err)))\r\n        );\r\n      })\r\n    )\r\n  );\r\n\r\n  isCustomStatusEnabled: boolean = false;\r\n\r\n  constructor(\r\n    private actions$: Actions,\r\n    private api: GetLeadsService,\r\n    private leadRotationApi: LeadsRotationService,\r\n    private _notificationService: NotificationsService,\r\n    private _store: Store<AppState>,\r\n    private commonService: CommonService,\r\n    private router: Router,\r\n    private modalService: BsModalService,\r\n    private modalRef: BsModalRef\r\n  ) { }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}