{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/reports/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpParams } from '@angular/common/http';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport * as moment from 'moment';\nimport { EMPTY_GUID, PROPERTY_BUDGET_FILTER, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';\nimport { BHKType, DateRange, DoneStatus, EnquiryType, Facing, FurnishStatus, Gender, LeadSource, MaritalStatusType, OfferType, PossessionType, PropertyPriceFilter, PropertyStatus, PurposeType, SaleType } from 'src/app/app.enum';\nimport { environment as env } from 'src/environments/environment';\nexport const getEnvDetails = (isDomain = false) => {\n  let extension = '';\n  let defaultDomain = '';\n\n  switch (env.envt) {\n    case 'dev':\n      extension = '.leadratd.com';\n      defaultDomain = 'alpha';\n      break;\n\n    case 'qa':\n      extension = '.leadrat.info';\n      defaultDomain = 'helium';\n      break;\n\n    case 'uat':\n      // extension = '.leadrat.app';\n      extension = '.dhinwa.com';\n      defaultDomain = 'uat';\n      break;\n\n    case 'prod':\n      extension = '.leadrat.com';\n      defaultDomain = 'black';\n      break;\n  }\n\n  return isDomain ? defaultDomain : extension;\n};\nexport const getTenantName = () => {\n  let subDomain = '';\n\n  if (location.href.split('.').length > 1) {\n    if (location.href.split('.')[0].includes('www')) {\n      subDomain = location.href.split('.')[1];\n    } else {\n      subDomain = location.href.split('.')[0].split('//')[1];\n    }\n  }\n\n  return subDomain || localStorage.getItem('subDomain') || getEnvDetails(true) || '';\n};\nexport const getAppName = () => {\n  let appName = '';\n\n  switch (env.envt) {\n    case 'dev':\n      appName = 'leadrat';\n      break;\n\n    case 'qa':\n      appName = 'leadrat';\n      break;\n\n    case 'uat':\n      appName = 'dhinwa';\n      break;\n\n    case 'prod':\n      appName = 'leadrat';\n      break;\n  }\n\n  return appName;\n};\nexport const getAppImages = () => {\n  let subDomain = getTenantName();\n\n  switch (true) {\n    // case env.envt == 'dev' && subDomain == 'carbon':\n    //   return {\n    //     appFavIcon: 'assets/images/favicon.ico',\n    //     appLogo: 'assets/images/app-logo-hj.svg',\n    //     appText: 'assets/images/app-logo-text-hj.svg',\n    //     appFull: 'assets/images/app-logo-text-hj-blue.svg',\n    //     appLoader: 'assets/images/app-text-shadow.svg',\n    //   };\n    case env.envt == 'qa' && subDomain == 'helium':\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-kunj4u.svg',\n        appText: 'assets/images/app-text-kunj4u.svg',\n        appFull: 'assets/images/app-logo-text-kunj4u.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n    // case env.envt == 'uat' && subDomain == 'app':\n    //   return {\n    //     appFavIcon: 'assets/images/favicon.ico',\n    //     appLogo: 'assets/images/app-logo-dhinwa.svg',\n    //     appText: 'assets/images/app-logo-text-dhinwa.svg',\n    //     appFull: 'assets/images/app-logo-text-dhinwa-red.svg',\n    //     appLoader: 'assets/images/app-text-dhinwa.svg',\n    //   };\n\n    case env.envt == 'prod' && subDomain == 'hj':\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-hj.svg',\n        appText: 'assets/images/app-logo-text-hj.svg',\n        appFull: 'assets/images/app-logo-text-hj-blue.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n\n    case env.envt == 'prod' && subDomain == 'prowinproperties':\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-prowin.svg',\n        appText: 'assets/images/app-text-prowin.svg',\n        appFull: 'assets/images/app-logo-text-prowin.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n\n    case env.envt == 'prod' && subDomain == 'realtors-hub':\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-rh.svg',\n        appText: 'assets/images/app-text-rh.svg',\n        appFull: 'assets/images/app-logo-text-rh.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n\n    case env.envt == 'prod' && subDomain == 'kunj4u':\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-kunj4u.svg',\n        appText: 'assets/images/app-text-kunj4u.svg',\n        appFull: 'assets/images/app-logo-text-kunj4u.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n\n    default:\n      return {\n        appFavIcon: 'assets/images/favicon.ico',\n        appLogo: 'assets/images/app-logo-green.svg',\n        appText: 'assets/images/app-logo-text-green.svg',\n        appFull: 'assets/images/app-logo-text-green.svg',\n        appLoader: 'assets/images/app-text-shadow.svg'\n      };\n  }\n};\nexport const getPages = (totalCount, pageSize) => Math.ceil(totalCount / pageSize);\nexport const capitalizeFirstLetter = string => string.charAt(0).toUpperCase() + string.slice(1);\nexport const patchFormControlValue = (form, formControlName, value) => {\n  form.patchValue({\n    [formControlName]: value\n  });\n};\nexport const getTimeZoneDate = (date, userTimeZone, returnType) => {\n  // 2024-10-03T10:00:00Z\n  const offset = ((userTimeZone === null || userTimeZone === void 0 ? void 0 : userTimeZone.charAt(0)) === '-' ? '' : '+') + userTimeZone;\n  const formats = {\n    timeWithMeridiem: 'hh:mm A',\n    dayMonthYear: 'DD-MM-YYYY',\n    dayMonthYearText: 'DD MMM, YYYY',\n    fullDateTime: 'DD-MM-YYYY hh:mm:ss A',\n    dateTimeDefault: 'DD-MM-YYYY hh:mm A',\n    dateWithTime: 'DD MMM, YYYY | hh:mm A',\n    dateWithFullTime: 'DD MMM, YYYY | hh:mm:ss A',\n    monthYear: 'MMM yyyy',\n    dayMonth: 'DD MMM',\n    ISO: 'YYYY-MM-DDTHH:mm:ss'\n  };\n  const format = formats[returnType] || formats['dateTimeDefault'];\n  return moment(date).utcOffset(offset).format(format);\n};\nexport const setTimeZoneDate = (date, baseUTcOffset) => {\n  if (!date) return null;\n  const updatedDate = new Date(date);\n  updatedDate.setHours(0, 0, 0, 0);\n  const formattedDate = updatedDate.getFullYear() + '-' + String(updatedDate.getMonth() + 1).padStart(2, '0') + '-' + String(updatedDate.getDate()).padStart(2, '0') + 'T' + String(updatedDate.getHours()).padStart(2, '0') + ':' + String(updatedDate.getMinutes()).padStart(2, '0') + ':' + String(updatedDate.getSeconds()).padStart(2, '0');\n  const Offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\n  const convertedTime = convertToUtc(formattedDate, Offset);\n  return convertedTime;\n};\nexport const patchTimeZoneDate = (dateString, baseUTcOffset) => {\n  // 2024-10-15T12:00:00Z\n  if (!dateString) {\n    return null;\n  }\n\n  const date = dateConverted(new Date(dateString));\n  let totalOffsetMinutes;\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\n  const sign = offset.startsWith('-') ? -1 : 1;\n  const offsetParts = offset.replace('-', '').split(':').map(Number);\n  totalOffsetMinutes = sign * (offsetParts[0] * 60 + offsetParts[1]);\n  const adjustedDate = new Date(date.getTime() + totalOffsetMinutes * 60 * 1000); // Tue Oct 15 2024 00:00:00 GMT+1400 (Line Islands Time)\n\n  return adjustedDate;\n};\nexport const convertToUtc = (localTime, baseUTcOffset) => {\n  const localDate = moment.utc(localTime);\n  const utcDate = localDate.clone().utcOffset(-moment.duration(baseUTcOffset).asMinutes());\n  return utcDate.format('YYYY-MM-DDTHH:mm:ss[Z]');\n};\nexport const setTimeZoneDateWithTime = (date, baseUTcOffset) => {\n  if (!date) return null;\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\n  const localeTimeString = new Date(date);\n  const formattedDate = localeTimeString.getFullYear() + '-' + String(localeTimeString.getMonth() + 1).padStart(2, '0') + '-' + String(localeTimeString.getDate()).padStart(2, '0') + 'T' + String(localeTimeString.getHours()).padStart(2, '0') + ':' + String(localeTimeString.getMinutes()).padStart(2, '0') + ':' + String(localeTimeString.getSeconds()).padStart(2, '0');\n  const convertedTime = convertToUtc(formattedDate, offset);\n  return convertedTime;\n};\nexport const patchTimeZoneWithTime = (utcDateInput, baseUTcOffset) => {\n  if (!utcDateInput) return null;\n  const utcDate = new Date(utcDateInput);\n\n  if (isNaN(utcDate.getTime())) {\n    throw new Error('Invalid Date');\n  }\n\n  if (!baseUTcOffset) return utcDate;\n  const utcDateString = utcDate.toISOString();\n  const localTimeString = convertToLocalTime(utcDateString, baseUTcOffset);\n  return new Date(localTimeString);\n};\nexport const convertToLocalTime = (utcTime, offset) => {\n  const utcDate = moment.utc(utcTime);\n  const offsetMinutes = moment.duration(offset).asMinutes();\n  const localTime = utcDate.clone().utcOffset(offsetMinutes);\n  return localTime.format('YYYY-MM-DDTHH:mm:ss');\n};\nexport const setTimeZoneTime = (date, baseUTcOffset) => {\n  if (!date) return null;\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\n  const isNegative = offset.startsWith('-');\n  const [offsetHours, offsetMinutes] = offset.replace('+', '').replace('-', '').split(':').map(Number);\n  const totalOffsetMinutes = offsetHours * 60 + offsetMinutes;\n  const utcDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);\n  const utcHours = utcDate.getUTCHours();\n  const utcMinutes = utcDate.getUTCMinutes();\n  const utcSeconds = utcDate.getUTCSeconds();\n  let adjustedTotalMinutes;\n\n  if (isNegative) {\n    adjustedTotalMinutes = utcHours * 60 + utcMinutes + totalOffsetMinutes;\n  } else {\n    adjustedTotalMinutes = utcHours * 60 + utcMinutes - totalOffsetMinutes;\n  }\n\n  const adjustedHours = Math.floor(adjustedTotalMinutes / 60) % 24;\n  const adjustedMinutes = adjustedTotalMinutes % 60;\n  const formattedTime = `${String((adjustedHours + 24) % 24).padStart(2, '0')}:${String((adjustedMinutes + 60) % 60).padStart(2, '0')}:${String(utcSeconds).padStart(2, '0')}`;\n  return formattedTime;\n};\nexport const patchTime = (timeString, offset) => {\n  //time - 15:30:00\n  if (!timeString) return null;\n  const currentDate = new Date();\n  const [time, modifier] = timeString.split(' ');\n  let [hours, minutes, seconds] = time.split(':').map(Number);\n  if (modifier === 'PM' && hours < 12) hours += 12;\n  if (modifier === 'AM' && hours === 12) hours = 0;\n  currentDate.setHours(hours, minutes, seconds || 0);\n  const baseoffset = offset ? offset : getSystemTimeOffset();\n  const isNegative = baseoffset.startsWith('-');\n  const [offsetHours, offsetMinutes] = baseoffset.replace('+', '').replace('-', '').split(':').map(Number);\n  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;\n  const adjustedTime = isNegative ? new Date(currentDate.getTime() - totalOffsetMillis) : new Date(currentDate.getTime() + totalOffsetMillis); // Thu Oct 24 2024 11:51:28 GMT+0530 (India Standard Time)\n\n  return adjustedTime;\n};\nexport const getTimeZoneTime = (timeString, offset) => {\n  if (!timeString) return null;\n  const [time, modifier] = timeString.split(' ');\n  let [hours, minutes, seconds] = time.split(':').map(Number);\n  if (modifier === 'PM' && hours < 12) hours += 12;\n  if (modifier === 'AM' && hours === 12) hours = 0;\n  const baseDate = new Date();\n  baseDate.setHours(hours, minutes, seconds || 0);\n  const baseOffset = offset ? offset : getSystemTimeOffset();\n  const isNegativeOffset = baseOffset.startsWith('-');\n  const [offsetHours, offsetMinutes] = baseOffset.replace('+', '').replace('-', '').split(':').map(Number);\n  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;\n  const adjustedDate = isNegativeOffset ? new Date(baseDate.getTime() - totalOffsetMillis) : new Date(baseDate.getTime() + totalOffsetMillis);\n  return adjustedDate.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  });\n};\nexport const getSystemTimeOffset = () => {\n  const offsetInMinutes = new Date().getTimezoneOffset();\n  const hours = Math.floor(Math.abs(offsetInMinutes) / 60);\n  const minutes = Math.abs(offsetInMinutes) % 60;\n  const sign = offsetInMinutes <= 0 ? '' : '-';\n  return `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;\n};\nexport const getSystemTimeZoneId = () => {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n};\nexport const dateConverted = date => {\n  if (!date) return null;\n  let convertedDate = new Date(date);\n  const timezoneOffset = convertedDate.getTimezoneOffset();\n  const offsetInMs = timezoneOffset * 60 * 1000;\n  convertedDate = new Date(convertedDate.getTime() + offsetInMs);\n  return convertedDate;\n};\nexport const changeCalendar = baseUTcOffset => {\n  const startAt = new Date();\n  const utcTime = new Date(startAt.getUTCFullYear(), startAt.getUTCMonth(), startAt.getUTCDate(), startAt.getUTCHours(), startAt.getUTCMinutes(), startAt.getUTCSeconds());\n  const offsetString = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\n  const [hours, minutes] = offsetString.split(':').map(Number);\n  const offsetMinutes = hours * 60 + (minutes || 0);\n  const adjustedTime = new Date(utcTime.getTime() + offsetMinutes * 60000);\n  return adjustedTime;\n};\nexport const formatDateToCustomString = (date, type) => {\n  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n  const day = date.getDate();\n  const monthIndex = date.getMonth();\n  const year = date.getFullYear();\n\n  if (type === 'month') {\n    return `${monthNames[monthIndex]} ${year}`;\n  }\n\n  return `${day} ${monthNames[monthIndex]} ${year}`;\n};\nexport const onPickerOpened = (date, type) => {\n  const observer = new MutationObserver(() => {\n    highlightCustomToday(date, type);\n  });\n  setTimeout(() => {\n    const calendarElement = document.querySelector('.owl-dt-calendar');\n\n    if (calendarElement) {\n      observer.observe(calendarElement, {\n        childList: true,\n        subtree: true\n      });\n      highlightCustomToday(date, type);\n    }\n  }, 100);\n};\n\nconst highlightCustomToday = (date, type) => {\n  const calendarCells = document.querySelectorAll('.owl-dt-calendar-cell');\n  const formattedMinAllowedDate = formatDateToCustomString(date, type);\n  calendarCells.forEach(cell => {\n    const ariaLabel = cell.getAttribute('aria-label');\n\n    if (ariaLabel) {\n      const cellDate = new Date(ariaLabel);\n\n      if (!isNaN(cellDate.getTime())) {\n        const formattedAriaLabelDate = formatDateToCustomString(cellDate, type);\n\n        if (formattedAriaLabelDate === formattedMinAllowedDate) {\n          if (!cell.classList.contains('owl-dt-calendar-cell-selected')) {\n            const cellContent = cell.querySelector('.owl-dt-calendar-cell-content');\n\n            if (cellContent) {\n              cellContent.classList.add('custom-today');\n            }\n          }\n        }\n      }\n    }\n  });\n};\n\nexport const getISODateFormat = dateTime => {\n  if (!dateTime) {\n    return null;\n  }\n\n  return `${moment(dateTime).format('YYYY-MM-DD')}T${moment(dateTime).format('HH:mm')}:00.000Z`;\n};\nexport const getISOOnlyDate = dateTime => {\n  if (!dateTime) {\n    return null;\n  }\n\n  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00.000Z`;\n};\nexport const getISODate = dateTime => {\n  if (!dateTime) {\n    return null;\n  }\n\n  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00`;\n};\nexport const validateAllFormFields = formGroup => {\n  Object.keys(formGroup.controls).forEach(field => {\n    const control = formGroup.get(field);\n\n    if (control instanceof FormControl) {\n      control.markAsTouched({\n        onlySelf: true\n      });\n    } else if (control instanceof FormGroup) {\n      validateAllFormFields(control);\n    }\n  });\n};\nexport const formatCurrency = (amount, locale, currencySymbol) => {\n  if (amount == null || amount == '') return '';\n  return `${currencySymbol} ${Number(amount).toLocaleString(locale)}`;\n};\nexport const sortArrayByKey = (array, keyName) => {\n  return array.sort((a, b) => a[keyName] > b[keyName] ? 1 : -1);\n};\nexport const getLeadStatusId = (status, statusList) => statusList.filter(item => item.actionName == status)[0].id;\nexport const toggleValidation = (validationType, formGroup, field, validators) => {\n  const control = formGroup.get(field);\n\n  if (control instanceof FormControl) {\n    switch (validationType) {\n      case VALIDATION_CLEAR:\n        control.clearValidators();\n        break;\n\n      case VALIDATION_SET:\n        control.setValidators(validators);\n        break;\n    }\n\n    control.updateValueAndValidity();\n  }\n};\nexport const getDateDiffInDays = (date1, date2) => moment(date1).isSameOrBefore(moment(date2), 'days');\nexport const isEmptyObject = obj => Object.keys(obj).length === 0 && obj.constructor === Object;\nexport const setPropertySubTypeList = (pType, propertyTypeList, isProject = false) => {\n  let propertySubTypeList;\n\n  if (!(propertyTypeList === null || propertyTypeList === void 0 ? void 0 : propertyTypeList.length)) {\n    return;\n  }\n\n  const [property] = propertyTypeList.filter(prop => prop.displayName === pType);\n\n  if ((property === null || property === void 0 ? void 0 : property.displayName) === 'Residential' && !isProject) {\n    propertySubTypeList = [...new Set(property === null || property === void 0 ? void 0 : property.childTypes.map(item => item.displayName))].map(item => {\n      return {\n        displayName: item\n      };\n    }) || [];\n  } else {\n    propertySubTypeList = (property === null || property === void 0 ? void 0 : property.childTypes) || [];\n  }\n\n  return propertySubTypeList;\n};\nexport const setPropertyBhkTypeList = (pType, propertyTypeList) => {\n  let propertyBhkTypeList;\n  const [property] = propertyTypeList.filter(prop => prop.displayName === pType);\n\n  if ((property === null || property === void 0 ? void 0 : property.displayName) === 'Residential') {\n    propertyBhkTypeList = [...new Set(property === null || property === void 0 ? void 0 : property.childTypes.map(item => item.bhkType))].map(item => {\n      return item;\n    }).filter(item => {\n      if (item != null) {\n        return {\n          bhkType: item\n        };\n      }\n\n      return item;\n    }) || [];\n  }\n\n  return propertyBhkTypeList;\n};\nexport const getPropertyTypeIds = (propertyTypeList, propertyType, propertySubTypes) => {\n  var _a;\n\n  const selectedPropertyType = propertyTypeList.find(pType => pType.displayName === propertyType);\n  if (!((_a = selectedPropertyType === null || selectedPropertyType === void 0 ? void 0 : selectedPropertyType.childTypes) === null || _a === void 0 ? void 0 : _a.length)) return [];\n\n  if (selectedPropertyType.displayName === 'Residential') {\n    return selectedPropertyType.childTypes.filter(pSubType => propertySubTypes.includes(pSubType.displayName)).map(pSubType => pSubType.id);\n  } else {\n    return selectedPropertyType.childTypes.filter(pSubType => propertySubTypes.includes(pSubType.displayName)).map(pSubType => pSubType.id);\n  }\n};\nexport const getPropertyTypeId = (propertyTypeList, propertyType, propertySubType) => {\n  const [selectedPropertyType] = propertyTypeList.filter(pType => pType.displayName == propertyType);\n  if (!(selectedPropertyType === null || selectedPropertyType === void 0 ? void 0 : selectedPropertyType.childTypes.length)) return;else if ((selectedPropertyType === null || selectedPropertyType === void 0 ? void 0 : selectedPropertyType.displayName) == 'Residential' && propertySubType != 'Plot') {\n    const [selectedPropertySubType] = selectedPropertyType === null || selectedPropertyType === void 0 ? void 0 : selectedPropertyType.childTypes.filter(pSubType => pSubType.displayName == propertySubType);\n    return selectedPropertySubType === null || selectedPropertySubType === void 0 ? void 0 : selectedPropertySubType.id;\n  } else {\n    const [selectedPropertySubType] = selectedPropertyType === null || selectedPropertyType === void 0 ? void 0 : selectedPropertyType.childTypes.filter(pSubType => pSubType.displayName == propertySubType);\n    return selectedPropertySubType === null || selectedPropertySubType === void 0 ? void 0 : selectedPropertySubType.id;\n  }\n};\nexport const istFormat = utc => moment.utc(utc).toDate();\nexport const getFormattedDate = (dateTime, format) => moment(new Date(dateTime)).utcOffset(0).format(format);\nexport const isValidFilePath = urlString => urlString.includes('data:');\nexport const getAWSImagePath = imagePath => {\n  return isValidFilePath(imagePath) ? imagePath : `${env.s3ImageBucketURL}${imagePath}`;\n};\nexport const getAreaUnit = (id, areaUnitList) => areaUnitList.filter(item => item.id === id)[0];\nexport const isTokenExpired = () => {\n  const idToken = localStorage.getItem('idToken');\n\n  if (idToken && idToken.length) {\n    const expirationTime = JSON.parse(atob(idToken.split('.')[1])).exp;\n    const isExpiredToken = Math.floor(new Date().getTime() / 1000) >= expirationTime;\n    return isExpiredToken;\n  }\n\n  return true;\n};\nexport const isEmptyGuid = guid => guid === EMPTY_GUID;\nexport const getAssignedToDetails = (userInfo, usersList, returnNameOnly = false) => {\n  var _a;\n\n  if (!userInfo || isEmptyGuid(userInfo) || !(usersList === null || usersList === void 0 ? void 0 : usersList.length)) return null;\n  const user = (_a = usersList === null || usersList === void 0 ? void 0 : usersList.filter(user => {\n    var _a, _b;\n\n    return user.id == userInfo || user.firstName == ((_a = userInfo === null || userInfo === void 0 ? void 0 : userInfo.split(' ')) === null || _a === void 0 ? void 0 : _a[0]) && user.lastName == ((_b = userInfo === null || userInfo === void 0 ? void 0 : userInfo.split(' ')) === null || _b === void 0 ? void 0 : _b[1]) || `${user.firstName} ${user.lastName}` === userInfo;\n  })) === null || _a === void 0 ? void 0 : _a[0];\n  return user ? returnNameOnly ? `${user.firstName} ${user.lastName}` : user : null;\n};\nexport const matchValidator = (control, controlTwo) => {\n  return () => {\n    if (control.value !== controlTwo.value) return {\n      match_password: 'Password and Confirm Password does not match'\n    };\n    return null;\n  };\n};\nexport const groupBy = (xs, key) => xs.reduce((rv, x) => {\n  (rv[x[key]] = rv[x[key]] || []).reverse().push(x);\n  return rv;\n}, {});\nexport const getIndexes = (property, array) => {\n  if (property === 'Source' || property === 'Sources' || property === 'LeadSources') {\n    array = array.map(item => {\n      return LeadSource[item];\n    });\n  } else if (property === 'BHKTypes') {\n    array = array.map(item => {\n      return BHKType[item];\n    });\n  } else if (property === 'Budget') {\n    array = array.map(item => {\n      return PROPERTY_BUDGET_FILTER.indexOf(item);\n    });\n  } else if (property === 'PriceRange') {\n    array = array.map(item => {\n      return PropertyPriceFilter[item];\n    });\n  } else if (property === 'enquiredFor') {\n    array = array.map(item => {\n      return EnquiryType[item];\n    });\n  } else if (property === 'MeetingOrVisitStatuses') {\n    array = array.map(item => {\n      return DoneStatus[item];\n    });\n  } else if (property === 'FurnishStatuses' || property === 'Furnished') {\n    array = array.map(item => {\n      return FurnishStatus[item];\n    });\n  } else if (property === 'OfferTypes') {\n    array = array.map(item => {\n      return OfferType[item];\n    });\n  } else if (property === 'Purposes') {\n    array = array.map(item => {\n      return PurposeType[item];\n    });\n  }\n\n  return array;\n};\nexport const formatBudget = (budget, currency) => {\n  const formatInternationalCurrency = (budget, symbol) => {\n    if (budget < 1000) {\n      return symbol + ' ' + (budget === null || budget === void 0 ? void 0 : budget.toString());\n    } else if (budget >= 1000 && budget < 1000000) {\n      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';\n    } else if (budget >= 1000000 && budget < 1000000000) {\n      return symbol + ' ' + (budget / 1000000).toFixed(2) + ' M';\n    } else {\n      return symbol + ' ' + (budget / 1000000000).toFixed(2) + ' B';\n    }\n  };\n\n  const formatINRCurrency = (budget, symbol) => {\n    if (budget < 1000) {\n      return symbol + ' ' + (budget === null || budget === void 0 ? void 0 : budget.toString());\n    } else if (budget >= 1000 && budget < 100000) {\n      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';\n    } else if (budget >= 100000 && budget < 10000000) {\n      return symbol + ' ' + (budget / 100000).toFixed(2) + ' Lacs';\n    } else {\n      return symbol + ' ' + (budget / 10000000).toFixed(2) + ' Cr';\n    }\n  };\n\n  if (currency === null) {\n    return budget === null || budget === void 0 ? void 0 : budget.toString();\n  }\n\n  if (currency === null || currency === void 0 ? void 0 : currency.includes('INR')) {\n    return formatINRCurrency(budget, 'INR');\n  } else {\n    return formatInternationalCurrency(budget, currency);\n  }\n};\nexport const onlyNumbers = event => {\n  const keyCode = event.keyCode;\n  const excludedKeys = [8, 37, 39, 46];\n\n  if (!(keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105 || excludedKeys.includes(keyCode))) {\n    event.preventDefault();\n  }\n};\nexport const onlyNumbersWithDecimal = (event, currentValue) => {\n  const keyCode = event.keyCode;\n  const excludedKeys = [8, 9, 37, 39, 46];\n  const isNumberKey = keyCode >= 48 && keyCode <= 57 || keyCode >= 96 && keyCode <= 105;\n  const isDecimalKey = keyCode === 190 || keyCode === 110;\n\n  if (!(isNumberKey || isDecimalKey || excludedKeys.includes(keyCode))) {\n    event.preventDefault();\n    return;\n  }\n\n  if ((keyCode === 190 || keyCode === 110) && currentValue.includes('.')) {\n    event.preventDefault();\n  }\n};\nexport const LeadTemplateMsg = (template, leadData, tenantName, defaultCurrency, header, footer, allUserList, userData, currentDate, module) => {\n  let combinedMsg = '';\n  const transformedAttributes = {};\n  let replacements = [];\n  template = template === null || template === void 0 ? void 0 : template.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  header = header === null || header === void 0 ? void 0 : header.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  footer = footer === null || footer === void 0 ? void 0 : footer.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n\n  const normalizeVar = variable => {\n    var _a;\n\n    return (_a = variable.toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '');\n  };\n\n  const processReplacements = (msg, replacements) => {\n    if (msg) {\n      replacements.forEach(item => {\n        var _a;\n\n        if (Array.isArray(item.var)) {\n          item.var.forEach(variable => {\n            var _a;\n\n            const normalizedVar = normalizeVar(variable);\n            msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, (_a = item === null || item === void 0 ? void 0 : item.text) === null || _a === void 0 ? void 0 : _a.trim());\n          });\n        } else {\n          const normalizedVar = normalizeVar(item.var);\n          msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, (_a = item === null || item === void 0 ? void 0 : item.text) === null || _a === void 0 ? void 0 : _a.trim());\n        }\n      });\n    }\n\n    return msg;\n  };\n\n  const processLeadData = (dataItem, index) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30, _31, _32, _33, _34, _35, _36, _37, _38, _39, _40, _41, _42, _43, _44, _45, _46, _47, _48, _49, _50, _51, _52, _53, _54, _55, _56, _57, _58, _59, _60, _61, _62, _63, _64, _65;\n\n    let date;\n    let remTime;\n    let remTimeStr;\n\n    if ((dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate)) {\n      date = new Date((dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate));\n      let today = new Date(currentDate);\n      remTime = date.getTime() - today.getTime();\n      remTimeStr = '';\n\n      if (remTime > 0) {\n        remTimeStr = Math.floor(remTime / (1000 * 60 * 60)) + ' hours ';\n        remTime = remTime % (1000 * 60 * 60);\n        remTimeStr += Math.floor(remTime / (1000 * 60)) + ' mins';\n      }\n    }\n\n    let msg = template;\n\n    if ((_a = dataItem.attributes) === null || _a === void 0 ? void 0 : _a.length) {\n      for (const attribute of dataItem.attributes) {\n        if (!(attribute === null || attribute === void 0 ? void 0 : attribute.attributeName.startsWith('Is'))) {\n          transformedAttributes[attribute === null || attribute === void 0 ? void 0 : attribute.attributeName] = attribute.value;\n        }\n      }\n    }\n\n    replacements = [{\n      var: '#leadName#',\n      text: (_b = dataItem === null || dataItem === void 0 ? void 0 : dataItem.name) === null || _b === void 0 ? void 0 : _b.replace(/\\s+/g, ' ').trim()\n    }, {\n      var: '#userName#',\n      text: `${userData === null || userData === void 0 ? void 0 : userData.firstName} ${userData === null || userData === void 0 ? void 0 : userData.lastName}`\n    }, {\n      var: '#userEmail#',\n      text: userData === null || userData === void 0 ? void 0 : userData.email\n    }, {\n      var: '#userMobile#',\n      text: userData === null || userData === void 0 ? void 0 : userData.phoneNumber\n    }, {\n      var: '#tenantName#',\n      text: tenantName || ''\n    }, {\n      var: ['#date#', '#Schedule Date#'],\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate) ? getTimeZoneDate((dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate), (_c = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _c === void 0 ? void 0 : _c.baseUTcOffset, 'dayMonthYear') : ''\n    }, {\n      var: '#PropertyMicrositeUrl#',\n      text: ((_d = dataItem === null || dataItem === void 0 ? void 0 : dataItem.properties) === null || _d === void 0 ? void 0 : _d.length) ? (_e = dataItem === null || dataItem === void 0 ? void 0 : dataItem.properties) === null || _e === void 0 ? void 0 : _e.map(property => getMSUrl(property.serialNo)).join(' , ') : ''\n    }, {\n      var: '#ProjectMicrositeUrl#',\n      text: ((_f = dataItem === null || dataItem === void 0 ? void 0 : dataItem.projects) === null || _f === void 0 ? void 0 : _f.length) ? (_g = dataItem === null || dataItem === void 0 ? void 0 : dataItem.projects) === null || _g === void 0 ? void 0 : _g.map(project => getMSUrl(project.serialNo, true)).join(' , ') : ''\n    }, {\n      var: ['#time#', '#ScheduleTime#'],\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate) ? getTimeZoneDate((dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduledDate) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem.scheduleDate), (_h = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _h === void 0 ? void 0 : _h.baseUTcOffset, 'timeWithMeridiem') : ''\n    }, {\n      var: ['#projectName#', '#Projects#'],\n      text: ((_j = dataItem === null || dataItem === void 0 ? void 0 : dataItem.projects) === null || _j === void 0 ? void 0 : _j.length) ? (_k = dataItem === null || dataItem === void 0 ? void 0 : dataItem.projects) === null || _k === void 0 ? void 0 : _k.map(project => project.name).join(', ') : ''\n    }, {\n      var: ['#propertyName#', '#Properties#'],\n      text: ((_l = dataItem === null || dataItem === void 0 ? void 0 : dataItem.properties) === null || _l === void 0 ? void 0 : _l.length) ? (_m = dataItem === null || dataItem === void 0 ? void 0 : dataItem.properties) === null || _m === void 0 ? void 0 : _m.map(property => property.title).join(', ') : ''\n    }, {\n      var: '#LowerBudget#',\n      text: ((_o = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _o === void 0 ? void 0 : _o.lowerBudget) ? ((_p = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _p === void 0 ? void 0 : _p.lowerBudget) + ' (' + formatBudget((_q = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _q === void 0 ? void 0 : _q.lowerBudget, ((_r = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _r === void 0 ? void 0 : _r.currency) || defaultCurrency) + ')' : ''\n    }, {\n      var: ['#priceRange#', '#UpperBudget#'],\n      text: ((_s = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _s === void 0 ? void 0 : _s.upperBudget) ? ((_t = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _t === void 0 ? void 0 : _t.upperBudget) + ' (' + formatBudget((_u = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _u === void 0 ? void 0 : _u.upperBudget, ((_v = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _v === void 0 ? void 0 : _v.currency) || defaultCurrency) + ')' : ''\n    }, {\n      var: ['#enquiryType#', '#EnquiryTypes#', '#EnquiredFor#', '#Enquired For#', '#SaleType#', '#Sale Type#'],\n      text: ((_w = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _w === void 0 ? void 0 : _w.enquiryTypes) ? (_z = (_y = [...((_x = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _x === void 0 ? void 0 : _x.enquiryTypes)]) === null || _y === void 0 ? void 0 : _y.map(type => EnquiryType[type])) === null || _z === void 0 ? void 0 : _z.join(', ') : ''\n    }, {\n      var: '#link#',\n      text: 'link'\n    }, {\n      var: '#remainingTime#',\n      text: remTime > 0 ? remTimeStr : '00:00 hrs'\n    }, {\n      var: ['#enquiredLocation#', '#Addresses#', '#Address#', '#PreferredLocation#'],\n      text: ((_0 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _0 === void 0 ? void 0 : _0.addresses) ? (_1 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _1 === void 0 ? void 0 : _1.addresses.map(address => getLocationDetailsByObj(address)).join(', ') : '--'\n    }, {\n      var: '#Lead Alternate Contact No#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.alternateContactNo) || ''\n    }, {\n      var: ['#Lead Contact No#', '#LeadFullContactNo#'],\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.contactNo) || ''\n    }, {\n      var: '#Lead Landline Number#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.landLine) || ''\n    }, {\n      var: '#Lead Email#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.email) || ''\n    }, {\n      var: '#Referral Name#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.referralName) || ''\n    }, {\n      var: '#Referral Contact No#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.referralContactNo) || ''\n    }, {\n      var: '#Referral Email#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.referralEmail) || ''\n    }, {\n      var: '#Company Name#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.companyName) || ''\n    }, {\n      var: ['#Primary Owner#', '#Assign To#'],\n      text: (() => {\n        const user = allUserList === null || allUserList === void 0 ? void 0 : allUserList.find(user => user.id === (dataItem === null || dataItem === void 0 ? void 0 : dataItem.assignTo));\n        return user ? user.firstName + ' ' + user.lastName : '';\n      })()\n    }, {\n      var: '#Secondary Owner#',\n      text: (() => {\n        const user = allUserList === null || allUserList === void 0 ? void 0 : allUserList.find(user => user.id === (dataItem === null || dataItem === void 0 ? void 0 : dataItem.secondaryUserId));\n        return user ? user.firstName + ' ' + user.lastName : '';\n      })()\n    }, {\n      var: '#Closing Manager#',\n      text: (() => {\n        const user = allUserList === null || allUserList === void 0 ? void 0 : allUserList.find(user => user.id === (dataItem === null || dataItem === void 0 ? void 0 : dataItem.closingManager));\n        return user ? user.firstName + ' ' + user.lastName : '';\n      })()\n    }, {\n      var: '#Sourcing Manager#',\n      text: (() => {\n        const user = allUserList === null || allUserList === void 0 ? void 0 : allUserList.find(user => user.id === (dataItem === null || dataItem === void 0 ? void 0 : dataItem.sourcingManager));\n        return user ? user.firstName + ' ' + user.lastName : '';\n      })()\n    }, {\n      var: '#Channel Partner Name#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.channelPartners) ? dataItem.channelPartners.map(partner => partner.firmName).join(', ') : ''\n    }, {\n      var: '#Designation#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.designation) || ''\n    }, {\n      var: '#Sale Type#',\n      text: SaleType[(_2 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _2 === void 0 ? void 0 : _2.saleType] || ''\n    }, {\n      var: '#Lead Source#',\n      text: LeadSource[(_3 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _3 === void 0 ? void 0 : _3.leadSource] || ((_5 = (_4 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _4 === void 0 ? void 0 : _4.prospectSource) === null || _5 === void 0 ? void 0 : _5.displayName) || ''\n    }, {\n      var: '#Sub Source#',\n      text: ((_6 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _6 === void 0 ? void 0 : _6.subSource) || ''\n    }, {\n      var: '#Property Type#',\n      text: ((_9 = (_8 = (_7 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _7 === void 0 ? void 0 : _7.propertyTypes) === null || _8 === void 0 ? void 0 : _8[0]) === null || _9 === void 0 ? void 0 : _9.displayName) || ''\n    }, {\n      var: '#PropertySubType#',\n      text: ((_12 = (_11 = (_10 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _10 === void 0 ? void 0 : _10.propertyTypes) === null || _11 === void 0 ? void 0 : _11.map(item => {\n        var _a;\n\n        return (_a = item === null || item === void 0 ? void 0 : item.childType) === null || _a === void 0 ? void 0 : _a.displayName;\n      })) === null || _12 === void 0 ? void 0 : _12.join(', ')) || ''\n    }, {\n      var: ['#noOfBhk#', '#BHKs#'],\n      text: ((_13 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _13 === void 0 ? void 0 : _13.bhKs) ? (_16 = (_15 = [...((_14 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _14 === void 0 ? void 0 : _14.bhKs)]) === null || _15 === void 0 ? void 0 : _15.map(bhk => getBHKDisplayString(bhk))) === null || _16 === void 0 ? void 0 : _16.join(', ') : ''\n    },\n    /* BR field commented out\r\n    {\r\n      var: '#BR#',\r\n      text: dataItem?.enquiry?.bhKs\r\n        ? [...dataItem?.enquiry?.bhKs]\r\n          ?.map((br: any) => getBRDisplayString(br))\r\n          ?.join(', ')\r\n        : '',\r\n    },\r\n    */\n    {\n      var: ['#bHKType#', '#BHKTypes#'],\n      text: ((_17 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _17 === void 0 ? void 0 : _17.bhkTypes) ? (_20 = (_19 = [...((_18 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _18 === void 0 ? void 0 : _18.bhkTypes)]) === null || _19 === void 0 ? void 0 : _19.map(type => BHKType[type])) === null || _20 === void 0 ? void 0 : _20.join(', ') : ''\n    }, {\n      var: '#Beds#',\n      text: ((_22 = (_21 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _21 === void 0 ? void 0 : _21.beds) === null || _22 === void 0 ? void 0 : _22.length) ? (_25 = (_24 = [...((_23 = dataItem.enquiry) === null || _23 === void 0 ? void 0 : _23.beds)]) === null || _24 === void 0 ? void 0 : _24.map(bed => bed === 0 || bed === '0' ? 'Studio' : bed)) === null || _25 === void 0 ? void 0 : _25.join(', ') : ''\n    }, {\n      var: '#Baths#',\n      text: ((_26 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _26 === void 0 ? void 0 : _26.baths) ? (_29 = (_28 = [...((_27 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _27 === void 0 ? void 0 : _27.baths)]) === null || _28 === void 0 ? void 0 : _28.map(bath => bath)) === null || _29 === void 0 ? void 0 : _29.join(', ') : ''\n    }, {\n      var: '#Furnish Status#',\n      text: FurnishStatus[(_30 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _30 === void 0 ? void 0 : _30.furnished] || ''\n    }, {\n      var: '#Preferred Floor#',\n      text: ((_31 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _31 === void 0 ? void 0 : _31.floors) ? (_34 = (_33 = [...((_32 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _32 === void 0 ? void 0 : _32.floors)]) === null || _33 === void 0 ? void 0 : _33.map(floor => floor)) === null || _34 === void 0 ? void 0 : _34.join(', ') : ''\n    }, {\n      var: '#Offering Type#',\n      text: OfferType[(_35 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _35 === void 0 ? void 0 : _35.offerType] || ''\n    }, {\n      var: '#Carpet Area#',\n      text: ((_37 = (_36 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _36 === void 0 ? void 0 : _36.carpetArea) !== null && _37 !== void 0 ? _37 : '') + ' ' + ((_39 = (_38 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _38 === void 0 ? void 0 : _38.carpetAreaUnit) !== null && _39 !== void 0 ? _39 : '')\n    }, {\n      var: '#Saleable Area#',\n      text: ((_41 = (_40 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _40 === void 0 ? void 0 : _40.saleableArea) !== null && _41 !== void 0 ? _41 : '') + ' ' + ((_43 = (_42 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _42 === void 0 ? void 0 : _42.saleableAreaUnit) !== null && _43 !== void 0 ? _43 : '')\n    }, {\n      var: '#BuiltUp Area#',\n      text: ((_45 = (_44 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _44 === void 0 ? void 0 : _44.builtUpArea) !== null && _45 !== void 0 ? _45 : '') + ' ' + ((_47 = (_46 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _46 === void 0 ? void 0 : _46.builtUpAreaUnit) !== null && _47 !== void 0 ? _47 : '')\n    }, {\n      var: '#Property Area#',\n      text: ((_49 = (_48 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _48 === void 0 ? void 0 : _48.propertyArea) !== null && _49 !== void 0 ? _49 : '') + ' ' + ((_51 = (_50 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _50 === void 0 ? void 0 : _50.propertyAreaUnit) !== null && _51 !== void 0 ? _51 : '')\n    }, {\n      var: '#Net Area#',\n      text: ((_53 = (_52 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _52 === void 0 ? void 0 : _52.netArea) !== null && _53 !== void 0 ? _53 : '') + ' ' + ((_55 = (_54 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _54 === void 0 ? void 0 : _54.netAreaUnit) !== null && _55 !== void 0 ? _55 : '')\n    }, {\n      var: '#Unit Number or Name#',\n      text: ((_56 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _56 === void 0 ? void 0 : _56.unitName) || ''\n    }, {\n      var: '#Cluster Name#',\n      text: ((_57 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _57 === void 0 ? void 0 : _57.clusterName) || ''\n    }, {\n      var: '#Purpose#',\n      text: PurposeType[(_58 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _58 === void 0 ? void 0 : _58.purpose] ? PurposeType[(_59 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _59 === void 0 ? void 0 : _59.purpose] : ''\n    }, {\n      var: '#Nationality#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.nationality) || ''\n    }, {\n      var: '#Possession Date#',\n      text: (() => {\n        var _a, _b, _c;\n\n        const possessionType = (module === null || module === void 0 ? void 0 : module.includes('data')) ? (_a = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _a === void 0 ? void 0 : _a.possesionType : dataItem === null || dataItem === void 0 ? void 0 : dataItem.possesionType;\n        const possessionDate = (module === null || module === void 0 ? void 0 : module.includes('data')) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.possesionDate : (_b = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _b === void 0 ? void 0 : _b.possessionDate;\n\n        if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\n          if (possessionType === PossessionType['Custom Date'] && possessionDate) {\n            return getTimeZoneDate(possessionDate, (_c = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _c === void 0 ? void 0 : _c.baseUTcOffset, 'dayMonthYear');\n          }\n\n          return PossessionType[possessionType];\n        }\n\n        return '';\n      })()\n    }, {\n      var: '#Currency#',\n      text: ((_60 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiry) === null || _60 === void 0 ? void 0 : _60.currency) || ''\n    }, {\n      var: '#Agencies#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.agencies) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.agencies.map(item => item.name).join(', ') : ''\n    }, {\n      var: '#Campaigns#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.campaigns) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.campaigns.map(item => item.name).join(', ') : ''\n    }, {\n      var: '#Custom Lead Status#',\n      text: (_65 = (_63 = (_62 = (_61 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.status) === null || _61 === void 0 ? void 0 : _61.childType) === null || _62 === void 0 ? void 0 : _62.displayName) !== null && _63 !== void 0 ? _63 : (_64 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.status) === null || _64 === void 0 ? void 0 : _64.displayName) !== null && _65 !== void 0 ? _65 : ''\n    }, {\n      var: '#Gender#',\n      text: Gender[dataItem === null || dataItem === void 0 ? void 0 : dataItem.gender] || ''\n    }, {\n      var: '#Marital Status#',\n      text: MaritalStatusType[dataItem === null || dataItem === void 0 ? void 0 : dataItem.maritalStatus] || ''\n    }, {\n      var: '#Date of Birth#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.dateOfBirth) ? getTimeZoneDate(dataItem === null || dataItem === void 0 ? void 0 : dataItem.dateOfBirth, '00:00:00', 'dayMonthYear') : ''\n    }];\n    return processReplacements(msg, replacements);\n  };\n\n  if (Array.isArray(leadData)) {\n    leadData === null || leadData === void 0 ? void 0 : leadData.forEach((dataItem, index) => {\n      const msg = processLeadData(dataItem, index);\n      combinedMsg += index + 1 + '. ' + msg;\n\n      if (index < leadData.length - 1) {\n        combinedMsg += '\\n';\n      }\n    });\n  } else {\n    combinedMsg = processLeadData(leadData, 0);\n  }\n\n  if (header) {\n    header = processReplacements(header, replacements);\n  }\n\n  if (footer) {\n    footer = processReplacements(footer, replacements);\n  }\n\n  return combinedMsg ? (header ? (header === null || header === void 0 ? void 0 : header.replace(/\\n/g, '\\\\n')) + '\\\\n' : '') + (combinedMsg === null || combinedMsg === void 0 ? void 0 : combinedMsg.replace(/\\n/g, '\\\\n')) + (footer ? '\\\\n' + (footer === null || footer === void 0 ? void 0 : footer.replace(/\\n/g, '\\\\n')) : '') : '';\n};\nexport const WhatsAppTemplateMsg = (template, leadData, tenantName, defaultCurrency, replaceData, media, allUserList, userData, currentDate) => {\n  let combinedMsg = '';\n  let replacements = [];\n\n  const processReplacements = (msg, replacements) => {\n    if (typeof msg === 'string') {\n      replacements.forEach(item => {\n        var _a, _b;\n\n        if (item.var === \"\\\"#BodyValues#\\\"\") {\n          msg = msg.replace(\"\\\"#BodyValues#\\\"\", ((_a = item === null || item === void 0 ? void 0 : item.text) === null || _a === void 0 ? void 0 : _a.map(s => `\"${s}\"`).join(',')) || '');\n        } else {\n          const regex = new RegExp(item.var, 'gi');\n          msg = msg.replaceAll(regex, (_b = item === null || item === void 0 ? void 0 : item.text) === null || _b === void 0 ? void 0 : _b.trim());\n        }\n      });\n    }\n\n    return msg;\n  };\n\n  const processLeadData = (dataItem, index) => {\n    let msg = replaceData;\n    let header, body;\n\n    if (template === null || template === void 0 ? void 0 : template.headerValues) {\n      header = Object.values(template === null || template === void 0 ? void 0 : template.headerValues);\n      header = header.join(', ');\n    }\n\n    if (template === null || template === void 0 ? void 0 : template.bodyValues) {\n      body = Object.values(template === null || template === void 0 ? void 0 : template.bodyValues).map(item => LeadTemplateMsg(item || 'default', leadData, tenantName, defaultCurrency, null, null, allUserList, userData, currentDate));\n    }\n\n    replacements = [{\n      var: '#LeadFullContactNo#',\n      text: dataItem === null || dataItem === void 0 ? void 0 : dataItem.contactNo\n    }, // {\n    //   var: '\\n',\n    //   text: '\\\\n',\n    // },\n    {\n      var: '#HeaderValues#',\n      text: LeadTemplateMsg(header || 'default', leadData, tenantName, defaultCurrency, null, null, allUserList, userData, currentDate)\n    }, {\n      var: '\\\"#BodyValues#\\\"',\n      text: body\n    }, {\n      var: '#Message#',\n      text: LeadTemplateMsg(template === null || template === void 0 ? void 0 : template.message, leadData, tenantName, defaultCurrency, template === null || template === void 0 ? void 0 : template.header, template === null || template === void 0 ? void 0 : template.footer, allUserList, userData, currentDate) || 'None'\n    }, {\n      var: '#TemplateName#',\n      text: (template === null || template === void 0 ? void 0 : template.title) || null\n    }, {\n      var: '#TemplateId#',\n      text: template === null || template === void 0 ? void 0 : template.id\n    }, {\n      var: '#FileName#',\n      text: (media === null || media === void 0 ? void 0 : media.fileName) || 'default'\n    }, {\n      var: '#MediaUrl#',\n      text: (media === null || media === void 0 ? void 0 : media.mediaUrl) || 'default'\n    }, {\n      var: '#MessageType#',\n      text: (media === null || media === void 0 ? void 0 : media.mediaType) || ''\n    }, {\n      var: '#leadName#',\n      text: leadData === null || leadData === void 0 ? void 0 : leadData.name\n    }];\n    return processReplacements(msg, replacements);\n  };\n\n  if (Array.isArray(leadData)) {\n    leadData === null || leadData === void 0 ? void 0 : leadData.forEach((dataItem, index) => {\n      const msg = processLeadData(dataItem, index);\n      combinedMsg += index + 1 + '. ' + msg;\n\n      if (index < leadData.length - 1) {\n        combinedMsg += '\\n';\n      }\n    });\n  } else {\n    combinedMsg = processLeadData(leadData, 0);\n  }\n\n  return combinedMsg ? combinedMsg : '';\n};\nexport const PropertyTemplateMsg = (template, propertyData, areaSizeUnits, tenantName, header, footer, canViewOwnerDetails, defaultCurrency, userData, currentDate) => {\n  let combinedMsg = '';\n  const transformedAttributes = {};\n  let replacements = [];\n  template = template === null || template === void 0 ? void 0 : template.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  header = header === null || header === void 0 ? void 0 : header.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  footer = footer === null || footer === void 0 ? void 0 : footer.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n\n  const normalizeVar = variable => {\n    var _a;\n\n    return (_a = variable.toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '');\n  };\n\n  const processReplacements = (msg, replacements) => {\n    if (msg) {\n      replacements.forEach(item => {\n        var _a;\n\n        if (Array.isArray(item.var)) {\n          item.var.forEach(variable => {\n            var _a;\n\n            const normalizedVar = normalizeVar(variable);\n            msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, (_a = item === null || item === void 0 ? void 0 : item.text) === null || _a === void 0 ? void 0 : _a.trim());\n          });\n        } else {\n          const normalizedVar = normalizeVar(item.var);\n          msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, (_a = item === null || item === void 0 ? void 0 : item.text) === null || _a === void 0 ? void 0 : _a.trim());\n        }\n      });\n    }\n\n    return msg;\n  };\n\n  const processPropertyData = (dataItem, index) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17, _18, _19, _20, _21, _22, _23, _24, _25, _26, _27, _28, _29, _30;\n\n    let msg = template;\n\n    if ((_a = dataItem.attributes) === null || _a === void 0 ? void 0 : _a.length) {\n      for (const attribute of dataItem.attributes) {\n        if (!(attribute === null || attribute === void 0 ? void 0 : attribute.attributeName.startsWith('Is'))) {\n          transformedAttributes[attribute === null || attribute === void 0 ? void 0 : attribute.attributeName] = attribute.value;\n        }\n      }\n    }\n\n    let propertyLinks = '';\n    (_b = dataItem === null || dataItem === void 0 ? void 0 : dataItem.links) === null || _b === void 0 ? void 0 : _b.map(link => {\n      propertyLinks += link + ', ';\n    });\n    propertyLinks = propertyLinks.slice(0, -2);\n    replacements = [{\n      var: '#PropertyStatus#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.status) == 1 ? 'Sold' : PropertyStatus[dataItem === null || dataItem === void 0 ? void 0 : dataItem.status]\n    }, {\n      var: '#EnquiredFor#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiredFor) ? EnquiryType[dataItem === null || dataItem === void 0 ? void 0 : dataItem.enquiredFor] : ''\n    }, {\n      var: '#PropertyType#',\n      text: ((_c = dataItem.propertyType) === null || _c === void 0 ? void 0 : _c.displayName) ? (_d = dataItem.propertyType) === null || _d === void 0 ? void 0 : _d.displayName : ''\n    }, {\n      var: '#PropertySubType#',\n      text: ((_f = (_e = dataItem.propertyType) === null || _e === void 0 ? void 0 : _e.childType) === null || _f === void 0 ? void 0 : _f.displayName) ? (_h = (_g = dataItem.propertyType) === null || _g === void 0 ? void 0 : _g.childType) === null || _h === void 0 ? void 0 : _h.displayName : ''\n    }, {\n      var: '#NoOfBHK#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.noOfBHK) ? getBHKDisplayString(dataItem === null || dataItem === void 0 ? void 0 : dataItem.noOfBHK) : ''\n    }, {\n      var: '#NoOfBR#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.noOfBHK) ? getBRDisplayString(dataItem === null || dataItem === void 0 ? void 0 : dataItem.noOfBHK) : ''\n    }, {\n      var: '#BHKType#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.bhkType) ? BHKType[dataItem === null || dataItem === void 0 ? void 0 : dataItem.bhkType] : ''\n    }, {\n      var: '#BRType#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.bhkType) ? BHKType[dataItem === null || dataItem === void 0 ? void 0 : dataItem.bhkType] : ''\n    }, {\n      var: '#Title#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.title) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.title : ''\n    }, {\n      var: ['#PropertyArea#', '#PropertySize#'],\n      text: `${((_j = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _j === void 0 ? void 0 : _j.area) ? (_k = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _k === void 0 ? void 0 : _k.area : ''} ${((_l = dataItem.dimension) === null || _l === void 0 ? void 0 : _l.areaUnitId) ? (_o = getAreaUnit((_m = dataItem.dimension) === null || _m === void 0 ? void 0 : _m.areaUnitId, areaSizeUnits)) === null || _o === void 0 ? void 0 : _o.unit : ''}`\n    }, {\n      var: '#CarpetArea#',\n      text: `${((_p = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _p === void 0 ? void 0 : _p.carpetArea) ? (_q = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _q === void 0 ? void 0 : _q.carpetArea : ''} ${((_r = dataItem.dimension) === null || _r === void 0 ? void 0 : _r.carpetAreaId) ? (_t = getAreaUnit((_s = dataItem.dimension) === null || _s === void 0 ? void 0 : _s.carpetAreaId, areaSizeUnits)) === null || _t === void 0 ? void 0 : _t.unit : ''}`\n    }, {\n      var: '#BuiltUpArea#',\n      text: `${((_u = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _u === void 0 ? void 0 : _u.buildUpArea) ? (_v = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _v === void 0 ? void 0 : _v.buildUpArea : ''} ${((_w = dataItem.dimension) === null || _w === void 0 ? void 0 : _w.buildUpAreaId) ? (_y = getAreaUnit((_x = dataItem.dimension) === null || _x === void 0 ? void 0 : _x.buildUpAreaId, areaSizeUnits)) === null || _y === void 0 ? void 0 : _y.unit : ''}`\n    }, {\n      var: '#SaleableArea#',\n      text: `${((_z = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _z === void 0 ? void 0 : _z.saleableArea) ? (_0 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _0 === void 0 ? void 0 : _0.saleableArea : ''} ${((_1 = dataItem.dimension) === null || _1 === void 0 ? void 0 : _1.saleableAreaId) ? (_3 = getAreaUnit((_2 = dataItem.dimension) === null || _2 === void 0 ? void 0 : _2.saleableAreaId, areaSizeUnits)) === null || _3 === void 0 ? void 0 : _3.unit : ''}`\n    }, {\n      var: '#NetArea#',\n      text: `${((_4 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _4 === void 0 ? void 0 : _4.netArea) ? (_5 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _5 === void 0 ? void 0 : _5.netArea : ''} ${((_6 = dataItem.dimension) === null || _6 === void 0 ? void 0 : _6.netAreaUnitId) ? (_8 = getAreaUnit((_7 = dataItem.dimension) === null || _7 === void 0 ? void 0 : _7.netAreaUnitId, areaSizeUnits)) === null || _8 === void 0 ? void 0 : _8.unit : ''}`\n    }, {\n      var: '#Dimension#',\n      text: `${((_9 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _9 === void 0 ? void 0 : _9.length) ? 'L-' + ((_10 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _10 === void 0 ? void 0 : _10.length) : ''} ${((_11 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _11 === void 0 ? void 0 : _11.breadth) && ((_12 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _12 === void 0 ? void 0 : _12.length) ? 'X' : ''} ${((_13 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _13 === void 0 ? void 0 : _13.breadth) ? 'B-' + ((_14 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.dimension) === null || _14 === void 0 ? void 0 : _14.breadth) : ''}`\n    }, {\n      var: '#SaleType#',\n      text: dataItem.saleType ? SaleType[dataItem.saleType] : ''\n    }, {\n      var: '#PossessionDate#',\n      text: (() => {\n        var _a, _b, _c;\n\n        const possessionType = dataItem === null || dataItem === void 0 ? void 0 : dataItem.possesionType;\n        const possessionDate = dataItem === null || dataItem === void 0 ? void 0 : dataItem.possessionDate;\n\n        if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\n          if (possessionType === PossessionType['Custom Date'] && possessionDate) {\n            const isReadyToMove = getTimeZoneDate(possessionDate, (_a = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _a === void 0 ? void 0 : _a.baseUTcOffset, 'dayMonthYear') <= getTimeZoneDate(currentDate, (_b = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _b === void 0 ? void 0 : _b.baseUTcOffset, 'dayMonthYear');\n            return isReadyToMove ? 'Ready To Move' : getTimeZoneDate(possessionDate, (_c = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _c === void 0 ? void 0 : _c.baseUTcOffset, 'dayMonthYear');\n          }\n\n          return PossessionType[possessionType];\n        }\n\n        return '';\n      })()\n    }, {\n      var: '#TotalPrice#',\n      text: ((_15 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _15 === void 0 ? void 0 : _15.expectedPrice) ? ((_16 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _16 === void 0 ? void 0 : _16.expectedPrice) + ' (' + formatBudget((_17 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _17 === void 0 ? void 0 : _17.expectedPrice, ((_18 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _18 === void 0 ? void 0 : _18.currency) || defaultCurrency) + ')' : ''\n    }, {\n      var: '#Brokerage#',\n      text: `${((_19 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _19 === void 0 ? void 0 : _19.brokerage) ? (_20 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _20 === void 0 ? void 0 : _20.brokerage : ''} ${((_21 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _21 === void 0 ? void 0 : _21.brokerageCurrency) ? (_22 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _22 === void 0 ? void 0 : _22.brokerageCurrency : ''}`\n    }, {\n      var: '#Notes#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.notes) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.notes : ''\n    }, {\n      var: '#AboutProperty#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.aboutProperty) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.aboutProperty : ''\n    }, {\n      var: '#Address#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.address) ? getLocationDetailsByObj(dataItem === null || dataItem === void 0 ? void 0 : dataItem.address) : ''\n    }, {\n      var: '#OwnerName#',\n      text: ((_23 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _23 === void 0 ? void 0 : _23.length) && canViewOwnerDetails ? (_25 = (_24 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _24 === void 0 ? void 0 : _24.filter(owner => {\n        var _a;\n\n        return (_a = owner.name) === null || _a === void 0 ? void 0 : _a.trim();\n      })) === null || _25 === void 0 ? void 0 : _25.map(owner => owner.name).join(', ') : ''\n    }, {\n      var: '#OwnerPhone#',\n      text: ((_26 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _26 === void 0 ? void 0 : _26.length) && canViewOwnerDetails ? (_27 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _27 === void 0 ? void 0 : _27.filter(owner => {\n        var _a;\n\n        return (_a = owner.phone) === null || _a === void 0 ? void 0 : _a.trim();\n      }).map(owner => owner.phone).join(', ') : ''\n    }, {\n      var: '#OwnerEmail#',\n      text: ((_28 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _28 === void 0 ? void 0 : _28.length) && canViewOwnerDetails ? (_30 = (_29 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.propertyOwnerDetails) === null || _29 === void 0 ? void 0 : _29.filter(owner => {\n        var _a;\n\n        return (_a = owner.email) === null || _a === void 0 ? void 0 : _a.trim();\n      })) === null || _30 === void 0 ? void 0 : _30.map(owner => owner.email).join(', ') : ''\n    }, {\n      var: '#TotalFloors#',\n      text: transformedAttributes['numberOfFloors'] ? transformedAttributes['numberOfFloors'] : ''\n    }, {\n      var: '#FloorNumber#',\n      text: transformedAttributes['floorNumber'] ? transformedAttributes['floorNumber'] : ''\n    }, {\n      var: '#NoOfBathRooms#',\n      text: transformedAttributes['numberOfBathrooms'] ? transformedAttributes['numberOfBathrooms'] : ''\n    }, {\n      var: '#NoOfBedRooms#',\n      text: transformedAttributes['numberOfBedrooms'] ? transformedAttributes['numberOfBedrooms'] : ''\n    }, {\n      var: '#NoOfKitchens#',\n      text: transformedAttributes['numberOfKitchens'] ? transformedAttributes['numberOfKitchens'] : ''\n    }, {\n      var: '#NoOfLivingRooms#',\n      text: transformedAttributes['numberOfLivingRooms'] ? transformedAttributes['numberOfLivingRooms'] : ''\n    }, {\n      var: '#NoOfUtilities#',\n      text: transformedAttributes['numberOfUtilities'] ? transformedAttributes['numberOfUtilities'] : ''\n    }, {\n      var: '#NoOfBalconies#',\n      text: transformedAttributes['numberOfBalconies'] ? transformedAttributes['numberOfBalconies'] : ''\n    }, {\n      var: '#NoofParking#',\n      text: transformedAttributes['numberOfParking'] ? transformedAttributes['numberOfParking'] : ''\n    }, {\n      var: '#FurnishStatus#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.furnishStatus) ? FurnishStatus[dataItem === null || dataItem === void 0 ? void 0 : dataItem.furnishStatus] : ''\n    }, {\n      var: '#Facing#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.facing) ? Facing[dataItem === null || dataItem === void 0 ? void 0 : dataItem.facing] : ''\n    }, {\n      var: '#MicrositeUrl#',\n      text: getMSUrl(dataItem === null || dataItem === void 0 ? void 0 : dataItem.serialNo, false, true)\n    }, {\n      var: '#Property URL#',\n      text: propertyLinks\n    }, {\n      var: '#userName#',\n      text: `${userData === null || userData === void 0 ? void 0 : userData.firstName} ${userData === null || userData === void 0 ? void 0 : userData.lastName}`\n    }, {\n      var: '#userEmail#',\n      text: userData === null || userData === void 0 ? void 0 : userData.email\n    }, {\n      var: '#userMobile#',\n      text: userData === null || userData === void 0 ? void 0 : userData.phoneNumber\n    }, {\n      var: '#tenantName#',\n      text: tenantName || ''\n    }, {\n      var: '#LeadName#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.leadName) || ''\n    }];\n    return processReplacements(msg, replacements);\n  };\n\n  if (Array.isArray(propertyData)) {\n    propertyData === null || propertyData === void 0 ? void 0 : propertyData.forEach((dataItem, index) => {\n      const msg = processPropertyData(dataItem, index);\n      combinedMsg += index + 1 + '. ' + msg;\n\n      if (index < propertyData.length - 1) {\n        combinedMsg += '\\n';\n      }\n    });\n  } else {\n    combinedMsg = processPropertyData(propertyData, 0);\n  }\n\n  if (header) {\n    header = processReplacements(header, replacements);\n  }\n\n  if (footer) {\n    footer = processReplacements(footer, replacements);\n  }\n\n  return combinedMsg ? (header ? header + '\\n' : '') + combinedMsg + (footer ? '\\n' + footer : '') : '';\n};\nexport const ProjectTemplateMsg = (template, projectData, areaSizeUnits, tenantName, header, footer, unitInfo, key, userData, currentDate) => {\n  let combinedMsg = '';\n  const transformedAttributes = {};\n  let replacements = [];\n  template = template === null || template === void 0 ? void 0 : template.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  header = header === null || header === void 0 ? void 0 : header.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n  footer = footer === null || footer === void 0 ? void 0 : footer.replace(/#([\\w\\s-]+)#/g, match => {\n    var _a;\n\n    return '#' + ((_a = match.slice(1, -1).toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '')) + '#';\n  });\n\n  const normalizeVar = variable => {\n    var _a;\n\n    return (_a = variable.toLowerCase()) === null || _a === void 0 ? void 0 : _a.replace(/[\\s-]+/g, '');\n  };\n\n  const processReplacements = (msg, replacements) => {\n    if (msg) {\n      replacements.forEach(item => {\n        if (Array.isArray(item.var)) {\n          item.var.forEach(variable => {\n            const normalizedVar = normalizeVar(variable);\n            const replacementText = typeof (item === null || item === void 0 ? void 0 : item.text) === 'string' ? item.text.trim() : item.text;\n            msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, replacementText);\n          });\n        } else {\n          const normalizedVar = normalizeVar(item.var);\n          const replacementText = typeof (item === null || item === void 0 ? void 0 : item.text) === 'string' ? item.text.trim() : item.text;\n          msg = msg === null || msg === void 0 ? void 0 : msg.replaceAll(normalizedVar, replacementText);\n        }\n      });\n    }\n\n    return msg;\n  };\n\n  const processProjectData = (dataItem, index) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13;\n\n    let msg = template;\n\n    if ((_a = dataItem.attributes) === null || _a === void 0 ? void 0 : _a.length) {\n      for (const attribute of dataItem.attributes) {\n        if (!(attribute === null || attribute === void 0 ? void 0 : attribute.attributeName.startsWith('Is'))) {\n          transformedAttributes[attribute === null || attribute === void 0 ? void 0 : attribute.attributeName] = attribute.value;\n        }\n      }\n    }\n\n    function getValueBasedOnKey(property) {\n      var _a, _b, _c;\n\n      if (key === 'share-project' || key === 'share-matching-lead') {\n        return ((_a = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _a === void 0 ? void 0 : _a[property]) ? (_b = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _b === void 0 ? void 0 : _b[property] : '';\n      } else {\n        return ((_c = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _c === void 0 ? void 0 : _c[property]) || (dataItem === null || dataItem === void 0 ? void 0 : dataItem[property]) || '';\n      }\n    }\n\n    let projectLinks = '';\n    (_b = dataItem === null || dataItem === void 0 ? void 0 : dataItem.links) === null || _b === void 0 ? void 0 : _b.map(link => {\n      projectLinks += link + ', ';\n    });\n    projectLinks = projectLinks.slice(0, -2);\n    replacements = [{\n      var: '#Project Status#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.currentStatus) == 1 ? 'Sold' : PropertyStatus[dataItem === null || dataItem === void 0 ? void 0 : dataItem.status]\n    }, {\n      var: '#ProjectType#',\n      text: ((_c = dataItem.projectType) === null || _c === void 0 ? void 0 : _c.displayName) ? (_d = dataItem.projectType) === null || _d === void 0 ? void 0 : _d.displayName : ''\n    }, {\n      var: '#ProjectSubType#',\n      text: ((_f = (_e = dataItem.projectType) === null || _e === void 0 ? void 0 : _e.childType) === null || _f === void 0 ? void 0 : _f.displayName) ? (_h = (_g = dataItem.projectType) === null || _g === void 0 ? void 0 : _g.childType) === null || _h === void 0 ? void 0 : _h.displayName : ''\n    }, {\n      var: '#Name#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.name) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.name : ''\n    }, {\n      var: '#PossessionDate#',\n      text: (() => {\n        var _a;\n\n        const possessionType = dataItem === null || dataItem === void 0 ? void 0 : dataItem.possesionType;\n        const possessionDate = dataItem === null || dataItem === void 0 ? void 0 : dataItem.possessionDate;\n\n        if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\n          if (possessionType === PossessionType['Custom Date'] && possessionDate) {\n            const possessionDateObj = new Date(possessionDate);\n            const currentDateObj = new Date(currentDate);\n            const isPast = possessionDateObj <= currentDateObj;\n            return isPast ? 'Ready To Move' : getTimeZoneDate(possessionDate, (_a = userData === null || userData === void 0 ? void 0 : userData.timeZoneInfo) === null || _a === void 0 ? void 0 : _a.baseUTcOffset, 'dayMonthYear');\n          }\n\n          return PossessionType[possessionType];\n        }\n\n        return '';\n      })()\n    }, // {\n    //   var: '#TotalPrice#',\n    //   text: dataItem?.monetaryInfo?.expectedPrice\n    //     ? dataItem?.monetaryInfo?.expectedPrice +\n    //     ' (' +\n    //     formatBudget(\n    //       dataItem?.monetaryInfo?.expectedPrice,\n    //       dataItem?.monetaryInfo?.currency\n    //     ) +\n    //     ')'\n    //     : '',\n    // },\n    {\n      var: '#Brokerage#',\n      text: `${((_j = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _j === void 0 ? void 0 : _j.brokerage) ? (_k = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _k === void 0 ? void 0 : _k.brokerage : ''} ${((_l = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _l === void 0 ? void 0 : _l.brokerageCurrency) ? (_m = dataItem === null || dataItem === void 0 ? void 0 : dataItem.monetaryInfo) === null || _m === void 0 ? void 0 : _m.brokerageCurrency : ''}`\n    }, {\n      var: '#LandArea#',\n      text: `${(dataItem === null || dataItem === void 0 ? void 0 : dataItem.area) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.area : ''} ${dataItem.areaUnitId ? (_o = getAreaUnit(dataItem.areaUnitId, areaSizeUnits)) === null || _o === void 0 ? void 0 : _o.unit : ''}`\n    }, {\n      var: '#Address#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.address) ? getLocationDetailsByObj(dataItem === null || dataItem === void 0 ? void 0 : dataItem.address) : ''\n    }, {\n      var: '#BuilderName#',\n      text: ((_p = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _p === void 0 ? void 0 : _p.name) ? (_q = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _q === void 0 ? void 0 : _q.name : ''\n    }, {\n      var: '#Notes#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.notes) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.notes : ''\n    }, {\n      var: '#BuilderPhone#',\n      text: ((_r = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _r === void 0 ? void 0 : _r.contactNo) ? (_s = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _s === void 0 ? void 0 : _s.contactNo : ''\n    }, {\n      var: '#BuilderPointOfContact#',\n      text: ((_t = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _t === void 0 ? void 0 : _t.pointOfContact) ? (_u = dataItem === null || dataItem === void 0 ? void 0 : dataItem.builderDetail) === null || _u === void 0 ? void 0 : _u.pointOfContact : ''\n    }, {\n      var: '#TotalFloors#',\n      text: transformedAttributes['numberOfFloors'] ? transformedAttributes['numberOfFloors'] : ''\n    }, {\n      var: '#Facing#',\n      text: ((_v = dataItem === null || dataItem === void 0 ? void 0 : dataItem.facings) === null || _v === void 0 ? void 0 : _v.length) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.facings.map(index => Facing[index]).join(', ') : ''\n    }, {\n      var: '#UnitName#',\n      text: getValueBasedOnKey('name')\n    }, {\n      var: '#UnitArea#',\n      text: `${(dataItem === null || dataItem === void 0 ? void 0 : dataItem.area) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.area : ''} ${dataItem.areaUnitId ? (_w = getAreaUnit(dataItem.areaUnitId, areaSizeUnits)) === null || _w === void 0 ? void 0 : _w.unit : ''}`\n    }, {\n      var: '#CarpetArea#',\n      text: `${(dataItem === null || dataItem === void 0 ? void 0 : dataItem.carpetArea) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.carpetArea : ''} ${dataItem.carpetAreaUnitId ? (_x = getAreaUnit(dataItem.carpetAreaUnitId, areaSizeUnits)) === null || _x === void 0 ? void 0 : _x.unit : ''}`\n    }, {\n      var: '#BuiltupArea#',\n      text: `${(dataItem === null || dataItem === void 0 ? void 0 : dataItem.buildUpArea) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.buildUpArea : ''} ${dataItem.buildUpAreaId ? (_y = getAreaUnit(dataItem.buildUpAreaId, areaSizeUnits)) === null || _y === void 0 ? void 0 : _y.unit : ''}`\n    }, {\n      var: '#SuperBuiltupArea#',\n      text: `${(dataItem === null || dataItem === void 0 ? void 0 : dataItem.superBuildUpArea) ? dataItem === null || dataItem === void 0 ? void 0 : dataItem.superBuildUpArea : ''} ${dataItem.superBuildUpAreaUnit ? (_z = getAreaUnit(dataItem.superBuildUpAreaUnit, areaSizeUnits)) === null || _z === void 0 ? void 0 : _z.unit : ''}`\n    }, {\n      var: '#MaintenanceCost#',\n      text: getValueBasedOnKey('maintenanceCost') + '(' + formatBudget(getValueBasedOnKey('maintenanceCost'), getValueBasedOnKey('currency')) + ')'\n    }, {\n      var: '#PricePerUnit#',\n      text: getValueBasedOnKey('pricePerUnit') + '(' + formatBudget(getValueBasedOnKey('pricePerUnit'), getValueBasedOnKey('currency')) + ')'\n    }, {\n      var: '#TotalPrice#',\n      text: getValueBasedOnKey('price') + '(' + formatBudget(getValueBasedOnKey('price'), getValueBasedOnKey('currency')) + ')'\n    }, {\n      var: '#UnitType#',\n      text: getValueBasedOnKey('unitType?.displayName')\n    }, {\n      var: '#UnitSubType#',\n      text: getValueBasedOnKey('unitType?.childType?.displayName')\n    }, {\n      var: '#Bhk#',\n      text: key !== 'share-project' ? ((_0 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _0 === void 0 ? void 0 : _0.noOfBHK) ? getBHKDisplayString(dataItem.unitInfo.noOfBHK) : getBHKDisplayString(dataItem === null || dataItem === void 0 ? void 0 : dataItem.noOfBHK) : ((_2 = (_1 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _1 === void 0 ? void 0 : _1[0]) === null || _2 === void 0 ? void 0 : _2.noOfBHK) ? getBHKDisplayString(dataItem.unitInfo[0].noOfBHK) : ''\n    }, {\n      var: '#BhkType#',\n      text: key === 'share-project' ? ((_3 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _3 === void 0 ? void 0 : _3.bhkType) ? BHKType[(_4 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _4 === void 0 ? void 0 : _4.bhkType] : BHKType[dataItem === null || dataItem === void 0 ? void 0 : dataItem.bhkType] : ((_5 = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _5 === void 0 ? void 0 : _5.bhkType) ? BHKType[(_6 = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _6 === void 0 ? void 0 : _6.bhkType] : ''\n    }, {\n      var: '#Facing#',\n      text: key !== 'share-project' ? ((_8 = (_7 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _7 === void 0 ? void 0 : _7.facings) === null || _8 === void 0 ? void 0 : _8.length) ? dataItem.unitInfo.facings.map(index => Facing[index]).join(', ') : '' : ((_10 = (_9 = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _9 === void 0 ? void 0 : _9.facings) === null || _10 === void 0 ? void 0 : _10.length) ? (_11 = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _11 === void 0 ? void 0 : _11.facings.map(index => Facing[index]).join(', ') : ''\n    }, {\n      var: '#FurnishingStatus#',\n      text: key !== 'share-project' ? ((_12 = dataItem === null || dataItem === void 0 ? void 0 : dataItem.unitInfo) === null || _12 === void 0 ? void 0 : _12.furnishingStatus) !== undefined ? FurnishStatus[dataItem.unitInfo.furnishingStatus] : FurnishStatus[dataItem === null || dataItem === void 0 ? void 0 : dataItem.furnishingStatus] : ((_13 = unitInfo === null || unitInfo === void 0 ? void 0 : unitInfo[0]) === null || _13 === void 0 ? void 0 : _13.furnishingStatus) !== undefined ? FurnishStatus[unitInfo[0].furnishingStatus] : ''\n    }, {\n      var: '#MicrositeUrl#',\n      text: getMSUrl(dataItem === null || dataItem === void 0 ? void 0 : dataItem.serialNo, true)\n    }, {\n      var: '#Project URL#',\n      text: projectLinks\n    }, {\n      var: '#userName#',\n      text: `${userData === null || userData === void 0 ? void 0 : userData.firstName} ${userData === null || userData === void 0 ? void 0 : userData.lastName}`\n    }, {\n      var: '#userEmail#',\n      text: userData === null || userData === void 0 ? void 0 : userData.email\n    }, {\n      var: '#userMobile#',\n      text: userData === null || userData === void 0 ? void 0 : userData.phoneNumber\n    }, {\n      var: '#tenantName#',\n      text: tenantName || ''\n    }, {\n      var: '#LeadName#',\n      text: (dataItem === null || dataItem === void 0 ? void 0 : dataItem.leadName) || ''\n    }];\n    return processReplacements(msg, replacements);\n  };\n\n  if (Array.isArray(projectData)) {\n    projectData === null || projectData === void 0 ? void 0 : projectData.forEach((dataItem, index) => {\n      const msg = processProjectData(dataItem, index);\n      combinedMsg += index + 1 + '. ' + msg;\n\n      if (index < projectData.length - 1) {\n        combinedMsg += '\\n';\n      }\n    });\n  } else {\n    combinedMsg = processProjectData(projectData, 0);\n  }\n\n  if (header) {\n    header = processReplacements(header, replacements);\n  }\n\n  if (footer) {\n    footer = processReplacements(footer, replacements);\n  }\n\n  return combinedMsg ? (header ? header + '\\n' : '') + combinedMsg + (footer ? '\\n' + footer : '') : '';\n};\nexport const getMSUrl = (serialNo, isProject = false, isListing = false) => {\n  var _a;\n\n  const subDomain = getTenantName();\n  const userName = (_a = JSON.parse(localStorage.getItem('userDetails'))) === null || _a === void 0 ? void 0 : _a.preferred_username;\n  let previewType = 'property-preview'; // default\n\n  if (isProject) {\n    previewType = 'project-preview';\n  } else if (isListing) {\n    previewType = 'listing-preview';\n  }\n\n  return `https://${subDomain + getEnvDetails()}/external/${previewType}/${userName}/${serialNo}`;\n};\nexport const sortAssignedUsers = (assignedUser, allActiveUsers) => {\n  const assignedUserIds = Array.isArray(assignedUser.value) ? assignedUser.value : [assignedUser.value];\n  const assignedUsers = [];\n  allActiveUsers.forEach(user => {\n    if (assignedUserIds.includes(user.id)) {\n      if (user.firstName === 'You') {\n        assignedUsers.unshift(user);\n      } else {\n        assignedUsers.push(user);\n      }\n    }\n  });\n  assignedUsers.sort((a, b) => {\n    if (a.firstName === 'You') return -1;\n    if (b.firstName === 'You') return 1;\n    const nameA = `${a.firstName} ${a.lastName}`;\n    const nameB = `${b.firstName} ${b.lastName}`;\n    return nameA.localeCompare(nameB);\n  });\n  const unassignedUsers = allActiveUsers.filter(user => !assignedUserIds.includes(user.id));\n  unassignedUsers.sort((a, b) => {\n    if (a.firstName === 'You') return -1;\n    if (b.firstName === 'You') return 1;\n    const nameA = `${a.firstName} ${a.lastName}`;\n    const nameB = `${b.firstName} ${b.lastName}`;\n    return nameA.localeCompare(nameB);\n  });\n  return assignedUsers.concat(unassignedUsers);\n};\nexport const assignToSort = (allUserList, assignedToUserId = '', fullName = false) => {\n  var _a;\n\n  let userId = (_a = JSON.parse(localStorage.getItem('userDetails'))) === null || _a === void 0 ? void 0 : _a.sub;\n  let loggedInUser,\n      assignedUser,\n      activeUsers = [],\n      inactiveUsers = [],\n      usersList = [];\n  allUserList === null || allUserList === void 0 ? void 0 : allUserList.map(user => {\n    if (user.id === userId) {\n      loggedInUser = Object.assign(Object.assign({}, user), fullName ? {} : {\n        firstName: 'You',\n        lastName: ''\n      });\n    }\n\n    if (user.id === assignedToUserId && user.id !== userId) {\n      assignedUser = user;\n    }\n\n    if (user.id !== userId && user.id !== assignedToUserId && user.isActive) activeUsers.push(user);\n    if (user.id !== userId && user.id !== assignedToUserId && !user.isActive) inactiveUsers.push(user);\n  });\n  if (assignedUser) usersList.push(assignedUser);\n  if (loggedInUser) usersList.push(loggedInUser);\n  return allUserList ? [...usersList, ...activeUsers, ...inactiveUsers] : [];\n};\nexport const getTotalCountForReports = (items, statusList) => {\n  let total = {\n    projectTitle: 'Total',\n    firstName: 'Total',\n    lastName: '',\n    userName: 'Total',\n    subSource: 'Total',\n    agencyName: 'Total',\n    source: 'Total',\n    name: 'Total',\n    allCount: 0,\n    activeCount: 0,\n    newCount: 0,\n    totalCount: 0,\n    pendingCount: 0,\n    overdueCount: 0,\n    callbackCount: 0,\n    meetingScheduledCount: 0,\n    siteVisitScheduledCount: 0,\n    meetingDoneCount: 0,\n    meetingNotDoneCount: 0,\n    siteVisitDoneCount: 0,\n    siteVisitNotDoneCount: 0,\n    bookedCount: 0,\n    notInterestedCount: 0,\n    droppedCount: 0,\n    meetingDoneUniqueCount: 0,\n    meetingNotDoneUniqueCount: 0,\n    siteVisitDoneUniqueCount: 0,\n    siteVisitNotDoneUniqueCount: 0,\n    averageWorkingHours: 0,\n    callsInitiatedCount: 0,\n    callsInitiatedLeadsCount: 0,\n    whatsAppInitiatedCount: 0,\n    whatsAppInitiatedLeadsCount: 0,\n    emailsInitiatedCount: 0,\n    emailsInitiatedLeadsCount: 0,\n    smsInitiatedCount: 0,\n    smsInitiatedLeadsCount: 0,\n    statusEditsCount: 0,\n    statusEditsLeadsCount: 0,\n    formEditsCount: 0,\n    formEditsLeadsCount: 0,\n    notesAddedCount: 0,\n    notesAddedLeadsCount: 0,\n    callbackScheduledLeadsCount: 0,\n    bookedLeadsCount: 0,\n    notInterestedLeadsCount: 0,\n    droppedLeadsCount: 0,\n    hotLeadsCount: 0,\n    warmLeadsCount: 0,\n    coldLeadsCount: 0,\n    escalatedLeadsCount: 0,\n    highlightedLeadsCount: 0,\n    aboutToConvertLeadsCount: 0,\n    all: 0,\n    active: 0,\n    overdue: 0,\n    callback: 0,\n    busy: 0,\n    toScheduleAMeeting: 0,\n    followUp: 0,\n    toScheduleSiteVisit: 0,\n    planPostponed: 0,\n    needMoreInfo: 0,\n    notAnswered: 0,\n    notReachable: 0,\n    dropped: 0,\n    notLooking: 0,\n    ringingNotReceived: 0,\n    wrongOrInvalidNo: 0,\n    purchasedFromOthers: 0,\n    meetingScheduled: 0,\n    onCall: 0,\n    online: 0,\n    inPerson: 0,\n    others: 0,\n    notInterested: 0,\n    differentLocation: 0,\n    differentRequirements: 0,\n    unmatchedBudget: 0,\n    siteVisitScheduled: 0,\n    firstVisit: 0,\n    reVisit: 0,\n    pending: 0,\n    booked: 0,\n    new: 0,\n    notInterestedAfterMeetingDone: 0,\n    notInterestedAfterSiteVisitDone: 0,\n    droppedAfterMeetingDone: 0,\n    droppedAfterSiteVisitDone: 0,\n    incomingAnswered: 0,\n    incomingMissed: 0,\n    totalIncomingCalls: 0,\n    outgoingAnswered: 0,\n    outgoingNotConnected: 0,\n    totalOutgoingCalls: 0,\n    totalCalls: 0,\n    totalTalkTime: 0,\n    averageTalkTime: 0,\n    maxTalkTime: 0,\n    minTalkTime: 0,\n    convertedDataCount: 0\n  };\n\n  const calculateTotalForStatus = (items, statusId, total) => {\n    items.forEach(item => {\n      const dataArray = ((item === null || item === void 0 ? void 0 : item.data) || []).filter(data => (data === null || data === void 0 ? void 0 : data.statusId) === statusId);\n      const dataCount = dataArray.reduce((sum, data) => sum + ((data === null || data === void 0 ? void 0 : data.dataCount) || 0), 0);\n      total[`${statusId}DataCount`] = (total[`${statusId}DataCount`] || 0) + dataCount;\n    });\n  };\n\n  const calculateTotalForSubSource = (items, statusId, total) => {\n    items.forEach(item => {\n      const dataArray = ((item === null || item === void 0 ? void 0 : item.subSource) || []).filter(data => `${data === null || data === void 0 ? void 0 : data.subSource}(${data === null || data === void 0 ? void 0 : data.sourceName})` === (statusId === null || statusId === void 0 ? void 0 : statusId.toString()));\n      const dataCount = dataArray === null || dataArray === void 0 ? void 0 : dataArray.reduce((sum, data) => sum + ((data === null || data === void 0 ? void 0 : data.count) || 0), 0);\n      total[`${statusId}DataCount`] = (total[`${statusId}DataCount`] || 0) + dataCount;\n    });\n  };\n\n  const calculateTotalForSource = (items, statusId, total) => {\n    items.forEach(item => {\n      const dataArray = ((item === null || item === void 0 ? void 0 : item.source) || []).filter(data => (data === null || data === void 0 ? void 0 : data.displayName) === (statusId === null || statusId === void 0 ? void 0 : statusId.toString()));\n      const dataCount = dataArray === null || dataArray === void 0 ? void 0 : dataArray.reduce((sum, data) => sum + ((data === null || data === void 0 ? void 0 : data.count) || 0), 0);\n      total[`${statusId}DataCount`] = (total[`${statusId}DataCount`] || 0) + dataCount;\n    });\n  };\n\n  statusList === null || statusList === void 0 ? void 0 : statusList.forEach(status => {\n    total[`${status}DataCount`] = 0;\n    total[`${status}DataCount`] = 0;\n    calculateTotalForSource(items, status, total);\n    calculateTotalForSubSource(items, status, total);\n  });\n  statusList === null || statusList === void 0 ? void 0 : statusList.forEach(status => {\n    calculateTotalForStatus(items, status, total);\n  });\n  items === null || items === void 0 ? void 0 : items.map(item => {\n    let objArray = Object.entries(item);\n    objArray.map(entry => {\n      if (typeof entry[1] === 'number') {\n        if (!(total === null || total === void 0 ? void 0 : total[entry[0]])) {\n          total[entry[0]] = 0;\n        }\n\n        total[entry[0]] += entry[1];\n      } else if (entry[0] === 'averageWorkingHours' || entry[0] === 'minTalkTime' || entry[0] === 'maxTalkTime' || entry[0] === 'averageTalkTime' || entry[0] === 'totalTalkTime') {\n        const workingHours = moment.duration(entry[1]);\n        total[entry[0]] += workingHours.asSeconds();\n      }\n    });\n  });\n\n  const formatDuration = seconds => {\n    var _a, _b, _c, _d, _e, _f;\n\n    const duration = moment.duration(seconds, 'seconds');\n    const hours = (_b = (_a = Math.floor(duration === null || duration === void 0 ? void 0 : duration.asHours())) === null || _a === void 0 ? void 0 : _a.toString()) === null || _b === void 0 ? void 0 : _b.padStart(2, '0');\n    const minutes = (_d = (_c = duration === null || duration === void 0 ? void 0 : duration.minutes()) === null || _c === void 0 ? void 0 : _c.toString()) === null || _d === void 0 ? void 0 : _d.padStart(2, '0');\n    const secondsStr = (_f = (_e = duration === null || duration === void 0 ? void 0 : duration.seconds()) === null || _e === void 0 ? void 0 : _e.toString()) === null || _f === void 0 ? void 0 : _f.padStart(2, '0');\n    return `${hours}:${minutes}:${secondsStr}`;\n  };\n\n  if ((items === null || items === void 0 ? void 0 : items.length) > 1) {\n    ['averageWorkingHours', 'minTalkTime', 'maxTalkTime', 'averageTalkTime', 'totalTalkTime'].forEach(field => {\n      if (total[field] !== undefined) {\n        total[field] = formatDuration(total[field]);\n      }\n    });\n    return [...items, total];\n  }\n\n  return items;\n};\nexport const getDaysInMonth = (year, month) => {\n  let date = new Date(Date.UTC(year, month, 1));\n  let monthDays = [];\n\n  while (date.getUTCMonth() === month) {\n    monthDays.push(new Date(date));\n    date.setUTCDate(date.getUTCDate() + 1);\n  }\n\n  return monthDays;\n};\nexport const containsOnlyEmojis = input => {\n  const emojiRegex = /^(?:[\\u2700-\\u27bf]|(?:\\ud83c[\\udde6-\\uddff]){2}|[\\ud800-\\udbff][\\udc00-\\udfff]|[\\u0023-\\u0039]\\ufe0f?\\u20e3|\\u3299|\\u3297|\\u303d|\\u3030|\\u24c2|\\ud83c[\\udd70-\\udd71]|\\ud83c[\\udd7e-\\udd7f]|\\ud83c\\udd8e|\\ud83c[\\udd91-\\udd9a]|\\ud83c[\\udde6-\\uddff]|[\\ud83c[\\ude01-\\ude02]|\\ud83c\\ude1a|\\ud83c\\ude2f|[\\ud83c[\\ude32-\\ude3a]|[\\ud83c[\\ude50-\\ude51]|\\u203c|\\u2049|[\\u25aa-\\u25ab]|\\u25b6|\\u25c0|[\\u25fb-\\u25fe]|\\u00a9|\\u00ae|\\u2122|\\u2139|\\ud83c\\udc04|[\\u2600-\\u26FF]|\\u2b05|\\u2b06|\\u2b07|\\u2b1b|\\u2b1c|\\u2b50|\\u2b55|\\u231a|\\u231b|\\u2328|\\u23cf|[\\u23e9-\\u23f3]|[\\u23f8-\\u23fa]|\\ud83c\\udccf|\\u2934|\\u2935|[\\u2190-\\u21ff])+$/;\n  return emojiRegex.test(input);\n};\nexport const getLocationDetailsByObj = object => {\n  const {\n    subLocality,\n    locality,\n    subCommunity,\n    community,\n    towerName,\n    city,\n    district,\n    state,\n    country,\n    postalCode\n  } = object || {};\n  const addressParts = [subLocality, locality && locality !== subLocality && locality, city && city !== subLocality && city !== locality && city, subCommunity, community, towerName, district, state, country, postalCode].filter(part => part).map(part => part.trim()).join(', ');\n\n  if (addressParts.trim().endsWith(',')) {\n    return addressParts.trim().slice(0, -1);\n  }\n\n  return addressParts || '';\n};\nexport const getLocalityDetailsByObj = obj => {\n  if (!obj) {\n    return null;\n  }\n\n  const subLocality = obj.subLocality;\n  const locality = obj.locality;\n  const city = obj.city;\n  const enquiredLocComponents = [];\n  if (subLocality !== locality) enquiredLocComponents.push(subLocality);\n  if (locality !== city) enquiredLocComponents.push(locality);\n  let enquiredLoc = enquiredLocComponents.join(', ');\n  enquiredLoc = enquiredLoc.trim().endsWith(',') ? enquiredLoc.trim().slice(0, -1) : enquiredLoc;\n  return enquiredLoc || null;\n};\n/**\r\n * A function to check if a string contains\r\n * any element in a given array\r\n * @returns boolean\r\n */\n\nexport const isStringSubsetInArray = (inputString, arrayOfStrings) => {\n  for (let i = 0; i < arrayOfStrings.length; i++) {\n    if (inputString.includes(arrayOfStrings[i])) {\n      return true;\n    }\n  }\n\n  return false;\n};\n/**\r\n * validator function to check if selected date with time is lesser than current date and time\r\n * @returns\r\n */\n\nexport const validateScheduleTime = currentDate => {\n  return control => {\n    const selectedTime = new Date(control === null || control === void 0 ? void 0 : control.value);\n    selectedTime.setSeconds(0, 0);\n\n    if (!(control === null || control === void 0 ? void 0 : control.value)) {\n      return {\n        required: true\n      };\n    }\n\n    const currentTime = new Date(currentDate);\n    currentTime.setSeconds(0, 0);\n\n    if (currentTime >= selectedTime) {\n      return {\n        invalidTime: true\n      };\n    }\n\n    return null;\n  };\n};\nexport const getBHKDisplayString = (bhkNo, isBHKText = false) => {\n  const displayBHKNo = bhkNo == '0.5' ? '1' : bhkNo;\n  const displayType = bhkNo == '0.5' ? 'RK' : 'BHK';\n  return !isBHKText ? `${displayBHKNo} ${displayType}` : `${displayBHKNo}`;\n};\nexport const getBRDisplayString = (brNo, isBRText = false) => {\n  if (brNo == '0.5') {\n    return 'Studio';\n  }\n\n  return isBRText ? `${brNo}` : `${brNo} BR`;\n}; //History\n\nexport const getBRDisplay = brNo => {\n  if (!brNo) return '';\n  const brNumbers = brNo.split(',').map(n => parseFloat(n.trim())).filter(n => !isNaN(n));\n  return brNumbers.length > 0 ? brNumbers.map(br => `${br} BR`).join(', ') : '';\n};\nexport const getBedsDisplay = bedNo => {\n  var _a;\n\n  if (!bedNo) return '';\n  const bedNumbers = (_a = bedNo === null || bedNo === void 0 ? void 0 : bedNo.split(',')) === null || _a === void 0 ? void 0 : _a.map(n => parseFloat(n.trim()));\n  const formattedBeds = bedNumbers === null || bedNumbers === void 0 ? void 0 : bedNumbers.map(bed => {\n    if (bed === 0) {\n      return 'Studio';\n    } else {\n      return bed === null || bed === void 0 ? void 0 : bed.toString();\n    }\n  });\n  return (formattedBeds === null || formattedBeds === void 0 ? void 0 : formattedBeds.join(', ')) || '';\n}; //History\n\nexport const getBHKDisplay = bhkNo => {\n  var _a;\n\n  if (!bhkNo) return '';\n  const bhkNumbers = (_a = bhkNo === null || bhkNo === void 0 ? void 0 : bhkNo.split(',')) === null || _a === void 0 ? void 0 : _a.map(n => parseFloat(n.trim()));\n  const formattedBHKs = bhkNumbers === null || bhkNumbers === void 0 ? void 0 : bhkNumbers.map(bhk => {\n    if (bhk === 0.5) {\n      return '1RK';\n    } else if (bhk) {\n      return `${bhk} BHK`;\n    } else {\n      return bhk === null || bhk === void 0 ? void 0 : bhk.toString();\n    }\n  });\n  return (formattedBHKs === null || formattedBHKs === void 0 ? void 0 : formattedBHKs.join(', ')) || '';\n};\nexport const generateLeadSourcesArray = () => {\n  const leadSourcesArray = Object.keys(LeadSource).slice(44).sort();\n  return leadSourcesArray;\n};\nexport const snakeToCamel = snakeCase => {\n  if (!snakeCase) return snakeCase;\n  const components = snakeCase.split('_');\n  const camelCaseString = components[0] + components.slice(1).map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('');\n  return camelCaseString;\n};\nexport const getDateRange = (range, currentDate) => {\n  const today = new Date(currentDate);\n  let startDate;\n  let endDate;\n\n  switch (range) {\n    case DateRange.Today:\n      startDate = new Date(today);\n      endDate = new Date(today);\n      break;\n\n    case DateRange.Yesterday:\n      startDate = new Date(today);\n      startDate.setDate(today.getDate() - 1);\n      endDate = new Date(today);\n      endDate.setDate(today.getDate() - 1);\n      break;\n\n    case DateRange.Last7Days:\n      startDate = new Date(today);\n      startDate.setDate(today.getDate() - 6);\n      endDate = new Date(today);\n      break;\n\n    case DateRange.CurrentMonth:\n      startDate = new Date(today.getFullYear(), today.getMonth(), 1);\n      endDate = new Date(today);\n      break;\n\n    case DateRange.TillDate:\n      startDate = null;\n      endDate = new Date(today);\n      break;\n\n    default:\n      startDate = new Date(today);\n      endDate = new Date(today);\n      break;\n  }\n\n  return [startDate, endDate];\n};\nexport const hexToRgba = (hex, opacity) => {\n  hex = hex === null || hex === void 0 ? void 0 : hex.replace('#', '');\n  const r = parseInt(hex.slice(0, 2), 16);\n  const g = parseInt(hex.slice(2, 4), 16);\n  const b = parseInt(hex.slice(4, 6), 16);\n  const validOpacity = Math.min(1, Math.max(0, opacity));\n  return `rgba(${r}, ${g}, ${b}, ${validOpacity})`;\n};\nexport const atLeastTwoSelectedValidator = control => {\n  const teamUsers = control.value;\n\n  if (!teamUsers || teamUsers.length < 2) {\n    return {\n      atLeastTwoSelected: true\n    };\n  }\n\n  return null;\n};\nexport function onFilterChanged(event) {\n  const nodes = [];\n  event.api.forEachNodeAfterFilter(node => {\n    if (node.isSelected()) {\n      nodes.push(node);\n    }\n  });\n  event.api.getSelectedNodes().forEach(node => {\n    if (!nodes.includes(node)) {\n      node.setSelected(false);\n    }\n  });\n}\nexport function isUrl(str) {\n  const urlPattern = new RegExp('^(http(s)?:\\\\/\\\\/)?' + // http:// or https:// (optional)\n  '(www\\\\.)?' + // www. (optional)\n  '[a-zA-Z0-9@:%._\\\\+~#=]{2,256}\\\\.[a-z]{2,6}\\\\b' + // domain.tld\n  '([-a-zA-Z0-9@:%_\\\\+.~#?&//=]*)$', // path (optional)\n  'i' // Case-insensitive flag\n  );\n  return urlPattern.test(str);\n}\nexport function convertUrlsToLinks(text, applyLinkColor = false) {\n  if (!text) return '';\n  const urlRegex = /(\\b(?:https?:\\/\\/|www\\.|[a-zA-Z0-9-]+\\.[a-z]{2,})(?:[^\\s]*))/g;\n  return text.replace(urlRegex, url => {\n    let clickableUrl = url;\n\n    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('www.')) {\n      clickableUrl = `http://${url}`;\n    }\n\n    return applyLinkColor ? `<a href=\"${clickableUrl}\" target=\"_blank\" class=\"text-accent-green\">${url}</a>` : `<a href=\"${clickableUrl}\" target=\"_blank\">${url}</a>`;\n  });\n}\nexport const atLeastOneSelectedValidator = control => {\n  const controlValue = control.value;\n\n  if (!controlValue || controlValue.length < 1) {\n    return {\n      atLeastOneSelected: true\n    };\n  }\n\n  return null;\n};\nexport function generateFloorOptions() {\n  const floors = ['Upper Basement', 'Lower Basement', 'Ground'];\n\n  for (let i = 1; i <= 200; i++) {\n    floors.push(i.toString());\n  }\n\n  return floors;\n}\nexport function buildHttpParams(payload) {\n  let params = new HttpParams();\n  Object.entries(payload).forEach(([key, value]) => {\n    if (value || value === 0) {\n      if (Array.isArray(value)) {\n        const indexes = getIndexes(key, value);\n        indexes.forEach(element => {\n          params = params.append(key, element);\n        });\n      } else {\n        params = params.set(key, value);\n      }\n    }\n  });\n  return params;\n}\nexport function generateEnumList(enumData, displayNames) {\n  return Object.keys(enumData).filter(key => isNaN(Number(key)) && key !== 'None').map((key, index) => ({\n    enumValue: enumData[key],\n    name: key,\n    displayName: (displayNames === null || displayNames === void 0 ? void 0 : displayNames[index]) || key\n  }));\n}\nexport function getFormattedLocation(location) {\n  if (!location) {\n    return '';\n  }\n\n  location = location.replace(/^,+/, '');\n  location = location.replace(/,+/g, ',');\n  return location.trim();\n}\nexport function allowLandlineInput(event) {\n  const allowed = /[0-9\\-]/;\n\n  if (!allowed.test(event.key)) {\n    event.preventDefault();\n  }\n}\nexport function handleCachedData(_x2, _x3, _x4, _x5, _x6, _x7) {\n  return _handleCachedData.apply(this, arguments);\n}\n\nfunction _handleCachedData() {\n  _handleCachedData = _asyncToGenerator(function* (storeName, cacheKey, fetchLastModified, fetchData, buildRecord, getItems) {\n    const dbName = 'CachingDb';\n    let db = null;\n\n    try {\n      db = yield new Promise((resolve, reject) => {\n        const request = indexedDB.open(dbName);\n\n        request.onsuccess = () => resolve(request.result);\n\n        request.onerror = () => reject(request.error);\n      });\n\n      if (!db.objectStoreNames.contains(storeName)) {\n        const newVersion = db.version + 1;\n        db.close();\n\n        try {\n          db = yield new Promise((resolve, reject) => {\n            const request = indexedDB.open(dbName, newVersion);\n\n            request.onupgradeneeded = event => {\n              const upgradeDb = event.target.result;\n\n              if (!upgradeDb.objectStoreNames.contains(storeName)) {\n                upgradeDb.createObjectStore(storeName, {\n                  keyPath: 'id'\n                });\n              }\n            };\n\n            request.onsuccess = () => resolve(request.result);\n\n            request.onerror = () => reject(request.error);\n          });\n\n          if (!db.objectStoreNames.contains(storeName)) {\n            console.error(`[Cache] Failed to create store: ${storeName}. Fetching fresh data.`);\n            if (db) db.close();\n            const fresh = yield fetchData();\n            return getItems(fresh, false);\n          }\n        } catch (storeCreateErr) {\n          console.error(`[Cache] Store creation error for ${storeName}:`, storeCreateErr);\n          if (db) db.close();\n          const fresh = yield fetchData();\n          return getItems(fresh, false);\n        }\n      }\n\n      const localData = yield new Promise(resolve => {\n        try {\n          const tx = db.transaction(storeName, 'readonly');\n          const store = tx.objectStore(storeName);\n          const getReq = store.get(cacheKey);\n\n          getReq.onsuccess = () => resolve(getReq.result || null);\n\n          getReq.onerror = () => resolve(null);\n        } catch (txErr) {\n          console.error(`[Cache] Transaction error reading from ${storeName}:`, txErr);\n          resolve(null);\n        }\n      });\n      const localLastModified = (localData === null || localData === void 0 ? void 0 : localData.lastModified) || null;\n      let serverLastModified = null;\n\n      try {\n        serverLastModified = yield fetchLastModified();\n      } catch (err) {\n        console.error(`[Cache] Failed to fetch server lastModified:`, err);\n        if (db) db.close();\n\n        if (localData) {\n          return getItems(localData, true);\n        }\n\n        const fresh = yield fetchData();\n        return getItems(fresh, false);\n      }\n\n      if (serverLastModified && serverLastModified !== localLastModified) {\n        const fresh = yield fetchData();\n\n        try {\n          yield new Promise((resolve, reject) => {\n            const tx = db.transaction(storeName, 'readwrite');\n            const store = tx.objectStore(storeName);\n            const newRecord = buildRecord(fresh, serverLastModified);\n            store.put(newRecord);\n\n            tx.oncomplete = () => resolve();\n\n            tx.onerror = () => reject(tx.error);\n          });\n        } catch (updateErr) {\n          console.error(`[Cache] Failed to update cache for ${storeName}:`, updateErr);\n        }\n\n        if (db) db.close();\n        return getItems(fresh, false);\n      } else {\n        if (db) db.close();\n        return getItems(localData, true);\n      }\n    } catch (err) {\n      console.error(`[Cache] DB error for ${storeName}:`, err);\n      if (db) db.close();\n\n      try {\n        const fresh = yield fetchData();\n        return getItems(fresh, false);\n      } catch (fetchErr) {\n        console.error(`[Cache] Failed to fetch fresh data:`, fetchErr);\n        throw fetchErr;\n      }\n    }\n  });\n  return _handleCachedData.apply(this, arguments);\n}", "map": {"version": 3, "mappings": ";AAAA,SAASA,UAAT,QAA2B,sBAA3B;AACA,SAEEC,WAFF,EAGEC,SAHF,QAMO,gBANP;AAOA,OAAO,KAAKC,MAAZ,MAAwB,QAAxB;AAEA,SACEC,UADF,EAEEC,sBAFF,EAGEC,gBAHF,EAIEC,cAJF,QAKO,uBALP;AAMA,SACEC,OADF,EAEEC,SAFF,EAGEC,UAHF,EAIEC,WAJF,EAKEC,MALF,EAMEC,aANF,EAOEC,MAPF,EAQEC,UARF,EASEC,iBATF,EAUEC,SAVF,EAWEC,cAXF,EAYEC,mBAZF,EAaEC,cAbF,EAcEC,WAdF,EAeEC,QAfF,QAgBO,kBAhBP;AAkBA,SAASC,WAAW,IAAIC,GAAxB,QAAmC,8BAAnC;AAEA,OAAO,MAAMC,aAAa,GAAG,CAACC,WAAoB,KAArB,KAAsC;EACjE,IAAIC,SAAS,GAAW,EAAxB;EACA,IAAIC,aAAa,GAAW,EAA5B;;EACA,QAAQJ,GAAG,CAACK,IAAZ;IACE,KAAK,KAAL;MACEF,SAAS,GAAG,eAAZ;MACAC,aAAa,GAAG,OAAhB;MACA;;IACF,KAAK,IAAL;MACED,SAAS,GAAG,eAAZ;MACAC,aAAa,GAAG,QAAhB;MACA;;IACF,KAAK,KAAL;MACE;MACAD,SAAS,GAAG,aAAZ;MACAC,aAAa,GAAG,KAAhB;MACA;;IACF,KAAK,MAAL;MACED,SAAS,GAAG,cAAZ;MACAC,aAAa,GAAG,OAAhB;MACA;EAjBJ;;EAmBA,OAAOF,QAAQ,GAAGE,aAAH,GAAmBD,SAAlC;AACD,CAvBM;AAyBP,OAAO,MAAMG,aAAa,GAAG,MAAa;EACxC,IAAIC,SAAS,GAAW,EAAxB;;EACA,IAAIC,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoB,GAApB,EAAyBC,MAAzB,GAAkC,CAAtC,EAAyC;IACvC,IAAIH,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoB,GAApB,EAAyB,CAAzB,EAA4BE,QAA5B,CAAqC,KAArC,CAAJ,EAAiD;MAC/CL,SAAS,GAAGC,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoB,GAApB,EAAyB,CAAzB,CAAZ;IACD,CAFD,MAEO;MACLH,SAAS,GAAGC,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoB,GAApB,EAAyB,CAAzB,EAA4BA,KAA5B,CAAkC,IAAlC,EAAwC,CAAxC,CAAZ;IACD;EACF;;EACD,OACEH,SAAS,IAAIM,YAAY,CAACC,OAAb,CAAqB,WAArB,CAAb,IAAkDb,aAAa,CAAC,IAAD,CAA/D,IAAyE,EAD3E;AAGD,CAZM;AAcP,OAAO,MAAMc,UAAU,GAAG,MAAK;EAC7B,IAAIC,OAAO,GAAG,EAAd;;EACA,QAAQhB,GAAG,CAACK,IAAZ;IACE,KAAK,KAAL;MACEW,OAAO,GAAG,SAAV;MACA;;IACF,KAAK,IAAL;MACEA,OAAO,GAAG,SAAV;MACA;;IACF,KAAK,KAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF,KAAK,MAAL;MACEA,OAAO,GAAG,SAAV;MACA;EAZJ;;EAcA,OAAOA,OAAP;AACD,CAjBM;AAmBP,OAAO,MAAMC,YAAY,GAAG,MAAK;EAC/B,IAAIV,SAAS,GAAGD,aAAa,EAA7B;;EAEA,QAAQ,IAAR;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,KAAKN,GAAG,CAACK,IAAJ,IAAY,IAAZ,IAAoBE,SAAS,IAAI,QAAtC;MACE,OAAO;QACLW,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,mCAFJ;QAGLC,OAAO,EAAE,mCAHJ;QAILC,OAAO,EAAE,wCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;IAQF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,KAAKtB,GAAG,CAACK,IAAJ,IAAY,MAAZ,IAAsBE,SAAS,IAAI,IAAxC;MACE,OAAO;QACLW,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,+BAFJ;QAGLC,OAAO,EAAE,oCAHJ;QAILC,OAAO,EAAE,yCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;;IAQF,KAAKtB,GAAG,CAACK,IAAJ,IAAY,MAAZ,IAAsBE,SAAS,IAAI,kBAAxC;MACE,OAAO;QACLW,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,mCAFJ;QAGLC,OAAO,EAAE,mCAHJ;QAILC,OAAO,EAAE,wCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;;IAOF,KAAKtB,GAAG,CAACK,IAAJ,IAAY,MAAZ,IAAsBE,SAAS,IAAI,cAAxC;MACE,OAAO;QACLW,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,+BAFJ;QAGLC,OAAO,EAAE,+BAHJ;QAILC,OAAO,EAAE,oCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;;IAOF,KAAKtB,GAAG,CAACK,IAAJ,IAAY,MAAZ,IAAsBE,SAAS,IAAI,QAAxC;MACE,OAAO;QACLW,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,mCAFJ;QAGLC,OAAO,EAAE,mCAHJ;QAILC,OAAO,EAAE,wCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;;IAOF;MACE,OAAO;QACLJ,UAAU,EAAE,2BADP;QAELC,OAAO,EAAE,kCAFJ;QAGLC,OAAO,EAAE,uCAHJ;QAILC,OAAO,EAAE,uCAJJ;QAKLC,SAAS,EAAE;MALN,CAAP;EA9DJ;AAsED,CAzEM;AA2EP,OAAO,MAAMC,QAAQ,GAAG,CAACC,UAAD,EAAqBC,QAArB,KACtBC,IAAI,CAACC,IAAL,CAAUH,UAAU,GAAGC,QAAvB,CADK;AAGP,OAAO,MAAMG,qBAAqB,GAAIC,MAAD,IACnCA,MAAM,CAACC,MAAP,CAAc,CAAd,EAAiBC,WAAjB,KAAiCF,MAAM,CAACG,KAAP,CAAa,CAAb,CAD5B;AAGP,OAAO,MAAMC,qBAAqB,GAAG,CACnCC,IADmC,EAEnCC,eAFmC,EAGnCC,KAHmC,KAIjC;EACFF,IAAI,CAACG,UAAL,CAAgB;IAAE,CAACF,eAAD,GAAmBC;EAArB,CAAhB;AACD,CANM;AAQP,OAAO,MAAME,eAAe,GAAG,CAC7BC,IAD6B,EAE7BC,YAF6B,EAG7BC,UAH6B,KAI3B;EACF;EACA,MAAMC,MAAM,GAAG,CAAC,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEZ,MAAd,CAAqB,CAArB,OAA4B,GAA5B,GAAkC,EAAlC,GAAuC,GAAxC,IAA+CU,YAA9D;EAEA,MAAMG,OAAO,GAA8B;IACzCC,gBAAgB,EAAE,SADuB;IAEzCC,YAAY,EAAE,YAF2B;IAGzCC,gBAAgB,EAAE,cAHuB;IAIzCC,YAAY,EAAE,uBAJ2B;IAKzCC,eAAe,EAAE,oBALwB;IAMzCC,YAAY,EAAE,wBAN2B;IAOzCC,gBAAgB,EAAE,2BAPuB;IAQzCC,SAAS,EAAE,UAR8B;IASzCC,QAAQ,EAAE,QAT+B;IAUzCC,GAAG,EAAE;EAVoC,CAA3C;EAYA,MAAMC,MAAM,GAAGX,OAAO,CAACF,UAAD,CAAP,IAAuBE,OAAO,CAAC,iBAAD,CAA7C;EACA,OAAOhE,MAAM,CAAC4D,IAAD,CAAN,CAAagB,SAAb,CAAuBb,MAAvB,EAA+BY,MAA/B,CAAsCA,MAAtC,CAAP;AACD,CAtBM;AAwBP,OAAO,MAAME,eAAe,GAAG,CAACjB,IAAD,EAAakB,aAAb,KAAsC;EACnE,IAAI,CAAClB,IAAL,EAAW,OAAO,IAAP;EACX,MAAMmB,WAAW,GAAG,IAAIC,IAAJ,CAASpB,IAAT,CAApB;EACAmB,WAAW,CAACE,QAAZ,CAAqB,CAArB,EAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;EACA,MAAMC,aAAa,GACjBH,WAAW,CAACI,WAAZ,KACA,GADA,GAEAC,MAAM,CAACL,WAAW,CAACM,QAAZ,KAAyB,CAA1B,CAAN,CAAmCC,QAAnC,CAA4C,CAA5C,EAA+C,GAA/C,CAFA,GAGA,GAHA,GAIAF,MAAM,CAACL,WAAW,CAACQ,OAAZ,EAAD,CAAN,CAA8BD,QAA9B,CAAuC,CAAvC,EAA0C,GAA1C,CAJA,GAKA,GALA,GAMAF,MAAM,CAACL,WAAW,CAACS,QAAZ,EAAD,CAAN,CAA+BF,QAA/B,CAAwC,CAAxC,EAA2C,GAA3C,CANA,GAOA,GAPA,GAQAF,MAAM,CAACL,WAAW,CAACU,UAAZ,EAAD,CAAN,CAAiCH,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CARA,GASA,GATA,GAUAF,MAAM,CAACL,WAAW,CAACW,UAAZ,EAAD,CAAN,CAAiCJ,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CAXF;EAaA,MAAMK,MAAM,GAAGb,aAAa,GAAGA,aAAH,GAAmBc,mBAAmB,EAAlE;EACA,MAAMC,aAAa,GAAGC,YAAY,CAACZ,aAAD,EAAgBS,MAAhB,CAAlC;EACA,OAAOE,aAAP;AACD,CApBM;AAsBP,OAAO,MAAME,iBAAiB,GAAG,CAC/BC,UAD+B,EAE/BlB,aAF+B,KAG7B;EACF;EACA,IAAI,CAACkB,UAAL,EAAiB;IACf,OAAO,IAAP;EACD;;EAED,MAAMpC,IAAI,GAAGqC,aAAa,CAAC,IAAIjB,IAAJ,CAASgB,UAAT,CAAD,CAA1B;EAEA,IAAIE,kBAAJ;EAEA,MAAMnC,MAAM,GAAGe,aAAa,GAAGA,aAAH,GAAmBc,mBAAmB,EAAlE;EACA,MAAMO,IAAI,GAAGpC,MAAM,CAACqC,UAAP,CAAkB,GAAlB,IAAyB,CAAC,CAA1B,GAA8B,CAA3C;EACA,MAAMC,WAAW,GAAGtC,MAAM,CAACuC,OAAP,CAAe,GAAf,EAAoB,EAApB,EAAwBvE,KAAxB,CAA8B,GAA9B,EAAmCwE,GAAnC,CAAuCC,MAAvC,CAApB;EACAN,kBAAkB,GAAGC,IAAI,IAAIE,WAAW,CAAC,CAAD,CAAX,GAAiB,EAAjB,GAAsBA,WAAW,CAAC,CAAD,CAArC,CAAzB;EAEA,MAAMI,YAAY,GAAG,IAAIzB,IAAJ,CACnBpB,IAAI,CAAC8C,OAAL,KAAiBR,kBAAkB,GAAG,EAArB,GAA0B,IADxB,CAArB,CAfE,CAmBF;;EACA,OAAOO,YAAP;AACD,CAxBM;AA0BP,OAAO,MAAMX,YAAY,GAAG,CAACa,SAAD,EAAoB7B,aAApB,KAA6C;EACvE,MAAM8B,SAAS,GAAG5G,MAAM,CAAC6G,GAAP,CAAWF,SAAX,CAAlB;EACA,MAAMG,OAAO,GAAGF,SAAS,CACtBG,KADa,GAEbnC,SAFa,CAEH,CAAC5E,MAAM,CAACgH,QAAP,CAAgBlC,aAAhB,EAA+BmC,SAA/B,EAFE,CAAhB;EAGA,OAAOH,OAAO,CAACnC,MAAR,CAAe,wBAAf,CAAP;AACD,CANM;AAQP,OAAO,MAAMuC,uBAAuB,GAAG,CAACtD,IAAD,EAAakB,aAAb,KAAsC;EAC3E,IAAI,CAAClB,IAAL,EAAW,OAAO,IAAP;EACX,MAAMG,MAAM,GAAGe,aAAa,GAAGA,aAAH,GAAmBc,mBAAmB,EAAlE;EACA,MAAMuB,gBAAgB,GAAG,IAAInC,IAAJ,CAASpB,IAAT,CAAzB;EACA,MAAMsB,aAAa,GACjBiC,gBAAgB,CAAChC,WAAjB,KACA,GADA,GAEAC,MAAM,CAAC+B,gBAAgB,CAAC9B,QAAjB,KAA8B,CAA/B,CAAN,CAAwCC,QAAxC,CAAiD,CAAjD,EAAoD,GAApD,CAFA,GAGA,GAHA,GAIAF,MAAM,CAAC+B,gBAAgB,CAAC5B,OAAjB,EAAD,CAAN,CAAmCD,QAAnC,CAA4C,CAA5C,EAA+C,GAA/C,CAJA,GAKA,GALA,GAMAF,MAAM,CAAC+B,gBAAgB,CAAC3B,QAAjB,EAAD,CAAN,CAAoCF,QAApC,CAA6C,CAA7C,EAAgD,GAAhD,CANA,GAOA,GAPA,GAQAF,MAAM,CAAC+B,gBAAgB,CAAC1B,UAAjB,EAAD,CAAN,CAAsCH,QAAtC,CAA+C,CAA/C,EAAkD,GAAlD,CARA,GASA,GATA,GAUAF,MAAM,CAAC+B,gBAAgB,CAACzB,UAAjB,EAAD,CAAN,CAAsCJ,QAAtC,CAA+C,CAA/C,EAAkD,GAAlD,CAXF;EAYA,MAAMO,aAAa,GAAGC,YAAY,CAACZ,aAAD,EAAgBnB,MAAhB,CAAlC;EACA,OAAO8B,aAAP;AACD,CAlBM;AAoBP,OAAO,MAAMuB,qBAAqB,GAAG,CACnCC,YADmC,EAEnCvC,aAFmC,KAGjC;EACF,IAAI,CAACuC,YAAL,EAAmB,OAAO,IAAP;EACnB,MAAMP,OAAO,GAAG,IAAI9B,IAAJ,CAASqC,YAAT,CAAhB;;EACA,IAAIC,KAAK,CAACR,OAAO,CAACJ,OAAR,EAAD,CAAT,EAA8B;IAC5B,MAAM,IAAIa,KAAJ,CAAU,cAAV,CAAN;EACD;;EAED,IAAI,CAACzC,aAAL,EAAoB,OAAOgC,OAAP;EACpB,MAAMU,aAAa,GAAGV,OAAO,CAACW,WAAR,EAAtB;EACA,MAAMC,eAAe,GAAGC,kBAAkB,CAACH,aAAD,EAAgB1C,aAAhB,CAA1C;EACA,OAAO,IAAIE,IAAJ,CAAS0C,eAAT,CAAP;AACD,CAdM;AAgBP,OAAO,MAAMC,kBAAkB,GAAG,CAACC,OAAD,EAAkB7D,MAAlB,KAAoC;EACpE,MAAM+C,OAAO,GAAG9G,MAAM,CAAC6G,GAAP,CAAWe,OAAX,CAAhB;EACA,MAAMC,aAAa,GAAG7H,MAAM,CAACgH,QAAP,CAAgBjD,MAAhB,EAAwBkD,SAAxB,EAAtB;EACA,MAAMN,SAAS,GAAGG,OAAO,CAACC,KAAR,GAAgBnC,SAAhB,CAA0BiD,aAA1B,CAAlB;EACA,OAAOlB,SAAS,CAAChC,MAAV,CAAiB,qBAAjB,CAAP;AACD,CALM;AAOP,OAAO,MAAMmD,eAAe,GAAG,CAAClE,IAAD,EAAakB,aAAb,KAAsC;EACnE,IAAI,CAAClB,IAAL,EAAW,OAAO,IAAP;EAEX,MAAMG,MAAM,GAAGe,aAAa,GAAGA,aAAH,GAAmBc,mBAAmB,EAAlE;EACA,MAAMmC,UAAU,GAAGhE,MAAM,CAACqC,UAAP,CAAkB,GAAlB,CAAnB;EACA,MAAM,CAAC4B,WAAD,EAAcH,aAAd,IAA+B9D,MAAM,CACxCuC,OADkC,CAC1B,GAD0B,EACrB,EADqB,EAElCA,OAFkC,CAE1B,GAF0B,EAErB,EAFqB,EAGlCvE,KAHkC,CAG5B,GAH4B,EAIlCwE,GAJkC,CAI9BC,MAJ8B,CAArC;EAKA,MAAMN,kBAAkB,GAAG8B,WAAW,GAAG,EAAd,GAAmBH,aAA9C;EACA,MAAMf,OAAO,GAAG,IAAI9B,IAAJ,CAASpB,IAAI,CAAC8C,OAAL,KAAiB9C,IAAI,CAACqE,iBAAL,KAA2B,KAArD,CAAhB;EAEA,MAAMC,QAAQ,GAAGpB,OAAO,CAACqB,WAAR,EAAjB;EACA,MAAMC,UAAU,GAAGtB,OAAO,CAACuB,aAAR,EAAnB;EACA,MAAMC,UAAU,GAAGxB,OAAO,CAACyB,aAAR,EAAnB;EAEA,IAAIC,oBAAJ;;EACA,IAAIT,UAAJ,EAAgB;IACdS,oBAAoB,GAAGN,QAAQ,GAAG,EAAX,GAAgBE,UAAhB,GAA6BlC,kBAApD;EACD,CAFD,MAEO;IACLsC,oBAAoB,GAAGN,QAAQ,GAAG,EAAX,GAAgBE,UAAhB,GAA6BlC,kBAApD;EACD;;EAED,MAAMuC,aAAa,GAAG1F,IAAI,CAAC2F,KAAL,CAAWF,oBAAoB,GAAG,EAAlC,IAAwC,EAA9D;EACA,MAAMG,eAAe,GAAGH,oBAAoB,GAAG,EAA/C;EAEA,MAAMI,aAAa,GAAG,GAAGxD,MAAM,CAAC,CAACqD,aAAa,GAAG,EAAjB,IAAuB,EAAxB,CAAN,CAAkCnD,QAAlC,CACvB,CADuB,EAEvB,GAFuB,CAGxB,IAAIF,MAAM,CAAC,CAACuD,eAAe,GAAG,EAAnB,IAAyB,EAA1B,CAAN,CAAoCrD,QAApC,CAA6C,CAA7C,EAAgD,GAAhD,CAAoD,IAAIF,MAAM,CACjEkD,UADiE,CAAN,CAE3DhD,QAF2D,CAElD,CAFkD,EAE/C,GAF+C,CAE3C,EALlB;EAMA,OAAOsD,aAAP;AACD,CAlCM;AAoCP,OAAO,MAAMC,SAAS,GAAG,CAACC,UAAD,EAAqB/E,MAArB,KAAuC;EAC9D;EACA,IAAI,CAAC+E,UAAL,EAAiB,OAAO,IAAP;EACjB,MAAMC,WAAW,GAAG,IAAI/D,IAAJ,EAApB;EACA,MAAM,CAACgE,IAAD,EAAOC,QAAP,IAAmBH,UAAU,CAAC/G,KAAX,CAAiB,GAAjB,CAAzB;EACA,IAAI,CAACmH,KAAD,EAAQC,OAAR,EAAiBC,OAAjB,IAA4BJ,IAAI,CAACjH,KAAL,CAAW,GAAX,EAAgBwE,GAAhB,CAAoBC,MAApB,CAAhC;EAEA,IAAIyC,QAAQ,KAAK,IAAb,IAAqBC,KAAK,GAAG,EAAjC,EAAqCA,KAAK,IAAI,EAAT;EACrC,IAAID,QAAQ,KAAK,IAAb,IAAqBC,KAAK,KAAK,EAAnC,EAAuCA,KAAK,GAAG,CAAR;EAEvCH,WAAW,CAAC9D,QAAZ,CAAqBiE,KAArB,EAA4BC,OAA5B,EAAqCC,OAAO,IAAI,CAAhD;EACA,MAAMC,UAAU,GAAGtF,MAAM,GAAGA,MAAH,GAAY6B,mBAAmB,EAAxD;EACA,MAAMmC,UAAU,GAAGsB,UAAU,CAACjD,UAAX,CAAsB,GAAtB,CAAnB;EAEA,MAAM,CAAC4B,WAAD,EAAcH,aAAd,IAA+BwB,UAAU,CAC5C/C,OADkC,CAC1B,GAD0B,EACrB,EADqB,EAElCA,OAFkC,CAE1B,GAF0B,EAErB,EAFqB,EAGlCvE,KAHkC,CAG5B,GAH4B,EAIlCwE,GAJkC,CAI9BC,MAJ8B,CAArC;EAKA,MAAM8C,iBAAiB,GAAG,CAACtB,WAAW,GAAG,EAAd,GAAmB,EAAnB,GAAwBH,aAAa,GAAG,EAAzC,IAA+C,IAAzE;EAEA,MAAM0B,YAAY,GAAGxB,UAAU,GAC3B,IAAI/C,IAAJ,CAAS+D,WAAW,CAACrC,OAAZ,KAAwB4C,iBAAjC,CAD2B,GAE3B,IAAItE,IAAJ,CAAS+D,WAAW,CAACrC,OAAZ,KAAwB4C,iBAAjC,CAFJ,CArB8D,CAwB9D;;EACA,OAAOC,YAAP;AACD,CA1BM;AA4BP,OAAO,MAAMC,eAAe,GAAG,CAACV,UAAD,EAAqB/E,MAArB,KAA8C;EAC3E,IAAI,CAAC+E,UAAL,EAAiB,OAAO,IAAP;EAEjB,MAAM,CAACE,IAAD,EAAOC,QAAP,IAAmBH,UAAU,CAAC/G,KAAX,CAAiB,GAAjB,CAAzB;EACA,IAAI,CAACmH,KAAD,EAAQC,OAAR,EAAiBC,OAAjB,IAA4BJ,IAAI,CAACjH,KAAL,CAAW,GAAX,EAAgBwE,GAAhB,CAAoBC,MAApB,CAAhC;EAEA,IAAIyC,QAAQ,KAAK,IAAb,IAAqBC,KAAK,GAAG,EAAjC,EAAqCA,KAAK,IAAI,EAAT;EACrC,IAAID,QAAQ,KAAK,IAAb,IAAqBC,KAAK,KAAK,EAAnC,EAAuCA,KAAK,GAAG,CAAR;EAEvC,MAAMO,QAAQ,GAAG,IAAIzE,IAAJ,EAAjB;EACAyE,QAAQ,CAACxE,QAAT,CAAkBiE,KAAlB,EAAyBC,OAAzB,EAAkCC,OAAO,IAAI,CAA7C;EAEA,MAAMM,UAAU,GAAG3F,MAAM,GAAGA,MAAH,GAAY6B,mBAAmB,EAAxD;EACA,MAAM+D,gBAAgB,GAAGD,UAAU,CAACtD,UAAX,CAAsB,GAAtB,CAAzB;EAEA,MAAM,CAAC4B,WAAD,EAAcH,aAAd,IAA+B6B,UAAU,CAC5CpD,OADkC,CAC1B,GAD0B,EACrB,EADqB,EAElCA,OAFkC,CAE1B,GAF0B,EAErB,EAFqB,EAGlCvE,KAHkC,CAG5B,GAH4B,EAIlCwE,GAJkC,CAI9BC,MAJ8B,CAArC;EAKA,MAAM8C,iBAAiB,GAAG,CAACtB,WAAW,GAAG,EAAd,GAAmB,EAAnB,GAAwBH,aAAa,GAAG,EAAzC,IAA+C,IAAzE;EAEA,MAAMpB,YAAY,GAAGkD,gBAAgB,GACjC,IAAI3E,IAAJ,CAASyE,QAAQ,CAAC/C,OAAT,KAAqB4C,iBAA9B,CADiC,GAEjC,IAAItE,IAAJ,CAASyE,QAAQ,CAAC/C,OAAT,KAAqB4C,iBAA9B,CAFJ;EAIA,OAAO7C,YAAY,CAACmD,kBAAb,CAAgC,OAAhC,EAAyC;IAC9CC,IAAI,EAAE,SADwC;IAE9CC,MAAM,EAAE,SAFsC;IAG9CC,MAAM,EAAE;EAHsC,CAAzC,CAAP;AAKD,CA/BM;AAiCP,OAAO,MAAMnE,mBAAmB,GAAiB,MAAK;EACpD,MAAMoE,eAAe,GAAG,IAAIhF,IAAJ,GAAWiD,iBAAX,EAAxB;EACA,MAAMiB,KAAK,GAAGnG,IAAI,CAAC2F,KAAL,CAAW3F,IAAI,CAACkH,GAAL,CAASD,eAAT,IAA4B,EAAvC,CAAd;EACA,MAAMb,OAAO,GAAGpG,IAAI,CAACkH,GAAL,CAASD,eAAT,IAA4B,EAA5C;EACA,MAAM7D,IAAI,GAAG6D,eAAe,IAAI,CAAnB,GAAuB,EAAvB,GAA4B,GAAzC;EACA,OAAO,GAAG7D,IAAI,GAAGf,MAAM,CAAC8D,KAAD,CAAN,CAAc5D,QAAd,CAAuB,CAAvB,EAA0B,GAA1B,CAA8B,IAAIF,MAAM,CAAC+D,OAAD,CAAN,CAAgB7D,QAAhB,CACjD,CADiD,EAEjD,GAFiD,CAGlD,KAHD;AAID,CATM;AAWP,OAAO,MAAM4E,mBAAmB,GAAiB,MAAK;EACpD,OAAOC,IAAI,CAACC,cAAL,GAAsBC,eAAtB,GAAwCC,QAA/C;AACD,CAFM;AAIP,OAAO,MAAMrE,aAAa,GAAIrC,IAAD,IAAqB;EAChD,IAAI,CAACA,IAAL,EAAW,OAAO,IAAP;EAEX,IAAI2G,aAAa,GAAG,IAAIvF,IAAJ,CAASpB,IAAT,CAApB;EACA,MAAM4G,cAAc,GAAGD,aAAa,CAACtC,iBAAd,EAAvB;EACA,MAAMwC,UAAU,GAAGD,cAAc,GAAG,EAAjB,GAAsB,IAAzC;EACAD,aAAa,GAAG,IAAIvF,IAAJ,CAASuF,aAAa,CAAC7D,OAAd,KAA0B+D,UAAnC,CAAhB;EACA,OAAOF,aAAP;AACD,CARM;AAUP,OAAO,MAAMG,cAAc,GAAI5F,aAAD,IAAgC;EAC5D,MAAM6F,OAAO,GAAG,IAAI3F,IAAJ,EAAhB;EAEA,MAAM4C,OAAO,GAAG,IAAI5C,IAAJ,CACd2F,OAAO,CAACC,cAAR,EADc,EAEdD,OAAO,CAACE,WAAR,EAFc,EAGdF,OAAO,CAACG,UAAR,EAHc,EAIdH,OAAO,CAACxC,WAAR,EAJc,EAKdwC,OAAO,CAACtC,aAAR,EALc,EAMdsC,OAAO,CAACpC,aAAR,EANc,CAAhB;EAQA,MAAMwC,YAAY,GAAGjG,aAAa,GAAGA,aAAH,GAAmBc,mBAAmB,EAAxE;EACA,MAAM,CAACsD,KAAD,EAAQC,OAAR,IAAmB4B,YAAY,CAAChJ,KAAb,CAAmB,GAAnB,EAAwBwE,GAAxB,CAA4BC,MAA5B,CAAzB;EACA,MAAMqB,aAAa,GAAGqB,KAAK,GAAG,EAAR,IAAcC,OAAO,IAAI,CAAzB,CAAtB;EACA,MAAMI,YAAY,GAAG,IAAIvE,IAAJ,CAAS4C,OAAO,CAAClB,OAAR,KAAoBmB,aAAa,GAAG,KAA7C,CAArB;EACA,OAAO0B,YAAP;AACD,CAhBM;AAkBP,OAAO,MAAMyB,wBAAwB,GAAG,CAACpH,IAAD,EAAaqH,IAAb,KAAsC;EAC5E,MAAMC,UAAU,GAAG,CACjB,SADiB,EAEjB,UAFiB,EAGjB,OAHiB,EAIjB,OAJiB,EAKjB,KALiB,EAMjB,MANiB,EAOjB,MAPiB,EAQjB,QARiB,EASjB,WATiB,EAUjB,SAViB,EAWjB,UAXiB,EAYjB,UAZiB,CAAnB;EAeA,MAAMC,GAAG,GAAGvH,IAAI,CAAC2B,OAAL,EAAZ;EACA,MAAM6F,UAAU,GAAGxH,IAAI,CAACyB,QAAL,EAAnB;EACA,MAAMgG,IAAI,GAAGzH,IAAI,CAACuB,WAAL,EAAb;;EAEA,IAAI8F,IAAI,KAAK,OAAb,EAAsB;IACpB,OAAO,GAAGC,UAAU,CAACE,UAAD,CAAY,IAAIC,IAAI,EAAxC;EACD;;EACD,OAAO,GAAGF,GAAG,IAAID,UAAU,CAACE,UAAD,CAAY,IAAIC,IAAI,EAA/C;AACD,CAxBM;AA0BP,OAAO,MAAMC,cAAc,GAAG,CAAC1H,IAAD,EAAaqH,IAAb,KAAoC;EAChE,MAAMM,QAAQ,GAAG,IAAIC,gBAAJ,CAAqB,MAAK;IACzCC,oBAAoB,CAAC7H,IAAD,EAAOqH,IAAP,CAApB;EACD,CAFgB,CAAjB;EAIAS,UAAU,CAAC,MAAK;IACd,MAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAT,CAAuB,kBAAvB,CAAxB;;IACA,IAAIF,eAAJ,EAAqB;MACnBJ,QAAQ,CAACO,OAAT,CAAiBH,eAAjB,EAAkC;QAAEI,SAAS,EAAE,IAAb;QAAmBC,OAAO,EAAE;MAA5B,CAAlC;MACAP,oBAAoB,CAAC7H,IAAD,EAAOqH,IAAP,CAApB;IACD;EACF,CANS,EAMP,GANO,CAAV;AAOD,CAZM;;AAcP,MAAMQ,oBAAoB,GAAG,CAAC7H,IAAD,EAAaqH,IAAb,KAAoC;EAC/D,MAAMgB,aAAa,GAAGL,QAAQ,CAACM,gBAAT,CAA0B,uBAA1B,CAAtB;EACA,MAAMC,uBAAuB,GAAGnB,wBAAwB,CAACpH,IAAD,EAAOqH,IAAP,CAAxD;EAEAgB,aAAa,CAACG,OAAd,CAAuBC,IAAD,IAAS;IAC7B,MAAMC,SAAS,GAAGD,IAAI,CAACE,YAAL,CAAkB,YAAlB,CAAlB;;IAEA,IAAID,SAAJ,EAAe;MACb,MAAME,QAAQ,GAAG,IAAIxH,IAAJ,CAASsH,SAAT,CAAjB;;MAEA,IAAI,CAAChF,KAAK,CAACkF,QAAQ,CAAC9F,OAAT,EAAD,CAAV,EAAgC;QAC9B,MAAM+F,sBAAsB,GAAGzB,wBAAwB,CAACwB,QAAD,EAAWvB,IAAX,CAAvD;;QAEA,IAAIwB,sBAAsB,KAAKN,uBAA/B,EAAwD;UACtD,IAAI,CAACE,IAAI,CAACK,SAAL,CAAeC,QAAf,CAAwB,+BAAxB,CAAL,EAA+D;YAC7D,MAAMC,WAAW,GAAGP,IAAI,CAACR,aAAL,CAClB,+BADkB,CAApB;;YAGA,IAAIe,WAAJ,EAAiB;cACfA,WAAW,CAACF,SAAZ,CAAsBG,GAAtB,CAA0B,cAA1B;YACD;UACF;QACF;MACF;IACF;EACF,CArBD;AAsBD,CA1BD;;AA4BA,OAAO,MAAMC,gBAAgB,GAAIC,QAAD,IAAmB;EACjD,IAAI,CAACA,QAAL,EAAe;IACb,OAAO,IAAP;EACD;;EACD,OAAO,GAAG/M,MAAM,CAAC+M,QAAD,CAAN,CAAiBpI,MAAjB,CAAwB,YAAxB,CAAqC,IAAI3E,MAAM,CAAC+M,QAAD,CAAN,CAAiBpI,MAAjB,CACjD,OADiD,CAElD,UAFD;AAGD,CAPM;AASP,OAAO,MAAMqI,cAAc,GAAID,QAAD,IAAkB;EAC9C,IAAI,CAACA,QAAL,EAAe;IACb,OAAO,IAAP;EACD;;EACD,OAAO,GAAG/M,MAAM,CAAC+M,QAAD,CAAN,CAAiBpI,MAAjB,CAAwB,YAAxB,CAAqC,gBAA/C;AACD,CALM;AAMP,OAAO,MAAMsI,UAAU,GAAIF,QAAD,IAAmB;EAC3C,IAAI,CAACA,QAAL,EAAe;IACb,OAAO,IAAP;EACD;;EACD,OAAO,GAAG/M,MAAM,CAAC+M,QAAD,CAAN,CAAiBpI,MAAjB,CAAwB,YAAxB,CAAqC,WAA/C;AACD,CALM;AAMP,OAAO,MAAMuI,qBAAqB,GAAIC,SAAD,IAAyB;EAC5DC,MAAM,CAACC,IAAP,CAAYF,SAAS,CAACG,QAAtB,EAAgClB,OAAhC,CAAyCmB,KAAD,IAAU;IAChD,MAAMC,OAAO,GAAGL,SAAS,CAACM,GAAV,CAAcF,KAAd,CAAhB;;IACA,IAAIC,OAAO,YAAY1N,WAAvB,EAAoC;MAClC0N,OAAO,CAACE,aAAR,CAAsB;QAAEC,QAAQ,EAAE;MAAZ,CAAtB;IACD,CAFD,MAEO,IAAIH,OAAO,YAAYzN,SAAvB,EAAkC;MACvCmN,qBAAqB,CAACM,OAAD,CAArB;IACD;EACF,CAPD;AAQD,CATM;AAUP,OAAO,MAAMI,cAAc,GAAG,CAC5BC,MAD4B,EAE5BC,MAF4B,EAG5BC,cAH4B,KAI1B;EACF,IAAIF,MAAM,IAAI,IAAV,IAAkBA,MAAM,IAAI,EAAhC,EAAoC,OAAO,EAAP;EACpC,OAAO,GAAGE,cAAc,IAAIvH,MAAM,CAACqH,MAAD,CAAN,CAAeG,cAAf,CAA8BF,MAA9B,CAAqC,EAAjE;AACD,CAPM;AAQP,OAAO,MAAMG,cAAc,GAAG,CAACC,KAAD,EAAeC,OAAf,KAAkC;EAC9D,OAAOD,KAAK,CAACE,IAAN,CAAW,CAACC,CAAD,EAASC,CAAT,KAAqBD,CAAC,CAACF,OAAD,CAAD,GAAaG,CAAC,CAACH,OAAD,CAAd,GAA0B,CAA1B,GAA8B,CAAC,CAA/D,CAAP;AACD,CAFM;AAGP,OAAO,MAAMI,eAAe,GAAG,CAACC,MAAD,EAAiBC,UAAjB,KAC7BA,UAAU,CAACC,MAAX,CAAmBC,IAAD,IAAeA,IAAI,CAACC,UAAL,IAAmBJ,MAApD,EAA4D,CAA5D,EAA+DK,EAD1D;AAEP,OAAO,MAAMC,gBAAgB,GAAG,CAC9BC,cAD8B,EAE9B5B,SAF8B,EAG9BI,KAH8B,EAI9ByB,UAJ8B,KAK5B;EACF,MAAMxB,OAAO,GAAGL,SAAS,CAACM,GAAV,CAAcF,KAAd,CAAhB;;EACA,IAAIC,OAAO,YAAY1N,WAAvB,EAAoC;IAClC,QAAQiP,cAAR;MACE,KAAK5O,gBAAL;QACEqN,OAAO,CAACyB,eAAR;QACA;;MACF,KAAK7O,cAAL;QACEoN,OAAO,CAAC0B,aAAR,CAAsBF,UAAtB;QACA;IANJ;;IAQAxB,OAAO,CAAC2B,sBAAR;EACD;AACF,CAlBM;AAmBP,OAAO,MAAMC,iBAAiB,GAAQ,CAACC,KAAD,EAAaC,KAAb,KACpCtP,MAAM,CAACqP,KAAD,CAAN,CAAcE,cAAd,CAA6BvP,MAAM,CAACsP,KAAD,CAAnC,EAA4C,MAA5C,CADK;AAEP,OAAO,MAAME,aAAa,GAAIC,GAAD,IAC3BrC,MAAM,CAACC,IAAP,CAAYoC,GAAZ,EAAiBzN,MAAjB,KAA4B,CAA5B,IAAiCyN,GAAG,CAACC,WAAJ,KAAoBtC,MADhD;AAEP,OAAO,MAAMuC,sBAAsB,GAAQ,CACzCC,KADyC,EAEzCC,gBAFyC,EAGzCC,YAAqB,KAHoB,KAIvC;EACF,IAAIC,mBAAJ;;EACA,IAAI,EAACF,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAE7N,MAAnB,CAAJ,EAA+B;IAC7B;EACD;;EACD,MAAM,CAACgO,QAAD,IAAkBH,gBAAgB,CAACnB,MAAjB,CACrBuB,IAAD,IAAeA,IAAI,CAACC,WAAL,KAAqBN,KADd,CAAxB;;EAGA,IAAI,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEM,WAAV,MAA0B,aAA1B,IAA2C,CAACJ,SAAhD,EAA2D;IACzDC,mBAAmB,GACjB,CACE,GAAG,IAAII,GAAJ,CAAQH,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEI,UAAV,CAAqB7J,GAArB,CAA0BoI,IAAD,IAAeA,IAAI,CAACuB,WAA7C,CAAR,CADL,EAEE3J,GAFF,CAEOoI,IAAD,IAAc;MAClB,OAAO;QAAEuB,WAAW,EAAEvB;MAAf,CAAP;IACD,CAJD,KAIM,EALR;EAMD,CAPD,MAOO;IACLoB,mBAAmB,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,UAAV,KAAwB,EAA9C;EACD;;EACD,OAAOL,mBAAP;AACD,CAvBM;AAwBP,OAAO,MAAMM,sBAAsB,GAAQ,CACzCT,KADyC,EAEzCC,gBAFyC,KAGvC;EACF,IAAIS,mBAAJ;EACA,MAAM,CAACN,QAAD,IAAkBH,gBAAgB,CAACnB,MAAjB,CACrBuB,IAAD,IAAeA,IAAI,CAACC,WAAL,KAAqBN,KADd,CAAxB;;EAGA,IAAI,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEM,WAAV,MAA0B,aAA9B,EAA6C;IAC3CI,mBAAmB,GACjB,CAAC,GAAG,IAAIH,GAAJ,CAAQH,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEI,UAAV,CAAqB7J,GAArB,CAA0BoI,IAAD,IAAeA,IAAI,CAAC4B,OAA7C,CAAR,CAAJ,EACGhK,GADH,CACQoI,IAAD,IAAc;MACjB,OAAOA,IAAP;IACD,CAHH,EAIGD,MAJH,CAIWC,IAAD,IAAiB;MACvB,IAAIA,IAAI,IAAI,IAAZ,EAAkB;QAChB,OAAO;UAAE4B,OAAO,EAAE5B;QAAX,CAAP;MACD;;MACD,OAAOA,IAAP;IACD,CATH,KASQ,EAVV;EAWD;;EACD,OAAO2B,mBAAP;AACD,CAtBM;AAwBP,OAAO,MAAME,kBAAkB,GAAG,CAChCX,gBADgC,EAEhCY,YAFgC,EAGhCC,gBAHgC,KAIvB;;;EACT,MAAMC,oBAAoB,GAAGd,gBAAgB,CAACe,IAAjB,CAC1BhB,KAAD,IAAWA,KAAK,CAACM,WAAN,KAAsBO,YADN,CAA7B;EAIA,IAAI,EAAC,0BAAoB,SAApB,wBAAoB,WAApB,GAAoB,MAApB,uBAAoB,CAAEL,UAAtB,MAAgC,IAAhC,IAAgCS,aAAhC,GAAgC,MAAhC,GAAgCA,GAAE7O,MAAnC,CAAJ,EAA+C,OAAO,EAAP;;EAE/C,IAAI2O,oBAAoB,CAACT,WAArB,KAAqC,aAAzC,EAAwD;IACtD,OAAOS,oBAAoB,CAACP,UAArB,CACJ1B,MADI,CACIoC,QAAD,IACNJ,gBAAgB,CAACzO,QAAjB,CAA0B6O,QAAQ,CAACZ,WAAnC,CAFG,EAIJ3J,GAJI,CAICuK,QAAD,IAAmBA,QAAQ,CAACjC,EAJ5B,CAAP;EAKD,CAND,MAMO;IACL,OAAO8B,oBAAoB,CAACP,UAArB,CACJ1B,MADI,CACIoC,QAAD,IACNJ,gBAAgB,CAACzO,QAAjB,CAA0B6O,QAAQ,CAACZ,WAAnC,CAFG,EAIJ3J,GAJI,CAICuK,QAAD,IAAmBA,QAAQ,CAACjC,EAJ5B,CAAP;EAKD;AACF,CAxBM;AA0BP,OAAO,MAAMkC,iBAAiB,GAAQ,CACpClB,gBADoC,EAEpCY,YAFoC,EAGpCO,eAHoC,KAIlC;EACF,MAAM,CAACL,oBAAD,IAA8Bd,gBAAgB,CAACnB,MAAjB,CACjCkB,KAAD,IAAgBA,KAAK,CAACM,WAAN,IAAqBO,YADH,CAApC;EAGA,IAAI,EAACE,oBAAoB,SAApB,wBAAoB,WAApB,GAAoB,MAApB,uBAAoB,CAAEP,UAAtB,CAAiCpO,MAAlC,CAAJ,EAA8C,OAA9C,KACK,IACH,qBAAoB,SAApB,wBAAoB,WAApB,GAAoB,MAApB,uBAAoB,CAAEkO,WAAtB,KAAqC,aAArC,IACAc,eAAe,IAAI,MAFhB,EAGH;IACA,MAAM,CAACC,uBAAD,IACJN,oBAAoB,SAApB,wBAAoB,WAApB,GAAoB,MAApB,uBAAoB,CAAEP,UAAtB,CAAiC1B,MAAjC,CACGoC,QAAD,IAAmBA,QAAQ,CAACZ,WAAT,IAAwBc,eAD7C,CADF;IAIA,OAAOC,uBAAuB,SAAvB,2BAAuB,WAAvB,GAAuB,MAAvB,0BAAuB,CAAEpC,EAAhC;EACD,CATI,MASE;IACL,MAAM,CAACoC,uBAAD,IACJN,oBAAoB,SAApB,wBAAoB,WAApB,GAAoB,MAApB,uBAAoB,CAAEP,UAAtB,CAAiC1B,MAAjC,CACGoC,QAAD,IAAmBA,QAAQ,CAACZ,WAAT,IAAwBc,eAD7C,CADF;IAIA,OAAOC,uBAAuB,SAAvB,2BAAuB,WAAvB,GAAuB,MAAvB,0BAAuB,CAAEpC,EAAhC;EACD;AACF,CAzBM;AA2BP,OAAO,MAAMqC,SAAS,GAASrK,GAAD,IAAc7G,MAAM,CAAC6G,GAAP,CAAWA,GAAX,EAAgBsK,MAAhB,EAArC;AACP,OAAO,MAAMC,gBAAgB,GAAG,CAACrE,QAAD,EAAiBpI,MAAjB,KAC9B3E,MAAM,CAAC,IAAIgF,IAAJ,CAAS+H,QAAT,CAAD,CAAN,CAA2BnI,SAA3B,CAAqC,CAArC,EAAwCD,MAAxC,CAA+CA,MAA/C,CADK;AAEP,OAAO,MAAM0M,eAAe,GAAIC,SAAD,IAC7BA,SAAS,CAACrP,QAAV,CAAmB,OAAnB,CADK;AAGP,OAAO,MAAMsP,eAAe,GAAIC,SAAD,IAAsB;EACnD,OAAOH,eAAe,CAACG,SAAD,CAAf,GACHA,SADG,GAEH,GAAGnQ,GAAG,CAACoQ,gBAAgB,GAAGD,SAAS,EAFvC;AAGD,CAJM;AAKP,OAAO,MAAME,WAAW,GAAQ,CAC9B7C,EAD8B,EAE9B8C,YAF8B,KAG3BA,YAAY,CAACjD,MAAb,CAAqBC,IAAD,IAA8BA,IAAI,CAACE,EAAL,KAAYA,EAA9D,EAAkE,CAAlE,CAHE;AAKP,OAAO,MAAM+C,cAAc,GAAG,MAAK;EACjC,MAAMC,OAAO,GAAG3P,YAAY,CAACC,OAAb,CAAqB,SAArB,CAAhB;;EACA,IAAI0P,OAAO,IAAIA,OAAO,CAAC7P,MAAvB,EAA+B;IAC7B,MAAM8P,cAAc,GAAGC,IAAI,CAACC,KAAL,CAAWC,IAAI,CAACJ,OAAO,CAAC9P,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAf,EAAwCmQ,GAA/D;IACA,MAAMC,cAAc,GAClBpP,IAAI,CAAC2F,KAAL,CAAW,IAAI1D,IAAJ,GAAW0B,OAAX,KAAuB,IAAlC,KAA2CoL,cAD7C;IAEA,OAAOK,cAAP;EACD;;EACD,OAAO,IAAP;AACD,CATM;AAWP,OAAO,MAAMC,WAAW,GAAIC,IAAD,IAAkBA,IAAI,KAAKpS,UAA/C;AAEP,OAAO,MAAMqS,oBAAoB,GAAG,CAClCC,QADkC,EAElCC,SAFkC,EAGlCC,iBAA0B,KAHQ,KAIhC;;;EACF,IAAI,CAACF,QAAD,IAAaH,WAAW,CAACG,QAAD,CAAxB,IAAsC,EAACC,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAExQ,MAAZ,CAA1C,EAA8D,OAAO,IAAP;EAE9D,MAAM0Q,IAAI,GAAG,eAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEhE,MAAX,CACVgE,IAAD,IAAc;;;IACZ,WAAI,CAAC7D,EAAL,IAAW0D,QAAX,IACCG,IAAI,CAACC,SAAL,KAAkB,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE5Q,KAAV,CAAgB,GAAhB,OAAoB,IAApB,IAAoB8O,aAApB,GAAoB,MAApB,GAAoBA,GAAG,CAAH,CAAtC,KACC6B,IAAI,CAACE,QAAL,KAAiB,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7Q,KAAV,CAAgB,GAAhB,OAAoB,IAApB,IAAoB8Q,aAApB,GAAoB,MAApB,GAAoBA,GAAG,CAAH,CAArC,CAFF,IAGA,GAAGH,IAAI,CAACC,SAAS,IAAID,IAAI,CAACE,QAAQ,EAAlC,KAAyCL,QAHzC;EAGiD,CALxC,OAMZ,IANY,IAMZ1B,aANY,GAMZ,MANY,GAMZA,GAAG,CAAH,CAND;EAQA,OAAO6B,IAAI,GACPD,cAAc,GACZ,GAAGC,IAAI,CAACC,SAAS,IAAID,IAAI,CAACE,QAAQ,EADtB,GAEZF,IAHK,GAIP,IAJJ;AAKD,CApBM;AAsBP,OAAO,MAAMI,cAAc,GAAG,CAC5BtF,OAD4B,EAE5BuF,UAF4B,KAGb;EACf,OAAO,MAAK;IACV,IAAIvF,OAAO,CAAC/J,KAAR,KAAkBsP,UAAU,CAACtP,KAAjC,EACE,OAAO;MAAEuP,cAAc,EAAE;IAAlB,CAAP;IACF,OAAO,IAAP;EACD,CAJD;AAKD,CATM;AAWP,OAAO,MAAMC,OAAO,GAAG,CAACC,EAAD,EAAUC,GAAV,KACrBD,EAAE,CAACE,MAAH,CAAU,CAACC,EAAD,EAAUC,CAAV,KAAoB;EAC5B,CAACD,EAAE,CAACC,CAAC,CAACH,GAAD,CAAF,CAAF,GAAaE,EAAE,CAACC,CAAC,CAACH,GAAD,CAAF,CAAF,IAAc,EAA5B,EAAgCI,OAAhC,GAA0CC,IAA1C,CAA+CF,CAA/C;EACA,OAAOD,EAAP;AACD,CAHD,EAGG,EAHH,CADK;AAMP,OAAO,MAAMI,UAAU,GAAG,CAACzD,QAAD,EAAmB9B,KAAnB,KAAmC;EAC3D,IACE8B,QAAQ,KAAK,QAAb,IACAA,QAAQ,KAAK,SADb,IAEAA,QAAQ,KAAK,aAHf,EAIE;IACA9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAO/N,UAAU,CAAC+N,IAAD,CAAjB;IACD,CAFO,CAAR;EAGD,CARD,MAQO,IAAIqB,QAAQ,KAAK,UAAjB,EAA6B;IAClC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOtO,OAAO,CAACsO,IAAD,CAAd;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,QAAjB,EAA2B;IAChC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOzO,sBAAsB,CAACwT,OAAvB,CAA+B/E,IAA/B,CAAP;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,YAAjB,EAA+B;IACpC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAO3N,mBAAmB,CAAC2N,IAAD,CAA1B;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,aAAjB,EAAgC;IACrC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOnO,WAAW,CAACmO,IAAD,CAAlB;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,wBAAjB,EAA2C;IAChD9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOpO,UAAU,CAACoO,IAAD,CAAjB;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,iBAAb,IAAkCA,QAAQ,KAAK,WAAnD,EAAgE;IACrE9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOjO,aAAa,CAACiO,IAAD,CAApB;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,YAAjB,EAA+B;IACpC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAO7N,SAAS,CAAC6N,IAAD,CAAhB;IACD,CAFO,CAAR;EAGD,CAJM,MAIA,IAAIqB,QAAQ,KAAK,UAAjB,EAA6B;IAClC9B,KAAK,GAAGA,KAAK,CAAC3H,GAAN,CAAWoI,IAAD,IAAS;MACzB,OAAOzN,WAAW,CAACyN,IAAD,CAAlB;IACD,CAFO,CAAR;EAGD;;EACD,OAAOT,KAAP;AACD,CA3CM;AA6CP,OAAO,MAAMyF,YAAY,GAAG,CAACC,MAAD,EAAiBC,QAAjB,KAA4C;EACtE,MAAMC,2BAA2B,GAAG,CAACF,MAAD,EAAiBG,MAAjB,KAAmC;IACrE,IAAIH,MAAM,GAAG,IAAb,EAAmB;MACjB,OAAOG,MAAM,GAAG,GAAT,IAAeH,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEI,QAAR,EAAf,CAAP;IACD,CAFD,MAEO,IAAIJ,MAAM,IAAI,IAAV,IAAkBA,MAAM,GAAG,OAA/B,EAAwC;MAC7C,OAAOG,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,IAAV,EAAgBK,OAAhB,CAAwB,CAAxB,CAAf,GAA4C,IAAnD;IACD,CAFM,MAEA,IAAIL,MAAM,IAAI,OAAV,IAAqBA,MAAM,GAAG,UAAlC,EAA8C;MACnD,OAAOG,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,OAAV,EAAmBK,OAAnB,CAA2B,CAA3B,CAAf,GAA+C,IAAtD;IACD,CAFM,MAEA;MACL,OAAOF,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,UAAV,EAAsBK,OAAtB,CAA8B,CAA9B,CAAf,GAAkD,IAAzD;IACD;EACF,CAVD;;EAWA,MAAMC,iBAAiB,GAAG,CAACN,MAAD,EAAiBG,MAAjB,KAAmC;IAC3D,IAAIH,MAAM,GAAG,IAAb,EAAmB;MACjB,OAAOG,MAAM,GAAG,GAAT,IAAeH,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEI,QAAR,EAAf,CAAP;IACD,CAFD,MAEO,IAAIJ,MAAM,IAAI,IAAV,IAAkBA,MAAM,GAAG,MAA/B,EAAuC;MAC5C,OAAOG,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,IAAV,EAAgBK,OAAhB,CAAwB,CAAxB,CAAf,GAA4C,IAAnD;IACD,CAFM,MAEA,IAAIL,MAAM,IAAI,MAAV,IAAoBA,MAAM,GAAG,QAAjC,EAA2C;MAChD,OAAOG,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,MAAV,EAAkBK,OAAlB,CAA0B,CAA1B,CAAf,GAA8C,OAArD;IACD,CAFM,MAEA;MACL,OAAOF,MAAM,GAAG,GAAT,GAAe,CAACH,MAAM,GAAG,QAAV,EAAoBK,OAApB,CAA4B,CAA5B,CAAf,GAAgD,KAAvD;IACD;EACF,CAVD;;EAYA,IAAIJ,QAAQ,KAAK,IAAjB,EAAuB;IACrB,OAAOD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEI,QAAR,EAAP;EACD;;EAED,IAAIH,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE5R,QAAV,CAAmB,KAAnB,CAAJ,EAA+B;IAC7B,OAAOiS,iBAAiB,CAACN,MAAD,EAAS,KAAT,CAAxB;EACD,CAFD,MAEO;IACL,OAAOE,2BAA2B,CAACF,MAAD,EAASC,QAAT,CAAlC;EACD;AACF,CAjCM;AAmCP,OAAO,MAAMM,WAAW,GAAIC,KAAD,IAAe;EACxC,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAtB;EACA,MAAMC,YAAY,GAAG,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,EAAY,EAAZ,CAArB;;EACA,IACE,EACGD,OAAO,IAAI,EAAX,IAAiBA,OAAO,IAAI,EAA7B,IACCA,OAAO,IAAI,EAAX,IAAiBA,OAAO,IAAI,GAD7B,IAEAC,YAAY,CAACrS,QAAb,CAAsBoS,OAAtB,CAHF,CADF,EAME;IACAD,KAAK,CAACG,cAAN;EACD;AACF,CAZM;AAcP,OAAO,MAAMC,sBAAsB,GAAG,CAACJ,KAAD,EAAaK,YAAb,KAAqC;EACzE,MAAMJ,OAAO,GAAGD,KAAK,CAACC,OAAtB;EACA,MAAMC,YAAY,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,EAAP,EAAW,EAAX,EAAe,EAAf,CAArB;EACA,MAAMI,WAAW,GAAIL,OAAO,IAAI,EAAX,IAAiBA,OAAO,IAAI,EAA7B,IAAqCA,OAAO,IAAI,EAAX,IAAiBA,OAAO,IAAI,GAArF;EACA,MAAMM,YAAY,GAAGN,OAAO,KAAK,GAAZ,IAAmBA,OAAO,KAAK,GAApD;;EACA,IAAI,EAAEK,WAAW,IAAIC,YAAf,IAA+BL,YAAY,CAACrS,QAAb,CAAsBoS,OAAtB,CAAjC,CAAJ,EAAsE;IACpED,KAAK,CAACG,cAAN;IACA;EACD;;EACD,IAAI,CAACF,OAAO,KAAK,GAAZ,IAAmBA,OAAO,KAAK,GAAhC,KAAwCI,YAAY,CAACxS,QAAb,CAAsB,GAAtB,CAA5C,EAAwE;IACtEmS,KAAK,CAACG,cAAN;EACD;AACF,CAZM;AAcP,OAAO,MAAMK,eAAe,GAAG,CAC7BC,QAD6B,EAE7BC,QAF6B,EAG7BC,UAH6B,EAI7BC,eAJ6B,EAK7BC,MAL6B,EAM7BC,MAN6B,EAO7BC,WAP6B,EAQ7BC,QAR6B,EAS7BrM,WAT6B,EAU7BsM,MAV6B,KAW3B;EACF,IAAIC,WAAW,GAAG,EAAlB;EACA,MAAMC,qBAAqB,GAAQ,EAAnC;EAEA,IAAIC,YAAY,GAAQ,EAAxB;EAEAX,QAAQ,GAAGA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvO,OAAV,CAAkB,eAAlB,EAAoCmP,KAAD,IAAkB;;;IAC9D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATU,CAAX;EAUA2O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;EAUA4O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE5O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;;EAWA,MAAMqP,YAAY,GAAIC,QAAD,IAAqB;IAAA;;IACxC,qBAAQ,CAACF,WAAT,QAAsB,IAAtB,IAAsB7E,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvK,OAAF,CAAU,SAAV,EAAqB,EAArB,CAAtB;EAA8C,CADhD;;EAGA,MAAMuP,mBAAmB,GAAG,CAACC,GAAD,EAAWN,YAAX,KAAgC;IAC1D,IAAIM,GAAJ,EAAS;MACPN,YAAY,CAACpJ,OAAb,CAAsBuC,IAAD,IAAc;;;QACjC,IAAIoH,KAAK,CAACC,OAAN,CAAcrH,IAAI,CAACsH,GAAnB,CAAJ,EAA6B;UAC3BtH,IAAI,CAACsH,GAAL,CAAS7J,OAAT,CAAkBwJ,QAAD,IAAqB;;;YACpC,MAAMM,aAAa,GAAGP,YAAY,CAACC,QAAD,CAAlC;YACAE,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+B,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEE,IAAN,MAAU,IAAV,IAAUvF,aAAV,GAAU,MAAV,GAAUA,GAAEwF,IAAF,EAAzC,CAAN;UACD,CAHD;QAID,CALD,MAKO;UACL,MAAMH,aAAa,GAAGP,YAAY,CAAChH,IAAI,CAACsH,GAAN,CAAlC;UACAH,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+B,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEE,IAAN,MAAU,IAAV,IAAUvF,aAAV,GAAU,MAAV,GAAUA,GAAEwF,IAAF,EAAzC,CAAN;QACD;MACF,CAVD;IAWD;;IAED,OAAOP,GAAP;EACD,CAhBD;;EAkBA,MAAMQ,eAAe,GAAG,CAACC,QAAD,EAAgBC,KAAhB,KAAiC;;;IACvD,IAAI5S,IAAJ;IACA,IAAI6S,OAAJ;IACA,IAAIC,UAAJ;;IACA,IAAI,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,CAAJ,EAAuD;MACrDhT,IAAI,GAAG,IAAIoB,IAAJ,CAAS,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2R,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,CAAT,CAAP;MACA,IAAIC,KAAK,GAAG,IAAI7R,IAAJ,CAAS+D,WAAT,CAAZ;MAEA0N,OAAO,GAAG7S,IAAI,CAAC8C,OAAL,KAAiBmQ,KAAK,CAACnQ,OAAN,EAA3B;MACAgQ,UAAU,GAAG,EAAb;;MAEA,IAAID,OAAO,GAAG,CAAd,EAAiB;QACfC,UAAU,GAAG3T,IAAI,CAAC2F,KAAL,CAAW+N,OAAO,IAAI,OAAO,EAAP,GAAY,EAAhB,CAAlB,IAAyC,SAAtD;QACAA,OAAO,GAAGA,OAAO,IAAI,OAAO,EAAP,GAAY,EAAhB,CAAjB;QACAC,UAAU,IAAI3T,IAAI,CAAC2F,KAAL,CAAW+N,OAAO,IAAI,OAAO,EAAX,CAAlB,IAAoC,OAAlD;MACD;IACF;;IAED,IAAIX,GAAG,GAAGjB,QAAV;;IACA,IAAI,cAAQ,CAACiC,UAAT,MAAmB,IAAnB,IAAmBjG,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE7O,MAAzB,EAAiC;MAC/B,KAAK,MAAM+U,SAAX,IAAwBR,QAAQ,CAACO,UAAjC,EAA6C;QAC3C,IAAI,EAACC,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAX,CAAyB5Q,UAAzB,CAAoC,IAApC,CAAD,CAAJ,EAAgD;UAC9CmP,qBAAqB,CAACwB,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAZ,CAArB,GAAkDD,SAAS,CAACtT,KAA5D;QACD;MACF;IACF;;IACD+R,YAAY,GAAG,CACb;MAAES,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAE,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEa,IAAV,MAAc,IAAd,IAAcpE,aAAd,GAAc,MAAd,GAAcA,GAAEvM,OAAF,CAAU,MAAV,EAAkB,GAAlB,EAAuB+P,IAAvB;IAAzC,CADa,EAEb;MACEJ,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,GAAGhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEzC,SAAS,IAAIyC,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAExC,QAAQ;IAFpD,CAFa,EAMb;MAAEqD,GAAG,EAAE,aAAP;MAAsBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8B;IAAtC,CANa,EAOb;MAAEjB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+B;IAAvC,CAPa,EAQb;MAAElB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAErB,UAAU,IAAI;IAA3C,CARa,EASb;MACEkB,GAAG,EAAE,CAAC,QAAD,EAAW,iBAAX,CADP;MAEEG,IAAI,EACF,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEO,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,IACIjT,eAAe,CACf,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEgT,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,CADe,EAEf,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEQ,YAAV,MAAsB,IAAtB,IAAsBC,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvS,aAFT,EAGf,cAHe,CADnB,GAMI;IATR,CATa,EAoBb;MACEmR,GAAG,EAAE,wBADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkB,UAAV,MAAoB,IAApB,IAAoBC,aAApB,GAAoB,MAApB,GAAoBA,GAAEvV,MAAtB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsV,UAAV,MAAoB,IAApB,IAAoBE,aAApB,GAAoB,MAApB,GAAoBA,GAClBjR,GADkB,CACbyJ,QAAD,IAAmByH,QAAQ,CAACzH,QAAQ,CAAC0H,QAAV,CADb,EAEnBC,IAFmB,CAEd,KAFc,CADlB,GAIF;IANN,CApBa,EA4Bb;MACE1B,GAAG,EAAE,uBADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwB,QAAV,MAAkB,IAAlB,IAAkBC,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE7V,MAApB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4V,QAAV,MAAkB,IAAlB,IAAkBE,aAAlB,GAAkB,MAAlB,GAAkBA,GAChBvR,GADgB,CACXwR,OAAD,IAAkBN,QAAQ,CAACM,OAAO,CAACL,QAAT,EAAmB,IAAnB,CADd,EAEjBC,IAFiB,CAEZ,KAFY,CADhB,GAIF;IANN,CA5Ba,EAoCb;MACE1B,GAAG,EAAE,CAAC,QAAD,EAAW,gBAAX,CADP;MAEEG,IAAI,EACF,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEO,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,IACIjT,eAAe,CACf,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEgT,aAAV,MAA2BJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,YAArC,CADe,EAEf,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEQ,YAAV,MAAsB,IAAtB,IAAsBY,aAAtB,GAAsB,MAAtB,GAAsBA,GAAElT,aAFT,EAGf,kBAHe,CADnB,GAMI;IATR,CApCa,EA+Cb;MACEmR,GAAG,EAAE,CAAC,eAAD,EAAkB,YAAlB,CADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwB,QAAV,MAAkB,IAAlB,IAAkBK,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEjW,MAApB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4V,QAAV,MAAkB,IAAlB,IAAkBM,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE3R,GAAF,CAAOwR,OAAD,IAAkBA,OAAO,CAACd,IAAhC,EAAsCU,IAAtC,CAA2C,IAA3C,CADhB,GAEF;IAJN,CA/Ca,EAqDb;MACE1B,GAAG,EAAE,CAAC,gBAAD,EAAmB,cAAnB,CADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkB,UAAV,MAAoB,IAApB,IAAoBa,aAApB,GAAoB,MAApB,GAAoBA,GAAEnW,MAAtB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsV,UAAV,MAAoB,IAApB,IAAoBc,aAApB,GAAoB,MAApB,GAAoBA,GAClB7R,GADkB,CACbyJ,QAAD,IAAmBA,QAAQ,CAACqI,KADd,EAEnBV,IAFmB,CAEd,IAFc,CADlB,GAIF;IANN,CArDa,EA6Db;MACE1B,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBC,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,WAAnB,IACF,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,OAAV,MAAiB,IAAjB,IAAiBG,aAAjB,GAAiB,MAAjB,GAAiBA,GAAED,WAAnB,IACF,IADE,GAEF7E,YAAY,CACV,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2E,OAAV,MAAiB,IAAjB,IAAiBI,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEF,WADT,EAEV,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,OAAV,MAAiB,IAAjB,IAAiBK,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE9E,QAAnB,KAA+BmB,eAFrB,CAFV,GAMF,GAPI,GAQF;IAVN,CA7Da,EAyEb;MACEiB,GAAG,EAAE,CAAC,cAAD,EAAiB,eAAjB,CADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBM,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,WAAnB,IACF,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEP,OAAV,MAAiB,IAAjB,IAAiBQ,aAAjB,GAAiB,MAAjB,GAAiBA,GAAED,WAAnB,IACF,IADE,GAEFlF,YAAY,CACV,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2E,OAAV,MAAiB,IAAjB,IAAiBS,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEF,WADT,EAEV,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEP,OAAV,MAAiB,IAAjB,IAAiBU,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEnF,QAAnB,KAA+BmB,eAFrB,CAFV,GAMF,GAPI,GAQF;IAVN,CAzEa,EAqFb;MACEiB,GAAG,EAAE,CACH,eADG,EAEH,gBAFG,EAGH,eAHG,EAIH,gBAJG,EAKH,YALG,EAMH,aANG,CADP;MASEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBW,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,YAAnB,IACF,aAAC,IAAG,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEZ,OAAV,MAAiB,IAAjB,IAAiBa,aAAjB,GAAiB,MAAjB,GAAiBA,GAAED,YAAtB,CAAD,OAAoC,IAApC,IAAoCE,aAApC,GAAoC,MAApC,GAAoCA,GAClC7S,GADkC,CAC7B0E,IAAD,IAAezK,WAAW,CAACyK,IAAD,CADI,CAApC,MACuC,IADvC,IACuCoO,aADvC,GACuC,MADvC,GACuCA,GACrC1B,IADqC,CAChC,IADgC,CAFrC,GAIF;IAbN,CArFa,EAoGb;MAAE1B,GAAG,EAAE,QAAP;MAAiBG,IAAI,EAAE;IAAvB,CApGa,EAqGb;MACEH,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAEK,OAAO,GAAG,CAAV,GAAcC,UAAd,GAA2B;IAFnC,CArGa,EAyGb;MACET,GAAG,EAAE,CACH,oBADG,EAEH,aAFG,EAGH,WAHG,EAIH,qBAJG,CADP;MAOEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBgB,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,SAAnB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjB,OAAV,MAAiB,IAAjB,IAAiBkB,aAAjB,GAAiB,MAAjB,GAAiBA,GAAED,SAAF,CAChBhT,GADgB,CACXkT,OAAD,IAAkBC,uBAAuB,CAACD,OAAD,CAD7B,EAEhB9B,IAFgB,CAEX,IAFW,CADf,GAIF;IAXN,CAzGa,EAsHb;MACE1B,GAAG,EAAE,6BADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuD,kBAAV,KAAgC;IAFxC,CAtHa,EA0Hb;MACE1D,GAAG,EAAE,CAAC,mBAAD,EAAsB,qBAAtB,CADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwD,SAAV,KAAuB;IAF/B,CA1Ha,EA8Hb;MACE3D,GAAG,EAAE,wBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyD,QAAV,KAAsB;IAF9B,CA9Ha,EAkIb;MACE5D,GAAG,EAAE,cADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEc,KAAV,KAAmB;IAF3B,CAlIa,EAsIb;MACEjB,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0D,YAAV,KAA0B;IAFlC,CAtIa,EA0Ib;MACE7D,GAAG,EAAE,uBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2D,iBAAV,KAA+B;IAFvC,CA1Ia,EA8Ib;MACE9D,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4D,aAAV,KAA2B;IAFnC,CA9Ia,EAkJb;MACE/D,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6D,WAAV,KAAyB;IAFjC,CAlJa,EAsJb;MACEhE,GAAG,EAAE,CAAC,iBAAD,EAAoB,aAApB,CADP;MAEEG,IAAI,EAAE,CAAC,MAAK;QACV,MAAM1D,IAAI,GAAGyC,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEvE,IAAb,CACV8B,IAAD,IAAeA,IAAI,CAAC7D,EAAL,MAAY0H,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2D,QAAtB,CADJ,CAAb;QAGA,OAAOxH,IAAI,GAAGA,IAAI,CAACC,SAAL,GAAiB,GAAjB,GAAuBD,IAAI,CAACE,QAA/B,GAA0C,EAArD;MACD,CALK;IAFR,CAtJa,EA+Jb;MACEqD,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;QACV,MAAM1D,IAAI,GAAGyC,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEvE,IAAb,CACV8B,IAAD,IAAeA,IAAI,CAAC7D,EAAL,MAAY0H,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4D,eAAtB,CADJ,CAAb;QAGA,OAAOzH,IAAI,GAAGA,IAAI,CAACC,SAAL,GAAiB,GAAjB,GAAuBD,IAAI,CAACE,QAA/B,GAA0C,EAArD;MACD,CALK;IAFR,CA/Ja,EAwKb;MACEqD,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;QACV,MAAM1D,IAAI,GAAGyC,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEvE,IAAb,CACV8B,IAAD,IAAeA,IAAI,CAAC7D,EAAL,MAAY0H,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6D,cAAtB,CADJ,CAAb;QAGA,OAAO1H,IAAI,GAAGA,IAAI,CAACC,SAAL,GAAiB,GAAjB,GAAuBD,IAAI,CAACE,QAA/B,GAA0C,EAArD;MACD,CALK;IAFR,CAxKa,EAiLb;MACEqD,GAAG,EAAE,oBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;QACV,MAAM1D,IAAI,GAAGyC,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEvE,IAAb,CACV8B,IAAD,IAAeA,IAAI,CAAC7D,EAAL,MAAY0H,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8D,eAAtB,CADJ,CAAb;QAGA,OAAO3H,IAAI,GAAGA,IAAI,CAACC,SAAL,GAAiB,GAAjB,GAAuBD,IAAI,CAACE,QAA/B,GAA0C,EAArD;MACD,CALK;IAFR,CAjLa,EA0Lb;MACEqD,GAAG,EAAE,wBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkE,eAAV,IACF/D,QAAQ,CAAC+D,eAAT,CACC/T,GADD,CACMgU,OAAD,IAAkBA,OAAO,CAACC,QAD/B,EAEC7C,IAFD,CAEM,IAFN,CADE,GAIF;IANN,CA1La,EAkMb;MACE1B,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqE,WAAV,KAAyB;IAFjC,CAlMa,EAsMb;MACExE,GAAG,EAAE,aADP;MAEEG,IAAI,EAAEjV,QAAQ,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmX,OAAV,MAAiB,IAAjB,IAAiBoC,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,QAApB,CAAR,IAAyC;IAFjD,CAtMa,EA0Mb;MACE1E,GAAG,EAAE,eADP;MAEEG,IAAI,EACFxV,UAAU,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0X,OAAV,MAAiB,IAAjB,IAAiBsC,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,UAApB,CAAV,KACA,oBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvC,OAAV,MAAiB,IAAjB,IAAiBwC,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,cAAnB,MAAiC,IAAjC,IAAiCC,aAAjC,GAAiC,MAAjC,GAAiCA,GAAE9K,WADnC,KAEA;IALJ,CA1Ma,EAiNb;MACE+F,GAAG,EAAE,cADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiB2C,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,SAAnB,KAAgC;IAFxC,CAjNa,EAqNb;MACEjF,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAE,2BAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiB6C,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEC,aAAnB,MAAgC,IAAhC,IAAgCC,aAAhC,GAAgC,MAAhC,GAAgCA,GAAG,CAAH,CAAhC,MAAqC,IAArC,IAAqCC,aAArC,GAAqC,MAArC,GAAqCA,GAAEpL,WAAvC,KAAsD;IAF9D,CArNa,EAyNb;MACE+F,GAAG,EAAE,mBADP;MAEEG,IAAI,EACF,8BAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBiD,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEH,aAAnB,MAAgC,IAAhC,IAAgCI,cAAhC,GAAgC,MAAhC,GAAgCA,IAC5BjV,GAD4B,CACvBoI,IAAD,IAAc;QAAA;;QAAC,iBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE8M,SAAN,MAAe,IAAf,IAAe5K,aAAf,GAAe,MAAf,GAAeA,GAAEX,WAAjB;MAA4B,CADnB,CAAhC,MACoD,IADpD,IACoDwL,cADpD,GACoD,MADpD,GACoDA,IAChD/D,IADgD,CAC3C,IAD2C,CADpD,KAEkB;IALtB,CAzNa,EAgOb;MACE1B,GAAG,EAAE,CAAC,WAAD,EAAc,QAAd,CADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBqD,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,IAAnB,IACF,eAAC,IAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEtD,OAAV,MAAiB,IAAjB,IAAiBuD,cAAjB,GAAiB,MAAjB,GAAiBA,IAAED,IAAtB,CAAD,OAA4B,IAA5B,IAA4BE,cAA5B,GAA4B,MAA5B,GAA4BA,IAC1BvV,GAD0B,CACrBwV,GAAD,IAAcC,mBAAmB,CAACD,GAAD,CADX,CAA5B,MAC6C,IAD7C,IAC6CE,cAD7C,GAC6C,MAD7C,GAC6CA,IAC3CtE,IAD2C,CACtC,IADsC,CAF3C,GAIF;IANN,CAhOa;IAwOb;;;;;;;;;;IAUA;MACE1B,GAAG,EAAE,CAAC,WAAD,EAAc,YAAd,CADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiB4D,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,QAAnB,IACF,eAAC,IAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7D,OAAV,MAAiB,IAAjB,IAAiB8D,cAAjB,GAAiB,MAAjB,GAAiBA,IAAED,QAAtB,CAAD,OAAgC,IAAhC,IAAgCE,cAAhC,GAAgC,MAAhC,GAAgCA,IAC9B9V,GAD8B,CACzB0E,IAAD,IAAe5K,OAAO,CAAC4K,IAAD,CADI,CAAhC,MACmC,IADnC,IACmCqR,cADnC,GACmC,MADnC,GACmCA,IACjC3E,IADiC,CAC5B,IAD4B,CAFjC,GAIF;IANN,CAlPa,EA0Pb;MACE1B,GAAG,EAAE,QADP;MAEEG,IAAI,EAAE,uBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBiE,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,IAAnB,MAAuB,IAAvB,IAAuBC,cAAvB,GAAuB,MAAvB,GAAuBA,IAAEza,MAAzB,IACF,eAAC,IAAG,eAAQ,CAACsW,OAAT,MAAgB,IAAhB,IAAgBoE,cAAhB,GAAgB,MAAhB,GAAgBA,IAAEF,IAArB,CAAD,OAA2B,IAA3B,IAA2BG,cAA3B,GAA2B,MAA3B,GAA2BA,IACzBpW,GADyB,CACpBqW,GAAD,IAAeA,GAAG,KAAK,CAAR,IAAaA,GAAG,KAAK,GAArB,GAA2B,QAA3B,GAAsCA,GADhC,CAA3B,MACgE,IADhE,IACgEC,cADhE,GACgE,MADhE,GACgEA,IAC9DlF,IAD8D,CACzD,IADyD,CAF9D,GAIF;IANN,CA1Pa,EAkQb;MACE1B,GAAG,EAAE,SADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBwE,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,KAAnB,IACF,eAAC,IAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEzE,OAAV,MAAiB,IAAjB,IAAiB0E,cAAjB,GAAiB,MAAjB,GAAiBA,IAAED,KAAtB,CAAD,OAA6B,IAA7B,IAA6BE,cAA7B,GAA6B,MAA7B,GAA6BA,IAAE1W,GAAF,CAAO2W,IAAD,IAAeA,IAArB,CAA7B,MAAuD,IAAvD,IAAuDC,cAAvD,GAAuD,MAAvD,GAAuDA,IAAExF,IAAF,CAAO,IAAP,CADrD,GAEF;IAJN,CAlQa,EAwQb;MACE1B,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE1V,aAAa,CAAC,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4X,OAAV,MAAiB,IAAjB,IAAiB8E,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,SAApB,CAAb,IAA+C;IAFvD,CAxQa,EA4Qb;MACEpH,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBgF,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,MAAnB,IACF,eAAC,IAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjF,OAAV,MAAiB,IAAjB,IAAiBkF,cAAjB,GAAiB,MAAjB,GAAiBA,IAAED,MAAtB,CAAD,OAA8B,IAA9B,IAA8BE,cAA9B,GAA8B,MAA9B,GAA8BA,IAC5BlX,GAD4B,CACvBmC,KAAD,IAAgBA,KADQ,CAA9B,MAC4B,IAD5B,IAC4BgV,cAD5B,GAC4B,MAD5B,GAC4BA,IAC1B/F,IAD0B,CACrB,IADqB,CAF1B,GAIF;IANN,CA5Qa,EAoRb;MACE1B,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAEtV,SAAS,CAAC,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwX,OAAV,MAAiB,IAAjB,IAAiBqF,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,SAApB,CAAT,IAA2C;IAFnD,CApRa,EAwRb;MACE3H,GAAG,EAAE,eADP;MAEEG,IAAI,EACF,CAAC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBuF,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,UAAnB,MAA6B,IAA7B,IAA6BC,cAA7B,GAA6BA,GAA7B,GAAiC,EAAlC,IACA,GADA,IAEC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEzF,OAAV,MAAiB,IAAjB,IAAiB0F,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,cAAnB,MAAiC,IAAjC,IAAiCC,cAAjC,GAAiCA,GAAjC,GAAqC,EAFtC;IAHJ,CAxRa,EA+Rb;MACEjI,GAAG,EAAE,iBADP;MAEEG,IAAI,EACF,CAAC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiB6F,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,YAAnB,MAA+B,IAA/B,IAA+BC,cAA/B,GAA+BA,GAA/B,GAAmC,EAApC,IACA,GADA,IAEC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/F,OAAV,MAAiB,IAAjB,IAAiBgG,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,gBAAnB,MAAmC,IAAnC,IAAmCC,cAAnC,GAAmCA,GAAnC,GAAuC,EAFxC;IAHJ,CA/Ra,EAsSb;MACEvI,GAAG,EAAE,gBADP;MAEEG,IAAI,EACF,CAAC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBmG,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,WAAnB,MAA8B,IAA9B,IAA8BC,cAA9B,GAA8BA,GAA9B,GAAkC,EAAnC,IACA,GADA,IAEC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAErG,OAAV,MAAiB,IAAjB,IAAiBsG,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,eAAnB,MAAkC,IAAlC,IAAkCC,cAAlC,GAAkCA,GAAlC,GAAsC,EAFvC;IAHJ,CAtSa,EA6Sb;MACE7I,GAAG,EAAE,iBADP;MAEEG,IAAI,EACF,CAAC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiByG,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,YAAnB,MAA+B,IAA/B,IAA+BC,cAA/B,GAA+BA,GAA/B,GAAmC,EAApC,IACA,GADA,IAEC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE3G,OAAV,MAAiB,IAAjB,IAAiB4G,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,gBAAnB,MAAmC,IAAnC,IAAmCC,cAAnC,GAAmCA,GAAnC,GAAuC,EAFxC;IAHJ,CA7Sa,EAoTb;MACEnJ,GAAG,EAAE,YADP;MAEEG,IAAI,EACF,CAAC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiB+G,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,OAAnB,MAA0B,IAA1B,IAA0BC,cAA1B,GAA0BA,GAA1B,GAA8B,EAA/B,IACA,GADA,IAEC,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjH,OAAV,MAAiB,IAAjB,IAAiBkH,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,WAAnB,MAA8B,IAA9B,IAA8BC,cAA9B,GAA8BA,GAA9B,GAAkC,EAFnC;IAHJ,CApTa,EA2Tb;MACEzJ,GAAG,EAAE,uBADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBqH,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,QAAnB,KAA+B;IAFvC,CA3Ta,EA+Tb;MACE3J,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBuH,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,WAAnB,KAAkC;IAF1C,CA/Ta,EAmUb;MACE7J,GAAG,EAAE,WADP;MAEEG,IAAI,EAAElV,WAAW,CAAC,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoX,OAAV,MAAiB,IAAjB,IAAiByH,cAAjB,GAAiB,MAAjB,GAAiBA,IAAEC,OAApB,CAAX,GACF9e,WAAW,CAAC,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoX,OAAV,MAAiB,IAAjB,IAAiB2H,cAAjB,GAAiB,MAAjB,GAAiBA,IAAED,OAApB,CADT,GAEF;IAJN,CAnUa,EAyUb;MACE/J,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8J,WAAV,KAAyB;IAFjC,CAzUa,EA6Ub;MACEjK,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;;;QACV,MAAM+J,cAAc,GAClB,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEle,QAAR,CAAiB,MAAjB,KAA2B,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqW,OAAV,MAAiB,IAAjB,IAAiBzH,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEuP,aAA9C,GAA8D7J,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6J,aAD1E;QAEA,MAAMC,cAAc,GAClB,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEpe,QAAR,CAAiB,MAAjB,KAA2BsU,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+J,aAArC,GAAqD,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEhI,OAAV,MAAiB,IAAjB,IAAiBzF,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEwN,cAD1E;;QAEA,IAAIF,cAAc,IAAIA,cAAc,GAAG,CAAnC,IAAwCpf,cAAc,CAACof,cAAD,CAA1D,EAA4E;UAC1E,IAAIA,cAAc,KAAKpf,cAAc,CAAC,aAAD,CAAjC,IAAoDsf,cAAxD,EAAwE;YACtE,OAAO1c,eAAe,CACpB0c,cADoB,EAEpB,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjJ,YAAV,MAAsB,IAAtB,IAAsBC,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvS,aAFJ,EAGpB,cAHoB,CAAtB;UAKD;;UACD,OAAO/D,cAAc,CAACof,cAAD,CAArB;QACD;;QACD,OAAO,EAAP;MACD,CAhBK;IAFR,CA7Ua,EAiWb;MACElK,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkC,OAAV,MAAiB,IAAjB,IAAiBiI,cAAjB,GAAiB,MAAjB,GAAiBA,IAAE1M,QAAnB,KAA+B;IAFvC,CAjWa,EAqWb;MACEoC,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoK,QAAV,IACFjK,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiK,QAAV,CAAmBja,GAAnB,CAAwBoI,IAAD,IAAeA,IAAI,CAACsI,IAA3C,EAAiDU,IAAjD,CAAsD,IAAtD,CADE,GAEF;IAJN,CArWa,EA2Wb;MACE1B,GAAG,EAAE,aADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqK,SAAV,IACFlK,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkK,SAAV,CAAoBla,GAApB,CAAyBoI,IAAD,IAAeA,IAAI,CAACsI,IAA5C,EAAkDU,IAAlD,CAAuD,IAAvD,CADE,GAEF;IAJN,CA3Wa,EAiXb;MACE1B,GAAG,EAAE,sBADP;MAEEG,IAAI,EACF,oCAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE5H,MAAV,MAAgB,IAAhB,IAAgBkS,cAAhB,GAAgB,MAAhB,GAAgBA,IAAEjF,SAAlB,MAA2B,IAA3B,IAA2BkF,cAA3B,GAA2B,MAA3B,GAA2BA,IAAEzQ,WAA7B,MAAwC,IAAxC,IAAwC0Q,cAAxC,GAAwCA,GAAxC,GACA,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEpS,MAAV,MAAgB,IAAhB,IAAgBqS,cAAhB,GAAgB,MAAhB,GAAgBA,IAAE3Q,WADlB,MAC6B,IAD7B,IAC6B4Q,cAD7B,GAC6BA,GAD7B,GAEA;IALJ,CAjXa,EAwXb;MACE7K,GAAG,EAAE,UADP;MAEEG,IAAI,EAAEzV,MAAM,CAAC4V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwK,MAAX,CAAN,IAA4B;IAFpC,CAxXa,EA4Xb;MACE9K,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAEvV,iBAAiB,CAAC0V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyK,aAAX,CAAjB,IAA8C;IAFtD,CA5Xa,EAgYb;MACE/K,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6K,WAAV,IAAwBtd,eAAe,CAAC4S,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0K,WAAX,EAAwB,UAAxB,EAAoC,cAApC,CAAvC,GAA6F;IAFrG,CAhYa,CAAf;IAqYA,OAAOpL,mBAAmB,CAACC,GAAD,EAAMN,YAAN,CAA1B;EACD,CAhaD;;EAkaA,IAAIO,KAAK,CAACC,OAAN,CAAclB,QAAd,CAAJ,EAA6B;IAC3BA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE1I,OAAV,CAAkB,CAACmK,QAAD,EAAgBC,KAAhB,KAAiC;MACjD,MAAMV,GAAG,GAAGQ,eAAe,CAACC,QAAD,EAAWC,KAAX,CAA3B;MACAlB,WAAW,IAAIkB,KAAK,GAAG,CAAR,GAAY,IAAZ,GAAmBV,GAAlC;;MACA,IAAIU,KAAK,GAAG1B,QAAQ,CAAC9S,MAAT,GAAkB,CAA9B,EAAiC;QAC/BsT,WAAW,IAAI,IAAf;MACD;IACF,CAND;EAOD,CARD,MAQO;IACLA,WAAW,GAAGgB,eAAe,CAACxB,QAAD,EAAW,CAAX,CAA7B;EACD;;EACD,IAAIG,MAAJ,EAAY;IACVA,MAAM,GAAGY,mBAAmB,CAACZ,MAAD,EAASO,YAAT,CAA5B;EACD;;EAED,IAAIN,MAAJ,EAAY;IACVA,MAAM,GAAGW,mBAAmB,CAACX,MAAD,EAASM,YAAT,CAA5B;EACD;;EAED,OAAOF,WAAW,GACd,CAACL,MAAM,GAAG,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3O,OAAR,CAAgB,KAAhB,EAAuB,KAAvB,KAAgC,KAAnC,GAA2C,EAAlD,KACFgP,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEhP,OAAb,CAAqB,KAArB,EAA4B,KAA5B,CADE,KAED4O,MAAM,GAAG,SAAQA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE5O,OAAR,CAAgB,KAAhB,EAAuB,KAAvB,CAAR,CAAH,GAA2C,EAFhD,CADc,GAId,EAJJ;AAKD,CA/fM;AAigBP,OAAO,MAAM4a,mBAAmB,GAAG,CACjCrM,QADiC,EAEjCC,QAFiC,EAGjCC,UAHiC,EAIjCC,eAJiC,EAKjCmM,WALiC,EAMjCC,KANiC,EAOjCjM,WAPiC,EAQjCC,QARiC,EASjCrM,WATiC,KAU/B;EACF,IAAIuM,WAAW,GAAG,EAAlB;EAEA,IAAIE,YAAY,GAAQ,EAAxB;;EACA,MAAMK,mBAAmB,GAAG,CAACC,GAAD,EAAWN,YAAX,KAAgC;IAC1D,IAAI,OAAOM,GAAP,KAAe,QAAnB,EAA6B;MAC3BN,YAAY,CAACpJ,OAAb,CAAsBuC,IAAD,IAAc;;;QACjC,IAAIA,IAAI,CAACsH,GAAL,KAAa,kBAAjB,EAAqC;UACnCH,GAAG,GAAGA,GAAG,CAACxP,OAAJ,CACJ,kBADI,EAEJ,WAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE8P,IAAN,MAAU,IAAV,IAAUvF,aAAV,GAAU,MAAV,GAAUA,GAAEtK,GAAF,CAAO8a,CAAD,IAAe,IAAIA,CAAC,GAA1B,EAA+B1J,IAA/B,CAAoC,GAApC,CAAV,KAAsD,EAFlD,CAAN;QAID,CALD,MAKO;UACL,MAAM2J,KAAK,GAAG,IAAIC,MAAJ,CAAW5S,IAAI,CAACsH,GAAhB,EAAqB,IAArB,CAAd;UACAH,GAAG,GAAGA,GAAG,CAACK,UAAJ,CAAemL,KAAf,EAAsB,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAElL,IAAN,MAAU,IAAV,IAAUvD,aAAV,GAAU,MAAV,GAAUA,GAAEwD,IAAF,EAAhC,CAAN;QACD;MACF,CAVD;IAWD;;IACD,OAAOP,GAAP;EACD,CAfD;;EAiBA,MAAMQ,eAAe,GAAG,CAACC,QAAD,EAAgBC,KAAhB,KAAiC;IACvD,IAAIV,GAAG,GAAGqL,WAAV;IACA,IAAIlM,MAAJ,EAAiBuM,IAAjB;;IACA,IAAI3M,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4M,YAAd,EAA4B;MAC1BxM,MAAM,GAAG7H,MAAM,CAACsU,MAAP,CAAc7M,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4M,YAAxB,CAAT;MACAxM,MAAM,GAAGA,MAAM,CAAC0C,IAAP,CAAY,IAAZ,CAAT;IACD;;IACD,IAAI9C,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8M,UAAd,EAA0B;MACxBH,IAAI,GAAGpU,MAAM,CAACsU,MAAP,CAAc7M,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8M,UAAxB,EAAoCpb,GAApC,CAAyCoI,IAAD,IAC7CiG,eAAe,CACbjG,IAAI,IAAI,SADK,EAEbmG,QAFa,EAGbC,UAHa,EAIbC,eAJa,EAKb,IALa,EAMb,IANa,EAObG,WAPa,EAQbC,QARa,EASbrM,WATa,CADV,CAAP;IAaD;;IACDyM,YAAY,GAAG,CACb;MACES,GAAG,EAAE,qBADP;MAEEG,IAAI,EAAEG,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqD;IAFlB,CADa,EAKb;IACA;IACA;IACA;IACA;MACE3D,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAExB,eAAe,CACnBK,MAAM,IAAI,SADS,EAEnBH,QAFmB,EAGnBC,UAHmB,EAInBC,eAJmB,EAKnB,IALmB,EAMnB,IANmB,EAOnBG,WAPmB,EAQnBC,QARmB,EASnBrM,WATmB;IAFvB,CATa,EAuBb;MACEkN,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAEoL;IAFR,CAvBa,EA2Bb;MACEvL,GAAG,EAAE,WADP;MAEEG,IAAI,EACFxB,eAAe,CACbC,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+M,OADG,EAEb9M,QAFa,EAGbC,UAHa,EAIbC,eAJa,EAKbH,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEI,MALG,EAMbJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEK,MANG,EAObC,WAPa,EAQbC,QARa,EASbrM,WATa,CAAf,IAUK;IAbT,CA3Ba,EA0Cb;MAAEkN,GAAG,EAAE,gBAAP;MAAyBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiC,KAAV,KAAmB;IAAlD,CA1Ca,EA2Cb;MAAEpC,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAEvB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEhG;IAAvC,CA3Ca,EA4Cb;MAAEoH,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAE,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEyL,QAAP,KAAmB;IAA9C,CA5Ca,EA6Cb;MAAE5L,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAE,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE0L,QAAP,KAAmB;IAA9C,CA7Ca,EA8Cb;MAAE7L,GAAG,EAAE,eAAP;MAAwBG,IAAI,EAAE,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE2L,SAAP,KAAoB;IAAlD,CA9Ca,EA+Cb;MAAE9L,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAEtB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmC;IAArC,CA/Ca,CAAf;IAkDA,OAAOpB,mBAAmB,CAACC,GAAD,EAAMN,YAAN,CAA1B;EACD,CAzED;;EA2EA,IAAIO,KAAK,CAACC,OAAN,CAAclB,QAAd,CAAJ,EAA6B;IAC3BA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE1I,OAAV,CAAkB,CAACmK,QAAD,EAAgBC,KAAhB,KAAiC;MACjD,MAAMV,GAAG,GAAGQ,eAAe,CAACC,QAAD,EAAWC,KAAX,CAA3B;MACAlB,WAAW,IAAIkB,KAAK,GAAG,CAAR,GAAY,IAAZ,GAAmBV,GAAlC;;MACA,IAAIU,KAAK,GAAG1B,QAAQ,CAAC9S,MAAT,GAAkB,CAA9B,EAAiC;QAC/BsT,WAAW,IAAI,IAAf;MACD;IACF,CAND;EAOD,CARD,MAQO;IACLA,WAAW,GAAGgB,eAAe,CAACxB,QAAD,EAAW,CAAX,CAA7B;EACD;;EAED,OAAOQ,WAAW,GAAGA,WAAH,GAAiB,EAAnC;AACD,CAvHM;AAyHP,OAAO,MAAM0M,mBAAmB,GAAG,CACjCnN,QADiC,EAEjCoN,YAFiC,EAGjCC,aAHiC,EAIjCnN,UAJiC,EAKjCE,MALiC,EAMjCC,MANiC,EAOjCiN,mBAPiC,EAQjCnN,eARiC,EASjCI,QATiC,EAUjCrM,WAViC,KAW/B;EACF,IAAIuM,WAAW,GAAG,EAAlB;EACA,MAAMC,qBAAqB,GAAQ,EAAnC;EACA,IAAIC,YAAY,GAAQ,EAAxB;EAEAX,QAAQ,GAAGA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvO,OAAV,CAAkB,eAAlB,EAAoCmP,KAAD,IAAkB;;;IAC9D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATU,CAAX;EAUA2O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;EAUA4O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE5O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;;EAWA,MAAMqP,YAAY,GAAIC,QAAD,IAAqB;IAAA;;IACxC,qBAAQ,CAACF,WAAT,QAAsB,IAAtB,IAAsB7E,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvK,OAAF,CAAU,SAAV,EAAqB,EAArB,CAAtB;EAA8C,CADhD;;EAGA,MAAMuP,mBAAmB,GAAG,CAACC,GAAD,EAAWN,YAAX,KAAgC;IAC1D,IAAIM,GAAJ,EAAS;MACPN,YAAY,CAACpJ,OAAb,CAAsBuC,IAAD,IAAc;;;QACjC,IAAIoH,KAAK,CAACC,OAAN,CAAcrH,IAAI,CAACsH,GAAnB,CAAJ,EAA6B;UAC3BtH,IAAI,CAACsH,GAAL,CAAS7J,OAAT,CAAkBwJ,QAAD,IAAqB;;;YACpC,MAAMM,aAAa,GAAGP,YAAY,CAACC,QAAD,CAAlC;YACAE,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+B,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEE,IAAN,MAAU,IAAV,IAAUvF,aAAV,GAAU,MAAV,GAAUA,GAAEwF,IAAF,EAAzC,CAAN;UACD,CAHD;QAID,CALD,MAKO;UACL,MAAMH,aAAa,GAAGP,YAAY,CAAChH,IAAI,CAACsH,GAAN,CAAlC;UACAH,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+B,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEE,IAAN,MAAU,IAAV,IAAUvF,aAAV,GAAU,MAAV,GAAUA,GAAEwF,IAAF,EAAzC,CAAN;QACD;MACF,CAVD;IAWD;;IACD,OAAOP,GAAP;EACD,CAfD;;EAiBA,MAAMsM,mBAAmB,GAAG,CAAC7L,QAAD,EAAgBC,KAAhB,KAAiC;;;IAC3D,IAAIV,GAAG,GAAGjB,QAAV;;IACA,IAAI,cAAQ,CAACiC,UAAT,MAAmB,IAAnB,IAAmBjG,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE7O,MAAzB,EAAiC;MAC/B,KAAK,MAAM+U,SAAX,IAAwBR,QAAQ,CAACO,UAAjC,EAA6C;QAC3C,IAAI,EAACC,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAX,CAAyB5Q,UAAzB,CAAoC,IAApC,CAAD,CAAJ,EAAgD;UAC9CmP,qBAAqB,CAACwB,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAZ,CAArB,GAAkDD,SAAS,CAACtT,KAA5D;QACD;MACF;IACF;;IAED,IAAI4e,aAAa,GAAG,EAApB;IAEA,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,KAAV,MAAe,IAAf,IAAezP,aAAf,GAAe,MAAf,GAAeA,GAAEtM,GAAF,CAAOgc,IAAD,IAAc;MACjCF,aAAa,IAAIE,IAAI,GAAG,IAAxB;IACD,CAFc,CAAf;IAGAF,aAAa,GAAGA,aAAa,CAAChf,KAAd,CAAoB,CAApB,EAAuB,CAAC,CAAxB,CAAhB;IAEAmS,YAAY,GAAG,CACb;MACES,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE5H,MAAV,KAAoB,CAApB,GAAwB,MAAxB,GAAiCvN,cAAc,CAACsV,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/H,MAAX;IAFvD,CADa,EAKb;MACEyH,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoM,WAAV,IAAwBhiB,WAAW,CAAC+V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiM,WAAX,CAAnC,GAA6D;IAFrE,CALa,EASb;MACEvM,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAE,eAAQ,CAAC3F,YAAT,MAAqB,IAArB,IAAqB4G,aAArB,GAAqB,MAArB,GAAqBA,GAAEnH,WAAvB,IACF,cAAQ,CAACO,YAAT,MAAqB,IAArB,IAAqB8G,aAArB,GAAqB,MAArB,GAAqBA,GAAErH,WADrB,GAEF;IAJN,CATa,EAeb;MACE+F,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAE,qBAAQ,CAAC3F,YAAT,MAAqB,IAArB,IAAqB+G,aAArB,GAAqB,MAArB,GAAqBA,GAAEiE,SAAvB,MAAgC,IAAhC,IAAgC5D,aAAhC,GAAgC,MAAhC,GAAgCA,GAAE3H,WAAlC,IACF,oBAAQ,CAACO,YAAT,MAAqB,IAArB,IAAqBqH,aAArB,GAAqB,MAArB,GAAqBA,GAAE2D,SAAvB,MAAgC,IAAhC,IAAgCzD,aAAhC,GAAgC,MAAhC,GAAgCA,GAAE9H,WADhC,GAEF;IAJN,CAfa,EAqBb;MACE+F,GAAG,EAAE,WADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqM,OAAV,IAAoBzG,mBAAmB,CAACzF,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkM,OAAX,CAAvC,GAA6D;IAFrE,CArBa,EAyBb;MACExM,GAAG,EAAE,UADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqM,OAAV,IAAoBC,kBAAkB,CAACnM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkM,OAAX,CAAtC,GAA4D;IAFpE,CAzBa,EA6Bb;MACExM,GAAG,EAAE,WADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7F,OAAV,IAAoBlQ,OAAO,CAACkW,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEhG,OAAX,CAA3B,GAAiD;IAFzD,CA7Ba,EAiCb;MACE0F,GAAG,EAAE,UADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7F,OAAV,IAAoBlQ,OAAO,CAACkW,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEhG,OAAX,CAA3B,GAAiD;IAFzD,CAjCa,EAqCb;MAAE0F,GAAG,EAAE,SAAP;MAAkBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiC,KAAV,IAAkB9B,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8B,KAA5B,GAAoC;IAA5D,CArCa,EAsCb;MACEpC,GAAG,EAAE,CAAC,gBAAD,EAAmB,gBAAnB,CADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmB1K,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE2K,IAArB,IAA4B,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAED,SAAV,MAAmB,IAAnB,IAAmBzK,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE0K,IAAjD,GAAwD,EAAE,IAAI,eAAQ,CAACD,SAAT,MAAkB,IAAlB,IAAkBxK,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE0K,UAApB,IACnE,iBAAW,CAAC,cAAQ,CAACF,SAAT,MAAkB,IAAlB,IAAkBvK,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEyK,UAArB,EAAiCX,aAAjC,CAAX,MAA0D,IAA1D,IAA0D3J,aAA1D,GAA0D,MAA1D,GAA0DA,GAAEuK,IADO,GAEnE,EACF;IALJ,CAtCa,EA6Cb;MACE7M,GAAG,EAAE,cADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmBlK,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEqF,UAArB,IAAkC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6E,SAAV,MAAmB,IAAnB,IAAmBjK,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEoF,UAAvD,GAAoE,EAC3E,IAAI,eAAQ,CAAC6E,SAAT,MAAkB,IAAlB,IAAkBhK,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEoK,YAApB,IACA,iBAAW,CAAC,cAAQ,CAACJ,SAAT,MAAkB,IAAlB,IAAkB/J,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEmK,YAArB,EAAmCb,aAAnC,CAAX,MAA4D,IAA5D,IAA4DpJ,aAA5D,GAA4D,MAA5D,GAA4DA,GAAEgK,IAD9D,GAEA,EACJ;IANJ,CA7Ca,EAqDb;MACE7M,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmB5J,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEiK,WAArB,IACL,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEL,SAAV,MAAmB,IAAnB,IAAmB3J,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEgK,WADhB,GAEL,EACF,IAAI,eAAQ,CAACL,SAAT,MAAkB,IAAlB,IAAkB1J,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEgK,aAApB,IACA,iBAAW,CAAC,cAAQ,CAACN,SAAT,MAAkB,IAAlB,IAAkBxJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE8J,aAArB,EAAoCf,aAApC,CAAX,MAA6D,IAA7D,IAA6D9I,aAA7D,GAA6D,MAA7D,GAA6DA,GAC3D0J,IAFF,GAGA,EACJ;IATJ,CArDa,EAgEb;MACE7M,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmBtJ,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE+E,YAArB,IACL,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuE,SAAV,MAAmB,IAAnB,IAAmBrJ,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE8E,YADhB,GAEL,EACF,IAAI,eAAQ,CAACuE,SAAT,MAAkB,IAAlB,IAAkBnJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE0J,cAApB,IACA,iBAAW,CAAC,cAAQ,CAACP,SAAT,MAAkB,IAAlB,IAAkBjI,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEwI,cAArB,EAAqChB,aAArC,CAAX,MAA8D,IAA9D,IAA8DtH,aAA9D,GAA8D,MAA9D,GAA8DA,GAC5DkI,IAFF,GAGA,EACJ;IATJ,CAhEa,EA2Eb;MACE7M,GAAG,EAAE,WADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmB7H,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEwE,OAArB,IAA+B,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqD,SAAV,MAAmB,IAAnB,IAAmB3H,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEsE,OAApD,GAA8D,EACrE,IAAI,eAAQ,CAACqD,SAAT,MAAkB,IAAlB,IAAkB1H,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEkI,aAApB,IACA,iBAAW,CAAC,cAAQ,CAACR,SAAT,MAAkB,IAAlB,IAAkBxH,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEgI,aAArB,EAAoCjB,aAApC,CAAX,MAA6D,IAA7D,IAA6D7G,aAA7D,GAA6D,MAA7D,GAA6DA,GAC3DyH,IAFF,GAGA,EACJ;IAPJ,CA3Ea,EAoFb;MACE7M,GAAG,EAAE,aADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuM,SAAV,MAAmB,IAAnB,IAAmBrH,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEtZ,MAArB,IAA8B,QAAO,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2gB,SAAV,MAAmB,IAAnB,IAAmBpH,cAAnB,GAAmB,MAAnB,GAAmBA,IAAEvZ,MAA5B,CAA9B,GAAmE,EAC1E,IAAI,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2gB,SAAV,MAAmB,IAAnB,IAAmBnH,cAAnB,GAAmB,MAAnB,GAAmBA,IAAE4H,OAArB,MAAgC,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAET,SAAV,MAAmB,IAAnB,IAAmBjH,cAAnB,GAAmB,MAAnB,GAAmBA,IAAE1Z,MAArD,IAA8D,GAA9D,GAAoE,EACxE,IAAI,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2gB,SAAV,MAAmB,IAAnB,IAAmBhH,cAAnB,GAAmB,MAAnB,GAAmBA,IAAEyH,OAArB,IACA,QAAO,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAET,SAAV,MAAmB,IAAnB,IAAmB9G,cAAnB,GAAmB,MAAnB,GAAmBA,IAAEuH,OAA5B,CADA,GAEA,EACJ;IAPJ,CApFa,EA6Fb;MACEnN,GAAG,EAAE,YADP;MAEEG,IAAI,EAAEG,QAAQ,CAACoE,QAAT,GAAoBxZ,QAAQ,CAACoV,QAAQ,CAACoE,QAAV,CAA5B,GAAkD;IAF1D,CA7Fa,EAiGb;MACE1E,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;;;QACV,MAAM+J,cAAc,GAAG5J,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6J,aAAjC;QACA,MAAMC,cAAc,GAAG9J,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8J,cAAjC;;QACA,IAAIF,cAAc,IAAIA,cAAc,GAAG,CAAnC,IAAwCpf,cAAc,CAACof,cAAD,CAA1D,EAA4E;UAC1E,IAAIA,cAAc,KAAKpf,cAAc,CAAC,aAAD,CAAjC,IAAoDsf,cAAxD,EAAwE;YACtE,MAAMgD,aAAa,GAAG1f,eAAe,CAAC0c,cAAD,EAAiB,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjJ,YAAV,MAAsB,IAAtB,IAAsBvG,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE/L,aAAzC,EAAwD,cAAxD,CAAf,IAA0FnB,eAAe,CAACoF,WAAD,EAAc,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqO,YAAV,MAAsB,IAAtB,IAAsBvE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE/N,aAAtC,EAAqD,cAArD,CAA/H;YACA,OAAOue,aAAa,GAChB,eADgB,GAEhB1f,eAAe,CACf0c,cADe,EAEf,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjJ,YAAV,MAAsB,IAAtB,IAAsBC,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvS,aAFT,EAGf,cAHe,CAFnB;UAOD;;UACD,OAAO/D,cAAc,CAACof,cAAD,CAArB;QACD;;QACD,OAAO,EAAP;MACD,CAjBK;IAFR,CAjGa,EAsHb;MACElK,GAAG,EAAE,cADP;MAEEG,IAAI,EAAE,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkN,YAAV,MAAsB,IAAtB,IAAsBxH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEyH,aAAxB,IACF,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAED,YAAV,MAAsB,IAAtB,IAAsBrH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEsH,aAAxB,IACF,IADE,GAEF5P,YAAY,CACV,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2P,YAAV,MAAsB,IAAtB,IAAsBpH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEqH,aADd,EAEV,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAED,YAAV,MAAsB,IAAtB,IAAsBlH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEvI,QAAxB,KAAoCmB,eAF1B,CAFV,GAMF,GAPI,GAQF;IAVN,CAtHa,EAkIb;MACEiB,GAAG,EAAE,aADP;MAEEG,IAAI,EAAE,GAAG,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkN,YAAV,MAAsB,IAAtB,IAAsBjH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEmH,SAAxB,IACL,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,YAAV,MAAsB,IAAtB,IAAsBhH,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEkH,SADnB,GAEL,EACF,IAAI,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,YAAV,MAAsB,IAAtB,IAAsB/G,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEkH,iBAAxB,IACA,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEH,YAAV,MAAsB,IAAtB,IAAsB7G,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEgH,iBADxB,GAEA,EACJ;IARJ,CAlIa,EA4Ib;MAAExN,GAAG,EAAE,SAAP;MAAkBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsN,KAAV,IAAkBnN,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmN,KAA5B,GAAoC;IAA5D,CA5Ia,EA6Ib;MACEzN,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuN,aAAV,IAA0BpN,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoN,aAApC,GAAoD;IAF5D,CA7Ia,EAiJb;MACE1N,GAAG,EAAE,WADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqD,OAAV,IACFC,uBAAuB,CAACnD,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkD,OAAX,CADrB,GAEF;IAJN,CAjJa,EAuJb;MACExD,GAAG,EAAE,aADP;MAEEG,IAAI,EACF,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwN,oBAAV,MAA8B,IAA9B,IAA8BlH,cAA9B,GAA8B,MAA9B,GAA8BA,IAAE1a,MAAhC,KAA0CmgB,mBAA1C,GACI,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyB,oBAAV,MAA8B,IAA9B,IAA8BjH,cAA9B,GAA8B,MAA9B,GAA8BA,IAAEjO,MAAF,CAAUmV,KAAD,IAAe;QAAA;;QAAC,kBAAK,CAAC5M,IAAN,MAAU,IAAV,IAAUpG,aAAV,GAAU,MAAV,GAAUA,GAAEwF,IAAF,EAAV;MAAkB,CAA3C,CAA9B,MAA0E,IAA1E,IAA0EwG,cAA1E,GAA0E,MAA1E,GAA0EA,IAAEtW,GAAF,CAAOsd,KAAD,IAAgBA,KAAK,CAAC5M,IAA5B,EAAkCU,IAAlC,CAAuC,IAAvC,CAD9E,GAEI;IALR,CAvJa,EA8Jb;MACE1B,GAAG,EAAE,cADP;MAEEG,IAAI,EACF,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwN,oBAAV,MAA8B,IAA9B,IAA8B9G,cAA9B,GAA8B,MAA9B,GAA8BA,IAAE9a,MAAhC,KAA0CmgB,mBAA1C,GACI,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyB,oBAAV,MAA8B,IAA9B,IAA8B5G,cAA9B,GAA8B,MAA9B,GAA8BA,IAAEtO,MAAF,CAAUmV,KAAD,IAAe;QAAA;;QAAC,kBAAK,CAACC,KAAN,MAAW,IAAX,IAAWjT,aAAX,GAAW,MAAX,GAAWA,GAAEwF,IAAF,EAAX;MAAmB,CAA5C,EAA8C9P,GAA9C,CAAmDsd,KAAD,IAAgBA,KAAK,CAACC,KAAxE,EAA+EnM,IAA/E,CAAoF,IAApF,CADlC,GAEI;IALR,CA9Ja,EAqKb;MACE1B,GAAG,EAAE,cADP;MAEEG,IAAI,EACF,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwN,oBAAV,MAA8B,IAA9B,IAA8B3G,cAA9B,GAA8B,MAA9B,GAA8BA,IAAEjb,MAAhC,KAA0CmgB,mBAA1C,GACI,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyB,oBAAV,MAA8B,IAA9B,IAA8BzG,cAA9B,GAA8B,MAA9B,GAA8BA,IAAEzO,MAAF,CAAUmV,KAAD,IAAe;QAAA;;QAAC,kBAAK,CAAC3M,KAAN,MAAW,IAAX,IAAWrG,aAAX,GAAW,MAAX,GAAWA,GAAEwF,IAAF,EAAX;MAAmB,CAA5C,CAA9B,MAA2E,IAA3E,IAA2E+G,cAA3E,GAA2E,MAA3E,GAA2EA,IAAE7W,GAAF,CAAOsd,KAAD,IAAgBA,KAAK,CAAC3M,KAA5B,EAAmCS,IAAnC,CAAwC,IAAxC,CAD/E,GAEI;IALR,CArKa,EA4Kb;MACE1B,GAAG,EAAE,eADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,gBAAD,CAArB,GACFA,qBAAqB,CAAC,gBAAD,CADnB,GAEF;IAJN,CA5Ka,EAkLb;MACEU,GAAG,EAAE,eADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,aAAD,CAArB,GACFA,qBAAqB,CAAC,aAAD,CADnB,GAEF;IAJN,CAlLa,EAwLb;MACEU,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,mBAAD,CAArB,GACFA,qBAAqB,CAAC,mBAAD,CADnB,GAEF;IAJN,CAxLa,EA8Lb;MACEU,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,kBAAD,CAArB,GACFA,qBAAqB,CAAC,kBAAD,CADnB,GAEF;IAJN,CA9La,EAoMb;MACEU,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,kBAAD,CAArB,GACFA,qBAAqB,CAAC,kBAAD,CADnB,GAEF;IAJN,CApMa,EA0Mb;MACEU,GAAG,EAAE,mBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,qBAAD,CAArB,GACFA,qBAAqB,CAAC,qBAAD,CADnB,GAEF;IAJN,CA1Ma,EAgNb;MACEU,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,mBAAD,CAArB,GACFA,qBAAqB,CAAC,mBAAD,CADnB,GAEF;IAJN,CAhNa,EAsNb;MACEU,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,mBAAD,CAArB,GACFA,qBAAqB,CAAC,mBAAD,CADnB,GAEF;IAJN,CAtNa,EA4Nb;MACEU,GAAG,EAAE,eADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,iBAAD,CAArB,GACFA,qBAAqB,CAAC,iBAAD,CADnB,GAEF;IAJN,CA5Na,EAkOb;MACEU,GAAG,EAAE,iBADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2N,aAAV,IACFrjB,aAAa,CAAC6V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwN,aAAX,CADX,GAEF;IAJN,CAlOa,EAwOb;MACE9N,GAAG,EAAE,UADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4N,MAAV,IAAmBvjB,MAAM,CAAC8V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyN,MAAX,CAAzB,GAA8C;IAFtD,CAxOa,EA4Ob;MACE/N,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAEqB,QAAQ,CAAClB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmB,QAAX,EAAqB,KAArB,EAA4B,IAA5B;IAFhB,CA5Oa,EAgPb;MAAEzB,GAAG,EAAE,gBAAP;MAAyBG,IAAI,EAAEiM;IAA/B,CAhPa,EAiPb;MACEpM,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,GAAGhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEzC,SAAS,IAAIyC,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAExC,QAAQ;IAFpD,CAjPa,EAqPb;MAAEqD,GAAG,EAAE,aAAP;MAAsBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8B;IAAtC,CArPa,EAsPb;MAAEjB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+B;IAAvC,CAtPa,EAuPb;MAAElB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAErB,UAAU,IAAI;IAA3C,CAvPa,EAwPb;MAAEkB,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6N,QAAV,KAAsB;IAAjD,CAxPa,CAAf;IA0PA,OAAOpO,mBAAmB,CAACC,GAAD,EAAMN,YAAN,CAA1B;EACD,CA5QD;;EA8QA,IAAIO,KAAK,CAACC,OAAN,CAAciM,YAAd,CAAJ,EAAiC;IAC/BA,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE7V,OAAd,CAAsB,CAACmK,QAAD,EAAgBC,KAAhB,KAAiC;MACrD,MAAMV,GAAG,GAAGsM,mBAAmB,CAAC7L,QAAD,EAAWC,KAAX,CAA/B;MACAlB,WAAW,IAAIkB,KAAK,GAAG,CAAR,GAAY,IAAZ,GAAmBV,GAAlC;;MACA,IAAIU,KAAK,GAAGyL,YAAY,CAACjgB,MAAb,GAAsB,CAAlC,EAAqC;QACnCsT,WAAW,IAAI,IAAf;MACD;IACF,CAND;EAOD,CARD,MAQO;IACLA,WAAW,GAAG8M,mBAAmB,CAACH,YAAD,EAAe,CAAf,CAAjC;EACD;;EAED,IAAIhN,MAAJ,EAAY;IACVA,MAAM,GAAGY,mBAAmB,CAACZ,MAAD,EAASO,YAAT,CAA5B;EACD;;EAED,IAAIN,MAAJ,EAAY;IACVA,MAAM,GAAGW,mBAAmB,CAACX,MAAD,EAASM,YAAT,CAA5B;EACD;;EAED,OAAOF,WAAW,GACd,CAACL,MAAM,GAAGA,MAAM,GAAG,IAAZ,GAAmB,EAA1B,IACFK,WADE,IAEDJ,MAAM,GAAG,OAAOA,MAAV,GAAmB,EAFxB,CADc,GAId,EAJJ;AAKD,CA1WM;AA4WP,OAAO,MAAMgP,kBAAkB,GAAG,CAChCrP,QADgC,EAEhCsP,WAFgC,EAGhCjC,aAHgC,EAIhCnN,UAJgC,EAKhCE,MALgC,EAMhCC,MANgC,EAOhCkP,QAPgC,EAQhCjR,GARgC,EAShCiC,QATgC,EAUhCrM,WAVgC,KAW9B;EACF,IAAIuM,WAAW,GAAG,EAAlB;EACA,MAAMC,qBAAqB,GAAQ,EAAnC;EACA,IAAIC,YAAY,GAAQ,EAAxB;EAEAX,QAAQ,GAAGA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvO,OAAV,CAAkB,eAAlB,EAAoCmP,KAAD,IAAkB;;;IAC9D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATU,CAAX;EAUA2O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;EAUA4O,MAAM,GAAGA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE5O,OAAR,CAAgB,eAAhB,EAAkCmP,KAAD,IAAkB;;;IAC1D,OACE,OACA,WAAK,CACFpS,KADH,CACS,CADT,EACY,CAAC,CADb,EAEGqS,WAFH,QAEgB,IAFhB,IAEgB7E,aAFhB,GAEgB,MAFhB,GAEgBA,GACZvK,OADY,CACJ,SADI,EACO,EADP,CAHhB,IAKA,GANF;EAQD,CATQ,CAAT;;EAWA,MAAMqP,YAAY,GAAIC,QAAD,IAAqB;IAAA;;IACxC,qBAAQ,CAACF,WAAT,QAAsB,IAAtB,IAAsB7E,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvK,OAAF,CAAU,SAAV,EAAqB,EAArB,CAAtB;EAA8C,CADhD;;EAGA,MAAMuP,mBAAmB,GAAG,CAACC,GAAD,EAAWN,YAAX,KAAgC;IAC1D,IAAIM,GAAJ,EAAS;MACPN,YAAY,CAACpJ,OAAb,CAAsBuC,IAAD,IAAc;QACjC,IAAIoH,KAAK,CAACC,OAAN,CAAcrH,IAAI,CAACsH,GAAnB,CAAJ,EAA6B;UAC3BtH,IAAI,CAACsH,GAAL,CAAS7J,OAAT,CAAkBwJ,QAAD,IAAqB;YACpC,MAAMM,aAAa,GAAGP,YAAY,CAACC,QAAD,CAAlC;YACA,MAAMyO,eAAe,GACnB,QAAO1V,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEyH,IAAb,MAAsB,QAAtB,GAAiCzH,IAAI,CAACyH,IAAL,CAAUC,IAAV,EAAjC,GAAoD1H,IAAI,CAACyH,IAD3D;YAEAN,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+BmO,eAA/B,CAAN;UACD,CALD;QAMD,CAPD,MAOO;UACL,MAAMnO,aAAa,GAAGP,YAAY,CAAChH,IAAI,CAACsH,GAAN,CAAlC;UACA,MAAMoO,eAAe,GACnB,QAAO1V,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEyH,IAAb,MAAsB,QAAtB,GAAiCzH,IAAI,CAACyH,IAAL,CAAUC,IAAV,EAAjC,GAAoD1H,IAAI,CAACyH,IAD3D;UAEAN,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEK,UAAL,CAAgBD,aAAhB,EAA+BmO,eAA/B,CAAN;QACD;MACF,CAdD;IAeD;;IACD,OAAOvO,GAAP;EACD,CAnBD;;EAqBA,MAAMwO,kBAAkB,GAAG,CAAC/N,QAAD,EAAgBC,KAAhB,KAAiC;;;IAC1D,IAAIV,GAAG,GAAGjB,QAAV;;IAEA,IAAI,cAAQ,CAACiC,UAAT,MAAmB,IAAnB,IAAmBjG,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE7O,MAAzB,EAAiC;MAC/B,KAAK,MAAM+U,SAAX,IAAwBR,QAAQ,CAACO,UAAjC,EAA6C;QAC3C,IAAI,EAACC,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAX,CAAyB5Q,UAAzB,CAAoC,IAApC,CAAD,CAAJ,EAAgD;UAC9CmP,qBAAqB,CAACwB,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,aAAZ,CAArB,GAAkDD,SAAS,CAACtT,KAA5D;QACD;MACF;IACF;;IAED,SAAS8gB,kBAAT,CAA4BvU,QAA5B,EAAyC;;;MACvC,IAAImD,GAAG,KAAK,eAAR,IAA2BA,GAAG,KAAK,qBAAvC,EAA8D;QAC5D,OAAO,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAatC,aAAb,GAAa,MAAb,GAAaA,GAAGb,QAAH,CAAb,IAA4B,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAa6C,aAAb,GAAa,MAAb,GAAaA,GAAG7C,QAAH,CAAzC,GAAwD,EAA/D;MACD,CAFD,MAEO;QACL,OAAO,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoU,QAAV,MAAkB,IAAlB,IAAkB/M,aAAlB,GAAkB,MAAlB,GAAkBA,GAAGrH,QAAH,CAAlB,MAAkCuG,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAGvG,QAAH,CAA1C,KAA0D,EAAjE;MACD;IACF;;IACD,IAAIwU,YAAY,GAAG,EAAnB;IAEA,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAElC,KAAV,MAAe,IAAf,IAAezP,aAAf,GAAe,MAAf,GAAeA,GAAEtM,GAAF,CAAOgc,IAAD,IAAc;MACjCiC,YAAY,IAAIjC,IAAI,GAAG,IAAvB;IACD,CAFc,CAAf;IAGAiC,YAAY,GAAGA,YAAY,CAACnhB,KAAb,CAAmB,CAAnB,EAAsB,CAAC,CAAvB,CAAf;IAEAmS,YAAY,GAAG,CACb;MACES,GAAG,EAAE,kBADP;MAEEG,IAAI,EACF,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqO,aAAV,KAA2B,CAA3B,GACI,MADJ,GAEIxjB,cAAc,CAACsV,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/H,MAAX;IALtB,CADa,EAQb;MACEyH,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,eAAQ,CAACsO,WAAT,MAAoB,IAApB,IAAoBrN,aAApB,GAAoB,MAApB,GAAoBA,GAAEnH,WAAtB,IACF,cAAQ,CAACwU,WAAT,MAAoB,IAApB,IAAoBnN,aAApB,GAAoB,MAApB,GAAoBA,GAAErH,WADpB,GAEF;IAJN,CARa,EAcb;MACE+F,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE,qBAAQ,CAACsO,WAAT,MAAoB,IAApB,IAAoBlN,aAApB,GAAoB,MAApB,GAAoBA,GAAEiE,SAAtB,MAA+B,IAA/B,IAA+B5D,aAA/B,GAA+B,MAA/B,GAA+BA,GAAE3H,WAAjC,IACF,oBAAQ,CAACwU,WAAT,MAAoB,IAApB,IAAoB5M,aAApB,GAAoB,MAApB,GAAoBA,GAAE2D,SAAtB,MAA+B,IAA/B,IAA+BzD,aAA/B,GAA+B,MAA/B,GAA+BA,GAAE9H,WAD/B,GAEF;IAJN,CAda,EAoBb;MAAE+F,GAAG,EAAE,QAAP;MAAiBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEa,IAAV,IAAiBV,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEU,IAA3B,GAAkC;IAAzD,CApBa,EAqBb;MACEhB,GAAG,EAAE,kBADP;MAEEG,IAAI,EAAE,CAAC,MAAK;;;QACV,MAAM+J,cAAc,GAAG5J,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6J,aAAjC;QACA,MAAMC,cAAc,GAAG9J,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8J,cAAjC;;QACA,IAAIF,cAAc,IAAIA,cAAc,GAAG,CAAnC,IAAwCpf,cAAc,CAACof,cAAD,CAA1D,EAA4E;UAC1E,IAAIA,cAAc,KAAKpf,cAAc,CAAC,aAAD,CAAjC,IAAoDsf,cAAxD,EAAwE;YACtE,MAAMsE,iBAAiB,GAAG,IAAI3f,IAAJ,CAASqb,cAAT,CAA1B;YACA,MAAMuE,cAAc,GAAG,IAAI5f,IAAJ,CAAS+D,WAAT,CAAvB;YACA,MAAM8b,MAAM,GAAGF,iBAAiB,IAAIC,cAApC;YACA,OAAOC,MAAM,GACT,eADS,GAETlhB,eAAe,CACf0c,cADe,EAEf,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjJ,YAAV,MAAsB,IAAtB,IAAsBvG,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE/L,aAFT,EAGf,cAHe,CAFnB;UAOD;;UACD,OAAO/D,cAAc,CAACof,cAAD,CAArB;QACD;;QACD,OAAO,EAAP;MACD,CAnBK;IAFR,CArBa,EA4Cb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACElK,GAAG,EAAE,aADP;MAEEG,IAAI,EAAE,GAAG,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkN,YAAV,MAAsB,IAAtB,IAAsBrL,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEuL,SAAxB,IACL,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,YAAV,MAAsB,IAAtB,IAAsBpL,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEsL,SADnB,GAEL,EACF,IAAI,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEF,YAAV,MAAsB,IAAtB,IAAsBnL,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEsL,iBAAxB,IACA,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEH,YAAV,MAAsB,IAAtB,IAAsBlL,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEqL,iBADxB,GAEA,EACJ;IARJ,CAxDa,EAkEb;MACExN,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwM,IAAV,IAAiBrM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqM,IAA3B,GAAkC,EAAE,IAAIrM,QAAQ,CAACsM,UAAT,GAC7C,iBAAW,CAACtM,QAAQ,CAACsM,UAAV,EAAsBX,aAAtB,CAAX,MAA+C,IAA/C,IAA+C3J,aAA/C,GAA+C,MAA/C,GAA+CA,GAAEuK,IADJ,GAE7C,EACF;IALJ,CAlEa,EAyEb;MACE7M,GAAG,EAAE,WADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqD,OAAV,IACFC,uBAAuB,CAACnD,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkD,OAAX,CADrB,GAEF;IAJN,CAzEa,EA+Eb;MACExD,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0O,aAAV,MAAuB,IAAvB,IAAuBrM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAExB,IAAzB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6N,aAAV,MAAuB,IAAvB,IAAuBpM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEzB,IADvB,GAEF;IAJN,CA/Ea,EAqFb;MACEhB,GAAG,EAAE,SADP;MAEEG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsN,KAAV,IAAkBnN,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmN,KAA5B,GAAoC;IAF5C,CArFa,EAyFb;MACEzN,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0O,aAAV,MAAuB,IAAvB,IAAuBnM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEiB,SAAzB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkL,aAAV,MAAuB,IAAvB,IAAuBlM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEgB,SADvB,GAEF;IAJN,CAzFa,EA+Fb;MACE3D,GAAG,EAAE,yBADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0O,aAAV,MAAuB,IAAvB,IAAuBhM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEiM,cAAzB,IACF,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAED,aAAV,MAAuB,IAAvB,IAAuB/L,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEgM,cADvB,GAEF;IAJN,CA/Fa,EAqGb;MACE9O,GAAG,EAAE,eADP;MAEEG,IAAI,EAAEb,qBAAqB,CAAC,gBAAD,CAArB,GACFA,qBAAqB,CAAC,gBAAD,CADnB,GAEF;IAJN,CArGa,EA2Gb;MACEU,GAAG,EAAE,UADP;MAEEG,IAAI,EAAE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4O,OAAV,MAAiB,IAAjB,IAAiBhM,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEhX,MAAnB,IACFuU,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyO,OAAV,CAAkBze,GAAlB,CAAuBiQ,KAAD,IAAmB/V,MAAM,CAAC+V,KAAD,CAA/C,EAAwDmB,IAAxD,CAA6D,IAA7D,CADE,GAEF;IAJN,CA3Ga,EAiHb;MACE1B,GAAG,EAAE,YADP;MAEEG,IAAI,EAAEmO,kBAAkB,CAAC,MAAD;IAF1B,CAjHa,EAqHb;MACEtO,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwM,IAAV,IAAiBrM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEqM,IAA3B,GAAkC,EAAE,IAAIrM,QAAQ,CAACsM,UAAT,GAC7C,iBAAW,CAACtM,QAAQ,CAACsM,UAAV,EAAsBX,aAAtB,CAAX,MAA+C,IAA/C,IAA+CjJ,aAA/C,GAA+C,MAA/C,GAA+CA,GAAE6J,IADJ,GAE7C,EACF;IALJ,CArHa,EA4Hb;MACE7M,GAAG,EAAE,cADP;MAEEG,IAAI,EAAE,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0H,UAAV,IAAuBvH,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuH,UAAjC,GAA8C,EAAE,IAAIvH,QAAQ,CAAC0O,gBAAT,GACzD,iBAAW,CAAC1O,QAAQ,CAAC0O,gBAAV,EAA4B/C,aAA5B,CAAX,MAAqD,IAArD,IAAqD/I,aAArD,GAAqD,MAArD,GAAqDA,GAAE2J,IADE,GAEzD,EACF;IALJ,CA5Ha,EAmIb;MACE7M,GAAG,EAAE,eADP;MAEEG,IAAI,EAAE,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4M,WAAV,IAAwBzM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEyM,WAAlC,GAAgD,EAAE,IAAIzM,QAAQ,CAAC0M,aAAT,GAC3D,iBAAW,CAAC1M,QAAQ,CAAC0M,aAAV,EAAyBf,aAAzB,CAAX,MAAkD,IAAlD,IAAkD9I,aAAlD,GAAkD,MAAlD,GAAkDA,GAAE0J,IADO,GAE3D,EACF;IALJ,CAnIa,EA0Ib;MACE7M,GAAG,EAAE,oBADP;MAEEG,IAAI,EAAE,GAAG,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8O,gBAAV,IAA6B3O,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2O,gBAAvC,GAA0D,EACjE,IAAI3O,QAAQ,CAAC4O,oBAAT,GACA,iBAAW,CAAC5O,QAAQ,CAAC4O,oBAAV,EAAgCjD,aAAhC,CAAX,MAAyD,IAAzD,IAAyD7I,aAAzD,GAAyD,MAAzD,GAAyDA,GAAEyJ,IAD3D,GAEA,EACJ;IANJ,CA1Ia,EAkJb;MACE7M,GAAG,EAAE,mBADP;MAEEG,IAAI,EACFmO,kBAAkB,CAAC,iBAAD,CAAlB,GACA,GADA,GAEA5Q,YAAY,CACV4Q,kBAAkB,CAAC,iBAAD,CADR,EAEVA,kBAAkB,CAAC,UAAD,CAFR,CAFZ,GAMA;IATJ,CAlJa,EA6Jb;MACEtO,GAAG,EAAE,gBADP;MAEEG,IAAI,EACFmO,kBAAkB,CAAC,cAAD,CAAlB,GACA,GADA,GAEA5Q,YAAY,CACV4Q,kBAAkB,CAAC,cAAD,CADR,EAEVA,kBAAkB,CAAC,UAAD,CAFR,CAFZ,GAMA;IATJ,CA7Ja,EAwKb;MACEtO,GAAG,EAAE,cADP;MAEEG,IAAI,EACFmO,kBAAkB,CAAC,OAAD,CAAlB,GACA,GADA,GAEA5Q,YAAY,CACV4Q,kBAAkB,CAAC,OAAD,CADR,EAEVA,kBAAkB,CAAC,UAAD,CAFR,CAFZ,GAMA;IATJ,CAxKa,EAmLb;MACEtO,GAAG,EAAE,YADP;MAEEG,IAAI,EAAEmO,kBAAkB,CAAC,uBAAD;IAF1B,CAnLa,EAuLb;MACEtO,GAAG,EAAE,eADP;MAEEG,IAAI,EAAEmO,kBAAkB,CAAC,kCAAD;IAF1B,CAvLa,EA2Lb;MACEtO,GAAG,EAAE,OADP;MAEEG,IAAI,EACFjD,GAAG,KAAK,eAAR,GACI,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiR,QAAV,MAAkB,IAAlB,IAAkB9K,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEmJ,OAApB,IACEzG,mBAAmB,CAACzF,QAAQ,CAAC6N,QAAT,CAAkB3B,OAAnB,CADrB,GAEEzG,mBAAmB,CAACzF,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEkM,OAAX,CAHzB,GAII,qBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2B,QAAV,MAAkB,IAAlB,IAAkB5K,aAAlB,GAAkB,MAAlB,GAAkBA,GAAG,CAAH,CAAlB,MAAuB,IAAvB,IAAuBkB,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE+H,OAAzB,IACEzG,mBAAmB,CAACzF,QAAQ,CAAC6N,QAAT,CAAkB,CAAlB,EAAqB3B,OAAtB,CADrB,GAEE;IATV,CA3La,EAsMb;MACExM,GAAG,EAAE,WADP;MAEEG,IAAI,EACFjD,GAAG,KAAK,eAAR,GACI,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiR,QAAV,MAAkB,IAAlB,IAAkBxJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAErK,OAApB,IACElQ,OAAO,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+jB,QAAV,MAAkB,IAAlB,IAAkBtJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEvK,OAArB,CADT,GAEElQ,OAAO,CAACkW,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEhG,OAAX,CAHb,GAII,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAayK,aAAb,GAAa,MAAb,GAAaA,GAAEzK,OAAf,IACElQ,OAAO,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAa4a,aAAb,GAAa,MAAb,GAAaA,GAAE1K,OAAhB,CADT,GAEE;IATV,CAtMa,EAiNb;MACE0F,GAAG,EAAE,UADP;MAEEG,IAAI,EACFjD,GAAG,KAAK,eAAR,GACI,qBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiR,QAAV,MAAkB,IAAlB,IAAkBjJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE6J,OAApB,MAA2B,IAA3B,IAA2B3J,aAA3B,GAA2B,MAA3B,GAA2BA,GAAErZ,MAA7B,IACEuU,QAAQ,CAAC6N,QAAT,CAAkBY,OAAlB,CACCze,GADD,CACMiQ,KAAD,IAAmB/V,MAAM,CAAC+V,KAAD,CAD9B,EAECmB,IAFD,CAEM,IAFN,CADF,GAIE,EALN,GAMI,sBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAa2D,aAAb,GAAa,MAAb,GAAaA,GAAE0J,OAAf,MAAsB,IAAtB,IAAsBzJ,cAAtB,GAAsB,MAAtB,GAAsBA,IAAEvZ,MAAxB,IACE,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAawZ,cAAb,GAAa,MAAb,GAAaA,IAAEwJ,OAAF,CACZze,GADY,CACPiQ,KAAD,IAAmB/V,MAAM,CAAC+V,KAAD,CADjB,EAEZmB,IAFY,CAEP,IAFO,CADf,GAIE;IAbV,CAjNa,EAgOb;MACE1B,GAAG,EAAE,oBADP;MAEEG,IAAI,EACFjD,GAAG,KAAK,eAAR,GACI,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEiR,QAAV,MAAkB,IAAlB,IAAkB1I,cAAlB,GAAkB,MAAlB,GAAkBA,IAAE0J,gBAApB,MAAyCC,SAAzC,GACE3kB,aAAa,CAAC6V,QAAQ,CAAC6N,QAAT,CAAkBgB,gBAAnB,CADf,GAEE1kB,aAAa,CAAC6V,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6O,gBAAX,CAHnB,GAII,gBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAG,CAAH,CAAR,MAAa,IAAb,IAAazJ,cAAb,GAAa,MAAb,GAAaA,IAAEyJ,gBAAf,MAAoCC,SAApC,GACE3kB,aAAa,CAAC0jB,QAAQ,CAAC,CAAD,CAAR,CAAYgB,gBAAb,CADf,GAEE;IATV,CAhOa,EA2Ob;MACEnP,GAAG,EAAE,gBADP;MAEEG,IAAI,EAAEqB,QAAQ,CAAClB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmB,QAAX,EAAqB,IAArB;IAFhB,CA3Oa,EA+Ob;MAAEzB,GAAG,EAAE,eAAP;MAAwBG,IAAI,EAAEoO;IAA9B,CA/Oa,EAgPb;MACEvO,GAAG,EAAE,YADP;MAEEG,IAAI,EAAE,GAAGhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEzC,SAAS,IAAIyC,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAExC,QAAQ;IAFpD,CAhPa,EAoPb;MAAEqD,GAAG,EAAE,aAAP;MAAsBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE8B;IAAtC,CApPa,EAqPb;MAAEjB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAEhB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE+B;IAAvC,CArPa,EAsPb;MAAElB,GAAG,EAAE,cAAP;MAAuBG,IAAI,EAAErB,UAAU,IAAI;IAA3C,CAtPa,EAuPb;MAAEkB,GAAG,EAAE,YAAP;MAAqBG,IAAI,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6N,QAAV,KAAsB;IAAjD,CAvPa,CAAf;IA0PA,OAAOpO,mBAAmB,CAACC,GAAD,EAAMN,YAAN,CAA1B;EACD,CApRD;;EAqRA,IAAIO,KAAK,CAACC,OAAN,CAAcmO,WAAd,CAAJ,EAAgC;IAC9BA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAE/X,OAAb,CAAqB,CAACmK,QAAD,EAAgBC,KAAhB,KAAiC;MACpD,MAAMV,GAAG,GAAGwO,kBAAkB,CAAC/N,QAAD,EAAWC,KAAX,CAA9B;MACAlB,WAAW,IAAIkB,KAAK,GAAG,CAAR,GAAY,IAAZ,GAAmBV,GAAlC;;MACA,IAAIU,KAAK,GAAG2N,WAAW,CAACniB,MAAZ,GAAqB,CAAjC,EAAoC;QAClCsT,WAAW,IAAI,IAAf;MACD;IACF,CAND;EAOD,CARD,MAQO;IACLA,WAAW,GAAGgP,kBAAkB,CAACH,WAAD,EAAc,CAAd,CAAhC;EACD;;EAED,IAAIlP,MAAJ,EAAY;IACVA,MAAM,GAAGY,mBAAmB,CAACZ,MAAD,EAASO,YAAT,CAA5B;EACD;;EAED,IAAIN,MAAJ,EAAY;IACVA,MAAM,GAAGW,mBAAmB,CAACX,MAAD,EAASM,YAAT,CAA5B;EACD;;EAED,OAAOF,WAAW,GACd,CAACL,MAAM,GAAGA,MAAM,GAAG,IAAZ,GAAmB,EAA1B,IACFK,WADE,IAEDJ,MAAM,GAAG,OAAOA,MAAV,GAAmB,EAFxB,CADc,GAId,EAJJ;AAKD,CArXM;AAuXP,OAAO,MAAMuC,QAAQ,GAAG,CACtBC,QADsB,EAEtB5H,YAAqB,KAFC,EAGtBwV,YAAqB,KAHC,KAIpB;;;EACF,MAAM1jB,SAAS,GAAGD,aAAa,EAA/B;EACA,MAAM4jB,QAAQ,GAAQ,UAAI,CAACvT,KAAL,CACpB9P,YAAY,CAACC,OAAb,CAAqB,aAArB,CADoB,OAErB,IAFqB,IAErB0O,aAFqB,GAErB,MAFqB,GAErBA,GAAE2U,kBAFH;EAIA,IAAIC,WAAW,GAAG,kBAAlB,CANE,CAMoC;;EAEtC,IAAI3V,SAAJ,EAAe;IACb2V,WAAW,GAAG,iBAAd;EACD,CAFD,MAEO,IAAIH,SAAJ,EAAe;IACpBG,WAAW,GAAG,iBAAd;EACD;;EAED,OAAO,WAAW7jB,SAAS,GAAGN,aAAa,EAAE,aAAamkB,WAAW,IAAIF,QAAQ,IAAI7N,QAAQ,EAA7F;AACD,CAnBM;AAqBP,OAAO,MAAMgO,iBAAiB,GAAG,CAACC,YAAD,EAAoBC,cAApB,KAA6C;EAC5E,MAAMC,eAAe,GAAG9P,KAAK,CAACC,OAAN,CAAc2P,YAAY,CAACliB,KAA3B,IACpBkiB,YAAY,CAACliB,KADO,GAEpB,CAACkiB,YAAY,CAACliB,KAAd,CAFJ;EAGA,MAAMqiB,aAAa,GAAU,EAA7B;EAEAF,cAAc,CAACxZ,OAAf,CAAwBsG,IAAD,IAAS;IAC9B,IAAImT,eAAe,CAAC5jB,QAAhB,CAAyByQ,IAAI,CAAC7D,EAA9B,CAAJ,EAAuC;MACrC,IAAI6D,IAAI,CAACC,SAAL,KAAmB,KAAvB,EAA8B;QAC5BmT,aAAa,CAACC,OAAd,CAAsBrT,IAAtB;MACD,CAFD,MAEO;QACLoT,aAAa,CAACtS,IAAd,CAAmBd,IAAnB;MACD;IACF;EACF,CARD;EASAoT,aAAa,CAAC1X,IAAd,CAAmB,CAACC,CAAD,EAAIC,CAAJ,KAAS;IAC1B,IAAID,CAAC,CAACsE,SAAF,KAAgB,KAApB,EAA2B,OAAO,CAAC,CAAR;IAC3B,IAAIrE,CAAC,CAACqE,SAAF,KAAgB,KAApB,EAA2B,OAAO,CAAP;IAC3B,MAAMqT,KAAK,GAAG,GAAG3X,CAAC,CAACsE,SAAS,IAAItE,CAAC,CAACuE,QAAQ,EAA1C;IACA,MAAMqT,KAAK,GAAG,GAAG3X,CAAC,CAACqE,SAAS,IAAIrE,CAAC,CAACsE,QAAQ,EAA1C;IACA,OAAOoT,KAAK,CAACE,aAAN,CAAoBD,KAApB,CAAP;EACD,CAND;EAQA,MAAME,eAAe,GAAGP,cAAc,CAAClX,MAAf,CACrBgE,IAAD,IAAU,CAACmT,eAAe,CAAC5jB,QAAhB,CAAyByQ,IAAI,CAAC7D,EAA9B,CADW,CAAxB;EAGAsX,eAAe,CAAC/X,IAAhB,CAAqB,CAACC,CAAD,EAAIC,CAAJ,KAAS;IAC5B,IAAID,CAAC,CAACsE,SAAF,KAAgB,KAApB,EAA2B,OAAO,CAAC,CAAR;IAC3B,IAAIrE,CAAC,CAACqE,SAAF,KAAgB,KAApB,EAA2B,OAAO,CAAP;IAC3B,MAAMqT,KAAK,GAAG,GAAG3X,CAAC,CAACsE,SAAS,IAAItE,CAAC,CAACuE,QAAQ,EAA1C;IACA,MAAMqT,KAAK,GAAG,GAAG3X,CAAC,CAACqE,SAAS,IAAIrE,CAAC,CAACsE,QAAQ,EAA1C;IACA,OAAOoT,KAAK,CAACE,aAAN,CAAoBD,KAApB,CAAP;EACD,CAND;EAQA,OAAOH,aAAa,CAACM,MAAd,CAAqBD,eAArB,CAAP;AACD,CAnCM;AAqCP,OAAO,MAAME,YAAY,GAAG,CAC1BlR,WAD0B,EAE1BmR,mBAA2B,EAFD,EAG1BC,WAAoB,KAHM,KAIxB;;;EACF,IAAIC,MAAM,GAAG,UAAI,CAACxU,KAAL,CAAW9P,YAAY,CAACC,OAAb,CAAqB,aAArB,CAAX,OAA+C,IAA/C,IAA+C0O,aAA/C,GAA+C,MAA/C,GAA+CA,GAAE4V,GAA9D;EACA,IAAIC,YAAJ;EAAA,IACEf,YADF;EAAA,IAEEgB,WAAW,GAAkB,EAF/B;EAAA,IAGEC,aAAa,GAAkB,EAHjC;EAAA,IAIEpU,SAAS,GAAkB,EAJ7B;EAMA2C,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAE5O,GAAb,CAAkBmM,IAAD,IAAc;IAC7B,IAAIA,IAAI,CAAC7D,EAAL,KAAY2X,MAAhB,EAAwB;MACtBE,YAAY,mCACPhU,IADO,GAEN6T,QAAQ,GAAG,EAAH,GAAQ;QAAE5T,SAAS,EAAE,KAAb;QAAoBC,QAAQ,EAAE;MAA9B,CAFV,CAAZ;IAID;;IACD,IAAIF,IAAI,CAAC7D,EAAL,KAAYyX,gBAAZ,IAAgC5T,IAAI,CAAC7D,EAAL,KAAY2X,MAAhD,EAAwD;MACtDb,YAAY,GAAGjT,IAAf;IACD;;IACD,IAAIA,IAAI,CAAC7D,EAAL,KAAY2X,MAAZ,IAAsB9T,IAAI,CAAC7D,EAAL,KAAYyX,gBAAlC,IAAsD5T,IAAI,CAACmU,QAA/D,EACEF,WAAW,CAACnT,IAAZ,CAAiBd,IAAjB;IACF,IAAIA,IAAI,CAAC7D,EAAL,KAAY2X,MAAZ,IAAsB9T,IAAI,CAAC7D,EAAL,KAAYyX,gBAAlC,IAAsD,CAAC5T,IAAI,CAACmU,QAAhE,EACED,aAAa,CAACpT,IAAd,CAAmBd,IAAnB;EACH,CAdD;EAgBA,IAAIiT,YAAJ,EAAkBnT,SAAS,CAACgB,IAAV,CAAemS,YAAf;EAClB,IAAIe,YAAJ,EAAkBlU,SAAS,CAACgB,IAAV,CAAekT,YAAf;EAElB,OAAOvR,WAAW,GAAG,CAAC,GAAG3C,SAAJ,EAAe,GAAGmU,WAAlB,EAA+B,GAAGC,aAAlC,CAAH,GAAsD,EAAxE;AACD,CAhCM;AAkCP,OAAO,MAAME,uBAAuB,GAAG,CAACC,KAAD,EAAatY,UAAb,KAAiC;EACtE,IAAIuY,KAAK,GAAQ;IACfC,YAAY,EAAE,OADC;IAEftU,SAAS,EAAE,OAFI;IAGfC,QAAQ,EAAE,EAHK;IAIf2S,QAAQ,EAAE,OAJK;IAKfrK,SAAS,EAAE,OALI;IAMfgM,UAAU,EAAE,OANG;IAOfC,MAAM,EAAE,OAPO;IAQflQ,IAAI,EAAE,OARS;IASfmQ,QAAQ,EAAE,CATK;IAUfC,WAAW,EAAE,CAVE;IAWfC,QAAQ,EAAE,CAXK;IAYfzkB,UAAU,EAAE,CAZG;IAaf0kB,YAAY,EAAE,CAbC;IAcfC,YAAY,EAAE,CAdC;IAefC,aAAa,EAAE,CAfA;IAgBfC,qBAAqB,EAAE,CAhBR;IAiBfC,uBAAuB,EAAE,CAjBV;IAkBfC,gBAAgB,EAAE,CAlBH;IAmBfC,mBAAmB,EAAE,CAnBN;IAoBfC,kBAAkB,EAAE,CApBL;IAqBfC,qBAAqB,EAAE,CArBR;IAsBfC,WAAW,EAAE,CAtBE;IAuBfC,kBAAkB,EAAE,CAvBL;IAwBfC,YAAY,EAAE,CAxBC;IAyBfC,sBAAsB,EAAE,CAzBT;IA0BfC,yBAAyB,EAAE,CA1BZ;IA2BfC,wBAAwB,EAAE,CA3BX;IA4BfC,2BAA2B,EAAE,CA5Bd;IA6BfC,mBAAmB,EAAE,CA7BN;IA8BfC,mBAAmB,EAAE,CA9BN;IA+BfC,wBAAwB,EAAE,CA/BX;IAgCfC,sBAAsB,EAAE,CAhCT;IAiCfC,2BAA2B,EAAE,CAjCd;IAkCfC,oBAAoB,EAAE,CAlCP;IAmCfC,yBAAyB,EAAE,CAnCZ;IAoCfC,iBAAiB,EAAE,CApCJ;IAqCfC,sBAAsB,EAAE,CArCT;IAsCfC,gBAAgB,EAAE,CAtCH;IAuCfC,qBAAqB,EAAE,CAvCR;IAwCfC,cAAc,EAAE,CAxCD;IAyCfC,mBAAmB,EAAE,CAzCN;IA0CfC,eAAe,EAAE,CA1CF;IA2CfC,oBAAoB,EAAE,CA3CP;IA4CfC,2BAA2B,EAAE,CA5Cd;IA6CfC,gBAAgB,EAAE,CA7CH;IA8CfC,uBAAuB,EAAE,CA9CV;IA+CfC,iBAAiB,EAAE,CA/CJ;IAgDfC,aAAa,EAAE,CAhDA;IAiDfC,cAAc,EAAE,CAjDD;IAkDfC,cAAc,EAAE,CAlDD;IAmDfC,mBAAmB,EAAE,CAnDN;IAoDfC,qBAAqB,EAAE,CApDR;IAqDfC,wBAAwB,EAAE,CArDX;IAsDfC,GAAG,EAAE,CAtDU;IAuDfC,MAAM,EAAE,CAvDO;IAwDfC,OAAO,EAAE,CAxDM;IAyDfC,QAAQ,EAAE,CAzDK;IA0DfC,IAAI,EAAE,CA1DS;IA2DfC,kBAAkB,EAAE,CA3DL;IA4DfC,QAAQ,EAAE,CA5DK;IA6DfC,mBAAmB,EAAE,CA7DN;IA8DfC,aAAa,EAAE,CA9DA;IA+DfC,YAAY,EAAE,CA/DC;IAgEfC,WAAW,EAAE,CAhEE;IAiEfC,YAAY,EAAE,CAjEC;IAkEfC,OAAO,EAAE,CAlEM;IAmEfC,UAAU,EAAE,CAnEG;IAoEfC,kBAAkB,EAAE,CApEL;IAqEfC,gBAAgB,EAAE,CArEH;IAsEfC,mBAAmB,EAAE,CAtEN;IAuEfC,gBAAgB,EAAE,CAvEH;IAwEfC,MAAM,EAAE,CAxEO;IAyEfC,MAAM,EAAE,CAzEO;IA0EfC,QAAQ,EAAE,CA1EK;IA2EfC,MAAM,EAAE,CA3EO;IA4EfC,aAAa,EAAE,CA5EA;IA6EfC,iBAAiB,EAAE,CA7EJ;IA8EfC,qBAAqB,EAAE,CA9ER;IA+EfC,eAAe,EAAE,CA/EF;IAgFfC,kBAAkB,EAAE,CAhFL;IAiFfC,UAAU,EAAE,CAjFG;IAkFfC,OAAO,EAAE,CAlFM;IAmFfC,OAAO,EAAE,CAnFM;IAoFfC,MAAM,EAAE,CApFO;IAqFfC,GAAG,EAAE,CArFU;IAsFfC,6BAA6B,EAAE,CAtFhB;IAuFfC,+BAA+B,EAAE,CAvFlB;IAwFfC,uBAAuB,EAAE,CAxFV;IAyFfC,yBAAyB,EAAE,CAzFZ;IA0FfC,gBAAgB,EAAE,CA1FH;IA2FfC,cAAc,EAAE,CA3FD;IA4FfC,kBAAkB,EAAE,CA5FL;IA6FfC,gBAAgB,EAAE,CA7FH;IA8FfC,oBAAoB,EAAE,CA9FP;IA+FfC,kBAAkB,EAAE,CA/FL;IAgGfC,UAAU,EAAE,CAhGG;IAiGfC,aAAa,EAAE,CAjGA;IAkGfC,eAAe,EAAE,CAlGF;IAmGfC,WAAW,EAAE,CAnGE;IAoGfC,WAAW,EAAE,CApGE;IAqGfC,kBAAkB,EAAE;EArGL,CAAjB;;EAwGA,MAAMC,uBAAuB,GAAG,CAC9BjG,KAD8B,EAE9BkG,QAF8B,EAG9BjG,KAH8B,KAI5B;IACFD,KAAK,CAAC3a,OAAN,CAAeuC,IAAD,IAAc;MAC1B,MAAMue,SAAS,GAAG,CAAC,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,IAAN,KAAc,EAAf,EAAmBze,MAAnB,CACfye,IAAD,IAAe,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEF,QAAN,MAAmBA,QADlB,CAAlB;MAGA,MAAMG,SAAS,GAAGF,SAAS,CAAC9Z,MAAV,CAChB,CAACia,GAAD,EAAcF,IAAd,KAA4BE,GAAG,IAAI,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAED,SAAN,KAAmB,CAAvB,CADf,EAEhB,CAFgB,CAAlB;MAIApG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,GACE,CAACjG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,IAAiC,CAAlC,IAAuCG,SADzC;IAED,CAVD;EAWD,CAhBD;;EAkBA,MAAME,0BAA0B,GAAG,CACjCvG,KADiC,EAEjCkG,QAFiC,EAGjCjG,KAHiC,KAI/B;IACFD,KAAK,CAAC3a,OAAN,CAAeuC,IAAD,IAAc;MAC1B,MAAMue,SAAS,GAAG,CAAC,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEhS,SAAN,KAAmB,EAApB,EAAwBxM,MAAxB,CACfye,IAAD,IACE,GAAGA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEjS,SAAS,IAAIiS,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEI,UAAU,GAAtC,MAA8CN,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjZ,QAAV,EAA9C,CAFc,CAAlB;MAIA,MAAMoZ,SAAS,GAAGF,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAE9Z,MAAX,CAChB,CAACia,GAAD,EAAcF,IAAd,KAA4BE,GAAG,IAAI,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEG,KAAN,KAAe,CAAnB,CADf,EAEhB,CAFgB,CAAlB;MAIAxG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,GACE,CAACjG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,IAAiC,CAAlC,IAAuCG,SADzC;IAED,CAXD;EAYD,CAjBD;;EAmBA,MAAMK,uBAAuB,GAAG,CAC9B1G,KAD8B,EAE9BkG,QAF8B,EAG9BjG,KAH8B,KAI5B;IACFD,KAAK,CAAC3a,OAAN,CAAeuC,IAAD,IAAc;MAC1B,MAAMue,SAAS,GAAG,CAAC,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE/F,MAAN,KAAgB,EAAjB,EAAqBzY,MAArB,CACfye,IAAD,IAAe,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEjd,WAAN,OAAsB+c,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjZ,QAAV,EAAtB,CADC,CAAlB;MAGA,MAAMoZ,SAAS,GAAGF,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAE9Z,MAAX,CAChB,CAACia,GAAD,EAAcF,IAAd,KAA4BE,GAAG,IAAI,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEG,KAAN,KAAe,CAAnB,CADf,EAEhB,CAFgB,CAAlB;MAIAxG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,GACE,CAACjG,KAAK,CAAC,GAAGiG,QAAQ,WAAZ,CAAL,IAAiC,CAAlC,IAAuCG,SADzC;IAED,CAVD;EAWD,CAhBD;;EAiBA3e,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAErC,OAAZ,CAAqBoC,MAAD,IAAgB;IAClCwY,KAAK,CAAC,GAAGxY,MAAM,WAAV,CAAL,GAA8B,CAA9B;IACAwY,KAAK,CAAC,GAAGxY,MAAM,WAAV,CAAL,GAA8B,CAA9B;IACAif,uBAAuB,CAAC1G,KAAD,EAAQvY,MAAR,EAAgBwY,KAAhB,CAAvB;IACAsG,0BAA0B,CAACvG,KAAD,EAAQvY,MAAR,EAAgBwY,KAAhB,CAA1B;EACD,CALD;EAOAvY,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAErC,OAAZ,CAAqBoC,MAAD,IAAgB;IAClCwe,uBAAuB,CAACjG,KAAD,EAAQvY,MAAR,EAAgBwY,KAAhB,CAAvB;EACD,CAFD;EAIAD,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAExgB,GAAP,CAAYoI,IAAD,IAAc;IACvB,IAAI+e,QAAQ,GAAGtgB,MAAM,CAACugB,OAAP,CAAehf,IAAf,CAAf;IACA+e,QAAQ,CAACnnB,GAAT,CAAcqnB,KAAD,IAAe;MAC1B,IAAI,OAAOA,KAAK,CAAC,CAAD,CAAZ,KAAoB,QAAxB,EAAkC;QAChC,IAAI,EAAC5G,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAG4G,KAAK,CAAC,CAAD,CAAR,CAAN,CAAJ,EAAwB;UACtB5G,KAAK,CAAC4G,KAAK,CAAC,CAAD,CAAN,CAAL,GAAkB,CAAlB;QACD;;QACD5G,KAAK,CAAC4G,KAAK,CAAC,CAAD,CAAN,CAAL,IAAmBA,KAAK,CAAC,CAAD,CAAxB;MACD,CALD,MAKO,IACLA,KAAK,CAAC,CAAD,CAAL,KAAa,qBAAb,IACAA,KAAK,CAAC,CAAD,CAAL,KAAa,aADb,IAEAA,KAAK,CAAC,CAAD,CAAL,KAAa,aAFb,IAGAA,KAAK,CAAC,CAAD,CAAL,KAAa,iBAHb,IAIAA,KAAK,CAAC,CAAD,CAAL,KAAa,eALR,EAML;QACA,MAAMC,YAAY,GAAG7tB,MAAM,CAACgH,QAAP,CAAgB4mB,KAAK,CAAC,CAAD,CAArB,CAArB;QACA5G,KAAK,CAAC4G,KAAK,CAAC,CAAD,CAAN,CAAL,IAAmBC,YAAY,CAACC,SAAb,EAAnB;MACD;IACF,CAhBD;EAiBD,CAnBD;;EAqBA,MAAMC,cAAc,GAAI3kB,OAAD,IAAoB;;;IACzC,MAAMpC,QAAQ,GAAGhH,MAAM,CAACgH,QAAP,CAAgBoC,OAAhB,EAAyB,SAAzB,CAAjB;IACA,MAAMF,KAAK,GAAG,gBAAI,CAACR,KAAL,CAAW1B,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEgnB,OAAV,EAAX,OAA+B,IAA/B,IAA+Bnd,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEmD,QAAF,EAA/B,MAA2C,IAA3C,IAA2CnB,aAA3C,GAA2C,MAA3C,GAA2CA,GAAEvN,QAAF,CAAW,CAAX,EAAc,GAAd,CAAzD;IACA,MAAM6D,OAAO,GAAG,oBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,OAAV,QAAmB,IAAnB,IAAmBkO,aAAnB,GAAmB,MAAnB,GAAmBA,GAAErD,QAAF,EAAnB,MAA+B,IAA/B,IAA+BuD,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEjS,QAAF,CAAW,CAAX,EAAc,GAAd,CAA/C;IACA,MAAM2oB,UAAU,GAAG,oBAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7kB,OAAV,QAAmB,IAAnB,IAAmBoO,aAAnB,GAAmB,MAAnB,GAAmBA,GAAExD,QAAF,EAAnB,MAA+B,IAA/B,IAA+B6D,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEvS,QAAF,CAAW,CAAX,EAAc,GAAd,CAAlD;IACA,OAAO,GAAG4D,KAAK,IAAIC,OAAO,IAAI8kB,UAAU,EAAxC;EACD,CAND;;EAQA,IAAI,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEjsB,MAAP,IAAgB,CAApB,EAAuB;IACrB,CACE,qBADF,EAEE,aAFF,EAGE,aAHF,EAIE,iBAJF,EAKE,eALF,EAMEoK,OANF,CAMWmB,KAAD,IAAU;MAClB,IAAIyZ,KAAK,CAACzZ,KAAD,CAAL,KAAiB8X,SAArB,EAAgC;QAC9B2B,KAAK,CAACzZ,KAAD,CAAL,GAAewgB,cAAc,CAAC/G,KAAK,CAACzZ,KAAD,CAAN,CAA7B;MACD;IACF,CAVD;IAWA,OAAO,CAAC,GAAGwZ,KAAJ,EAAWC,KAAX,CAAP;EACD;;EACD,OAAOD,KAAP;AACD,CAtNM;AAwNP,OAAO,MAAMmH,cAAc,GAAG,CAAC7iB,IAAD,EAAe8iB,KAAf,KAAgC;EAC5D,IAAIvqB,IAAI,GAAG,IAAIoB,IAAJ,CAASA,IAAI,CAACopB,GAAL,CAAS/iB,IAAT,EAAe8iB,KAAf,EAAsB,CAAtB,CAAT,CAAX;EACA,IAAIE,SAAS,GAAG,EAAhB;;EACA,OAAOzqB,IAAI,CAACiH,WAAL,OAAuBsjB,KAA9B,EAAqC;IACnCE,SAAS,CAAC7a,IAAV,CAAe,IAAIxO,IAAJ,CAASpB,IAAT,CAAf;IACAA,IAAI,CAAC0qB,UAAL,CAAgB1qB,IAAI,CAACkH,UAAL,KAAoB,CAApC;EACD;;EACD,OAAOujB,SAAP;AACD,CARM;AAUP,OAAO,MAAME,kBAAkB,GAAIC,KAAD,IAAwB;EACxD,MAAMC,UAAU,GACd,qmBADF;EAGA,OAAOA,UAAU,CAACC,IAAX,CAAgBF,KAAhB,CAAP;AACD,CALM;AAOP,OAAO,MAAM9U,uBAAuB,GAAIiV,MAAD,IAAwB;EAC7D,MAAM;IACJC,WADI;IAEJC,QAFI;IAGJC,YAHI;IAIJC,SAJI;IAKJC,SALI;IAMJC,IANI;IAOJC,QAPI;IAQJC,KARI;IASJC,OATI;IAUJC;EAVI,IAWFV,MAAM,IAAI,EAXd;EAYA,MAAMW,YAAY,GAAG,CACnBV,WADmB,EAEnBC,QAAQ,IAAIA,QAAQ,KAAKD,WAAzB,IAAwCC,QAFrB,EAGnBI,IAAI,IAAIA,IAAI,KAAKL,WAAjB,IAAgCK,IAAI,KAAKJ,QAAzC,IAAqDI,IAHlC,EAInBH,YAJmB,EAKnBC,SALmB,EAMnBC,SANmB,EAOnBE,QAPmB,EAQnBC,KARmB,EASnBC,OATmB,EAUnBC,UAVmB,EAYlB3gB,MAZkB,CAYV6gB,IAAD,IAAUA,IAZC,EAalBhpB,GAbkB,CAabgpB,IAAD,IAAUA,IAAI,CAAClZ,IAAL,EAbI,EAclBsB,IAdkB,CAcb,IAda,CAArB;;EAeA,IAAI2X,YAAY,CAACjZ,IAAb,GAAoBmZ,QAApB,CAA6B,GAA7B,CAAJ,EAAuC;IACrC,OAAOF,YAAY,CAACjZ,IAAb,GAAoBhT,KAApB,CAA0B,CAA1B,EAA6B,CAAC,CAA9B,CAAP;EACD;;EACD,OAAOisB,YAAY,IAAI,EAAvB;AACD,CAhCM;AAkCP,OAAO,MAAMG,uBAAuB,GAAIhgB,GAAD,IAAa;EAClD,IAAI,CAACA,GAAL,EAAU;IACR,OAAO,IAAP;EACD;;EACD,MAAMmf,WAAW,GAAGnf,GAAG,CAACmf,WAAxB;EACA,MAAMC,QAAQ,GAAGpf,GAAG,CAACof,QAArB;EACA,MAAMI,IAAI,GAAGxf,GAAG,CAACwf,IAAjB;EACA,MAAMS,qBAAqB,GAAG,EAA9B;EAEA,IAAId,WAAW,KAAKC,QAApB,EAA8Ba,qBAAqB,CAAClc,IAAtB,CAA2Bob,WAA3B;EAC9B,IAAIC,QAAQ,KAAKI,IAAjB,EAAuBS,qBAAqB,CAAClc,IAAtB,CAA2Bqb,QAA3B;EAEvB,IAAIc,WAAW,GAAGD,qBAAqB,CAAC/X,IAAtB,CAA2B,IAA3B,CAAlB;EACAgY,WAAW,GAAGA,WAAW,CAACtZ,IAAZ,GAAmBmZ,QAAnB,CAA4B,GAA5B,IACVG,WAAW,CAACtZ,IAAZ,GAAmBhT,KAAnB,CAAyB,CAAzB,EAA4B,CAAC,CAA7B,CADU,GAEVssB,WAFJ;EAIA,OAAOA,WAAW,IAAI,IAAtB;AACD,CAlBM;AAoBP;;;;;;AAKA,OAAO,MAAMC,qBAAqB,GAAG,CACnCC,WADmC,EAEnCC,cAFmC,KAGxB;EACX,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,cAAc,CAAC9tB,MAAnC,EAA2C+tB,CAAC,EAA5C,EAAgD;IAC9C,IAAIF,WAAW,CAAC5tB,QAAZ,CAAqB6tB,cAAc,CAACC,CAAD,CAAnC,CAAJ,EAA6C;MAC3C,OAAO,IAAP;IACD;EACF;;EACD,OAAO,KAAP;AACD,CAVM;AAYP;;;;;AAIA,OAAO,MAAMC,oBAAoB,GAAIjnB,WAAD,IAAkC;EACpE,OAAQyE,OAAD,IAAsD;IAC3D,MAAMyiB,YAAY,GAAG,IAAIjrB,IAAJ,CAASwI,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE/J,KAAlB,CAArB;IACAwsB,YAAY,CAACC,UAAb,CAAwB,CAAxB,EAA2B,CAA3B;;IAEA,IAAI,EAAC1iB,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE/J,KAAV,CAAJ,EAAqB;MACnB,OAAO;QAAE0sB,QAAQ,EAAE;MAAZ,CAAP;IACD;;IAED,MAAMC,WAAW,GAAG,IAAIprB,IAAJ,CAAS+D,WAAT,CAApB;IACAqnB,WAAW,CAACF,UAAZ,CAAuB,CAAvB,EAA0B,CAA1B;;IAEA,IAAIE,WAAW,IAAIH,YAAnB,EAAiC;MAC/B,OAAO;QAAEI,WAAW,EAAE;MAAf,CAAP;IACD;;IAED,OAAO,IAAP;EACD,CAhBD;AAiBD,CAlBM;AAoBP,OAAO,MAAMrU,mBAAmB,GAAG,CACjCsU,KADiC,EAEjCC,YAAqB,KAFY,KAGvB;EACV,MAAMC,YAAY,GAAGF,KAAK,IAAI,KAAT,GAAiB,GAAjB,GAAuBA,KAA5C;EACA,MAAMG,WAAW,GAAGH,KAAK,IAAI,KAAT,GAAiB,IAAjB,GAAwB,KAA5C;EACA,OAAO,CAACC,SAAD,GAAa,GAAGC,YAAY,IAAIC,WAAW,EAA3C,GAAgD,GAAGD,YAAY,EAAtE;AACD,CAPM;AASP,OAAO,MAAM9N,kBAAkB,GAAG,CAChCgO,IADgC,EAEhCC,WAAoB,KAFY,KAGtB;EACV,IAAID,IAAI,IAAI,KAAZ,EAAmB;IACjB,OAAO,QAAP;EACD;;EACD,OAAOC,QAAQ,GAAG,GAAGD,IAAI,EAAV,GAAe,GAAGA,IAAI,KAArC;AACD,CARM,C,CAUP;;AACA,OAAO,MAAME,YAAY,GAAIF,IAAD,IAAyB;EACnD,IAAI,CAACA,IAAL,EAAW,OAAO,EAAP;EAEX,MAAMG,SAAS,GAAGH,IAAI,CACnB3uB,KADe,CACT,GADS,EAEfwE,GAFe,CAEVuqB,CAAD,IAAOC,UAAU,CAACD,CAAC,CAACza,IAAF,EAAD,CAFN,EAGf3H,MAHe,CAGPoiB,CAAD,IAAO,CAACxpB,KAAK,CAACwpB,CAAD,CAHL,CAAlB;EAKA,OAAOD,SAAS,CAAC7uB,MAAV,GAAmB,CAAnB,GACH6uB,SAAS,CAACtqB,GAAV,CAAeyqB,EAAD,IAAQ,GAAGA,EAAE,KAA3B,EAAkCrZ,IAAlC,CAAuC,IAAvC,CADG,GAEH,EAFJ;AAGD,CAXM;AAaP,OAAO,MAAMsZ,cAAc,GAAIC,KAAD,IAA0B;;;EACtD,IAAI,CAACA,KAAL,EAAY,OAAO,EAAP;EACZ,MAAMC,UAAU,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEpvB,KAAP,CAAa,GAAb,OAAiB,IAAjB,IAAiB8O,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEtK,GAAF,CAAOuqB,CAAD,IAAOC,UAAU,CAACD,CAAC,CAACza,IAAF,EAAD,CAAvB,CAApC;EAEA,MAAM+a,aAAa,GAAGD,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAE5qB,GAAZ,CAAiBqW,GAAD,IAAQ;IAC5C,IAAIA,GAAG,KAAK,CAAZ,EAAe;MACb,OAAO,QAAP;IACD,CAFD,MAEO;MACL,OAAOA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5I,QAAL,EAAP;IACD;EACF,CANqB,CAAtB;EAQA,OAAO,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAE2D,IAAf,CAAoB,IAApB,MAA6B,EAApC;AACD,CAbM,C,CAeP;;AACA,OAAO,MAAM0Z,aAAa,GAAIf,KAAD,IAA0B;;;EACrD,IAAI,CAACA,KAAL,EAAY,OAAO,EAAP;EACZ,MAAMgB,UAAU,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEvvB,KAAP,CAAa,GAAb,OAAiB,IAAjB,IAAiB8O,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEtK,GAAF,CAAOuqB,CAAD,IAAOC,UAAU,CAACD,CAAC,CAACza,IAAF,EAAD,CAAvB,CAApC;EAEA,MAAMkb,aAAa,GAAGD,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAE/qB,GAAZ,CAAiBwV,GAAD,IAAQ;IAC5C,IAAIA,GAAG,KAAK,GAAZ,EAAiB;MACf,OAAO,KAAP;IACD,CAFD,MAEO,IAAIA,GAAJ,EAAS;MACd,OAAO,GAAGA,GAAG,MAAb;IACD,CAFM,MAEA;MACL,OAAOA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE/H,QAAL,EAAP;IACD;EACF,CARqB,CAAtB;EAUA,OAAO,cAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAE2D,IAAf,CAAoB,IAApB,MAA6B,EAApC;AACD,CAfM;AAiBP,OAAO,MAAM6Z,wBAAwB,GAAG,MAAoB;EAC1D,MAAMC,gBAAgB,GAAkBrkB,MAAM,CAACC,IAAP,CAAYzM,UAAZ,EACrCyC,KADqC,CAC/B,EAD+B,EAErC+K,IAFqC,EAAxC;EAGA,OAAOqjB,gBAAP;AACD,CALM;AAOP,OAAO,MAAMC,YAAY,GAAIC,SAAD,IAA8B;EACxD,IAAI,CAACA,SAAL,EAAgB,OAAOA,SAAP;EAEhB,MAAMC,UAAU,GAAGD,SAAS,CAAC5vB,KAAV,CAAgB,GAAhB,CAAnB;EACA,MAAM8vB,eAAe,GACnBD,UAAU,CAAC,CAAD,CAAV,GACAA,UAAU,CACPvuB,KADH,CACS,CADT,EAEGkD,GAFH,CAEQurB,IAAD,IAAUA,IAAI,CAAC3uB,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+B0uB,IAAI,CAACzuB,KAAL,CAAW,CAAX,CAFhD,EAGGsU,IAHH,CAGQ,EAHR,CAFF;EAOA,OAAOka,eAAP;AACD,CAZM;AAcP,OAAO,MAAME,YAAY,GAAG,CAACC,KAAD,EAAmBjpB,WAAnB,KAAgD;EAC1E,MAAM8N,KAAK,GAAG,IAAI7R,IAAJ,CAAS+D,WAAT,CAAd;EACA,IAAIkpB,SAAJ;EACA,IAAIC,OAAJ;;EAEA,QAAQF,KAAR;IACE,KAAK1xB,SAAS,CAAC6xB,KAAf;MACEF,SAAS,GAAG,IAAIjtB,IAAJ,CAAS6R,KAAT,CAAZ;MACAqb,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACA;;IAEF,KAAKvW,SAAS,CAAC8xB,SAAf;MACEH,SAAS,GAAG,IAAIjtB,IAAJ,CAAS6R,KAAT,CAAZ;MACAob,SAAS,CAACI,OAAV,CAAkBxb,KAAK,CAACtR,OAAN,KAAkB,CAApC;MACA2sB,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACAqb,OAAO,CAACG,OAAR,CAAgBxb,KAAK,CAACtR,OAAN,KAAkB,CAAlC;MACA;;IAEF,KAAKjF,SAAS,CAACgyB,SAAf;MACEL,SAAS,GAAG,IAAIjtB,IAAJ,CAAS6R,KAAT,CAAZ;MACAob,SAAS,CAACI,OAAV,CAAkBxb,KAAK,CAACtR,OAAN,KAAkB,CAApC;MACA2sB,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACA;;IAEF,KAAKvW,SAAS,CAACiyB,YAAf;MACEN,SAAS,GAAG,IAAIjtB,IAAJ,CAAS6R,KAAK,CAAC1R,WAAN,EAAT,EAA8B0R,KAAK,CAACxR,QAAN,EAA9B,EAAgD,CAAhD,CAAZ;MACA6sB,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACA;;IAEF,KAAKvW,SAAS,CAACkyB,QAAf;MACEP,SAAS,GAAG,IAAZ;MACAC,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACA;;IAEF;MACEob,SAAS,GAAG,IAAIjtB,IAAJ,CAAS6R,KAAT,CAAZ;MACAqb,OAAO,GAAG,IAAIltB,IAAJ,CAAS6R,KAAT,CAAV;MACA;EAhCJ;;EAkCA,OAAO,CAACob,SAAD,EAAYC,OAAZ,CAAP;AACD,CAxCM;AA0CP,OAAO,MAAMO,SAAS,GAAG,CAACC,GAAD,EAAcC,OAAd,KAAiC;EACxDD,GAAG,GAAGA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEpsB,OAAL,CAAa,GAAb,EAAkB,EAAlB,CAAN;EACA,MAAMssB,CAAC,GAAGC,QAAQ,CAACH,GAAG,CAACrvB,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;EACA,MAAMyvB,CAAC,GAAGD,QAAQ,CAACH,GAAG,CAACrvB,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;EACA,MAAMiL,CAAC,GAAGukB,QAAQ,CAACH,GAAG,CAACrvB,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAD,EAAkB,EAAlB,CAAlB;EACA,MAAM0vB,YAAY,GAAGhwB,IAAI,CAACiwB,GAAL,CAAS,CAAT,EAAYjwB,IAAI,CAACkwB,GAAL,CAAS,CAAT,EAAYN,OAAZ,CAAZ,CAArB;EACA,OAAO,QAAQC,CAAC,KAAKE,CAAC,KAAKxkB,CAAC,KAAKykB,YAAY,GAA7C;AACD,CAPM;AASP,OAAO,MAAMG,2BAA2B,GACtC1lB,OADsD,IAE3B;EAC3B,MAAM2lB,SAAS,GAAG3lB,OAAO,CAAC/J,KAA1B;;EACA,IAAI,CAAC0vB,SAAD,IAAcA,SAAS,CAACnxB,MAAV,GAAmB,CAArC,EAAwC;IACtC,OAAO;MAAEoxB,kBAAkB,EAAE;IAAtB,CAAP;EACD;;EACD,OAAO,IAAP;AACD,CARM;AAUP,OAAM,SAAUC,eAAV,CAA0Bjf,KAA1B,EAAoC;EACxC,MAAMkf,KAAK,GAAQ,EAAnB;EACAlf,KAAK,CAACmf,GAAN,CAAUC,sBAAV,CAAkCC,IAAD,IAAc;IAC7C,IAAIA,IAAI,CAACC,UAAL,EAAJ,EAAuB;MACrBJ,KAAK,CAAC9f,IAAN,CAAWigB,IAAX;IACD;EACF,CAJD;EAKArf,KAAK,CAACmf,GAAN,CAAUI,gBAAV,GAA6BvnB,OAA7B,CAAsCqnB,IAAD,IAAc;IACjD,IAAI,CAACH,KAAK,CAACrxB,QAAN,CAAewxB,IAAf,CAAL,EAA2B;MACzBA,IAAI,CAACG,WAAL,CAAiB,KAAjB;IACD;EACF,CAJD;AAKD;AAED,OAAM,SAAUC,KAAV,CAAgBC,GAAhB,EAA2B;EAC/B,MAAMC,UAAU,GAAG,IAAIxS,MAAJ,CACjB,wBAAwB;EACxB,WADA,GACc;EACd,+CAFA,GAEkD;EAClD,iCAJiB,EAIkB;EACnC,GALiB,CAKb;EALa,CAAnB;EAQA,OAAOwS,UAAU,CAACrF,IAAX,CAAgBoF,GAAhB,CAAP;AACD;AAED,OAAM,SAAUE,kBAAV,CACJ5d,IADI,EAEJ6d,iBAA0B,KAFtB,EAE2B;EAE/B,IAAI,CAAC7d,IAAL,EAAW,OAAO,EAAP;EACX,MAAM8d,QAAQ,GACZ,+DADF;EAEA,OAAO9d,IAAI,CAAC9P,OAAL,CAAa4tB,QAAb,EAAwBC,GAAD,IAAQ;IACpC,IAAIC,YAAY,GAAGD,GAAnB;;IACA,IACE,CAACA,GAAG,CAAC/tB,UAAJ,CAAe,SAAf,CAAD,IACA,CAAC+tB,GAAG,CAAC/tB,UAAJ,CAAe,UAAf,CADD,IAEA,CAAC+tB,GAAG,CAAC/tB,UAAJ,CAAe,MAAf,CAHH,EAIE;MACAguB,YAAY,GAAG,UAAUD,GAAG,EAA5B;IACD;;IACD,OAAOF,cAAc,GACjB,YAAYG,YAAY,+CAA+CD,GAAG,MADzD,GAEjB,YAAYC,YAAY,qBAAqBD,GAAG,MAFpD;EAGD,CAZM,CAAP;AAaD;AAED,OAAO,MAAME,2BAA2B,GACtC7mB,OADsD,IAE3B;EAC3B,MAAM8mB,YAAY,GAAG9mB,OAAO,CAAC/J,KAA7B;;EACA,IAAI,CAAC6wB,YAAD,IAAiBA,YAAY,CAACtyB,MAAb,GAAsB,CAA3C,EAA8C;IAC5C,OAAO;MAAEuyB,kBAAkB,EAAE;IAAtB,CAAP;EACD;;EACD,OAAO,IAAP;AACD,CARM;AAUP,OAAM,SAAUC,oBAAV,GAA8B;EAClC,MAAMjX,MAAM,GAAG,CAAC,gBAAD,EAAmB,gBAAnB,EAAqC,QAArC,CAAf;;EACA,KAAK,IAAIwS,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,GAArB,EAA0BA,CAAC,EAA3B,EAA+B;IAC7BxS,MAAM,CAAC/J,IAAP,CAAYuc,CAAC,CAAC/b,QAAF,EAAZ;EACD;;EACD,OAAOuJ,MAAP;AACD;AAED,OAAM,SAAUkX,eAAV,CAA0BC,OAA1B,EAAsC;EAC1C,IAAIC,MAAM,GAAG,IAAI90B,UAAJ,EAAb;EAEAuN,MAAM,CAACugB,OAAP,CAAe+G,OAAf,EAAwBtoB,OAAxB,CAAgC,CAAC,CAAC+G,GAAD,EAAM1P,KAAN,CAAD,KAAsB;IACpD,IAAIA,KAAK,IAAIA,KAAK,KAAK,CAAvB,EAA0B;MACxB,IAAIsS,KAAK,CAACC,OAAN,CAAcvS,KAAd,CAAJ,EAA0B;QACxB,MAAMmxB,OAAO,GAAGnhB,UAAU,CAACN,GAAD,EAAM1P,KAAN,CAA1B;QACAmxB,OAAO,CAACxoB,OAAR,CAAiByoB,OAAD,IAAiB;UAC/BF,MAAM,GAAGA,MAAM,CAACG,MAAP,CAAc3hB,GAAd,EAAmB0hB,OAAnB,CAAT;QACD,CAFD;MAGD,CALD,MAKO;QACLF,MAAM,GAAGA,MAAM,CAACI,GAAP,CAAW5hB,GAAX,EAAgB1P,KAAhB,CAAT;MACD;IACF;EACF,CAXD;EAaA,OAAOkxB,MAAP;AACD;AAED,OAAM,SAAUK,gBAAV,CACJC,QADI,EAEJC,YAFI,EAEmB;EAEvB,OAAO9nB,MAAM,CAACC,IAAP,CAAY4nB,QAAZ,EACJvmB,MADI,CACIyE,GAAD,IAAS7L,KAAK,CAACd,MAAM,CAAC2M,GAAD,CAAP,CAAL,IAAsBA,GAAG,KAAK,MAD1C,EAEJ5M,GAFI,CAEA,CAAC4M,GAAD,EAAMqD,KAAN,MAAiB;IACpB2e,SAAS,EAAEF,QAAQ,CAAC9hB,GAAD,CADC;IAEpB8D,IAAI,EAAE9D,GAFc;IAGpBjD,WAAW,EAAE,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAGsG,KAAH,CAAZ,KAAyBrD;EAHlB,CAAjB,CAFA,CAAP;AAOD;AAED,OAAM,SAAUiiB,oBAAV,CAA+BvzB,QAA/B,EAA+C;EACnD,IAAI,CAACA,QAAL,EAAe;IACb,OAAO,EAAP;EACD;;EACDA,QAAQ,GAAGA,QAAQ,CAACyE,OAAT,CAAiB,KAAjB,EAAwB,EAAxB,CAAX;EACAzE,QAAQ,GAAGA,QAAQ,CAACyE,OAAT,CAAiB,KAAjB,EAAwB,GAAxB,CAAX;EACA,OAAOzE,QAAQ,CAACwU,IAAT,EAAP;AACD;AAED,OAAM,SAAUgf,kBAAV,CAA6BjhB,KAA7B,EAAiD;EACrD,MAAMkhB,OAAO,GAAG,SAAhB;;EACA,IAAI,CAACA,OAAO,CAAC5G,IAAR,CAAata,KAAK,CAACjB,GAAnB,CAAL,EAA8B;IAC5BiB,KAAK,CAACG,cAAN;EACD;AACF;AAGD,gBAAsBghB,gBAAtB;EAAA;AAAA;;;wCAAO,WACLC,SADK,EAELC,QAFK,EAGLC,iBAHK,EAILC,SAJK,EAKLC,WALK,EAMLC,QANK,EAM+C;IAGpD,MAAMC,MAAM,GAAG,WAAf;IACA,IAAIC,EAAE,GAAuB,IAA7B;;IAEA,IAAI;MACFA,EAAE,SAAS,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAoB;QACzC,MAAMC,OAAO,GAAGC,SAAS,CAACC,IAAV,CAAeP,MAAf,CAAhB;;QACAK,OAAO,CAACG,SAAR,GAAoB,MAAML,OAAO,CAACE,OAAO,CAACI,MAAT,CAAjC;;QACAJ,OAAO,CAACK,OAAR,GAAkB,MAAMN,MAAM,CAACC,OAAO,CAACM,KAAT,CAA9B;MACD,CAJU,CAAX;;MAMA,IAAI,CAACV,EAAE,CAACW,gBAAH,CAAoB/pB,QAApB,CAA6B6oB,SAA7B,CAAL,EAA8C;QAC5C,MAAMmB,UAAU,GAAGZ,EAAE,CAACa,OAAH,GAAa,CAAhC;QACAb,EAAE,CAACc,KAAH;;QAEA,IAAI;UACFd,EAAE,SAAS,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAoB;YACzC,MAAMC,OAAO,GAAGC,SAAS,CAACC,IAAV,CAAeP,MAAf,EAAuBa,UAAvB,CAAhB;;YACAR,OAAO,CAACW,eAAR,GAA2B1iB,KAAD,IAAe;cACvC,MAAM2iB,SAAS,GAAG3iB,KAAK,CAAC4iB,MAAN,CAAaT,MAA/B;;cACA,IAAI,CAACQ,SAAS,CAACL,gBAAV,CAA2B/pB,QAA3B,CAAoC6oB,SAApC,CAAL,EAAqD;gBACnDuB,SAAS,CAACE,iBAAV,CAA4BzB,SAA5B,EAAuC;kBAAE0B,OAAO,EAAE;gBAAX,CAAvC;cACD;YACF,CALD;;YAMAf,OAAO,CAACG,SAAR,GAAoB,MAAML,OAAO,CAACE,OAAO,CAACI,MAAT,CAAjC;;YACAJ,OAAO,CAACK,OAAR,GAAkB,MAAMN,MAAM,CAACC,OAAO,CAACM,KAAT,CAA9B;UACD,CAVU,CAAX;;UAYA,IAAI,CAACV,EAAE,CAACW,gBAAH,CAAoB/pB,QAApB,CAA6B6oB,SAA7B,CAAL,EAA8C;YAC5C2B,OAAO,CAACV,KAAR,CAAc,mCAAmCjB,SAAS,wBAA1D;YACA,IAAIO,EAAJ,EAAQA,EAAE,CAACc,KAAH;YACR,MAAMO,KAAK,SAASzB,SAAS,EAA7B;YACA,OAAOE,QAAQ,CAACuB,KAAD,EAAQ,KAAR,CAAf;UACD;QAEF,CApBD,CAoBE,OAAOC,cAAP,EAAuB;UACvBF,OAAO,CAACV,KAAR,CAAc,oCAAoCjB,SAAS,GAA3D,EAAgE6B,cAAhE;UACA,IAAItB,EAAJ,EAAQA,EAAE,CAACc,KAAH;UACR,MAAMO,KAAK,SAASzB,SAAS,EAA7B;UACA,OAAOE,QAAQ,CAACuB,KAAD,EAAQ,KAAR,CAAf;QACD;MACF;;MAED,MAAME,SAAS,SAAS,IAAItB,OAAJ,CAAkBC,OAAD,IAAY;QACnD,IAAI;UACF,MAAMsB,EAAE,GAAGxB,EAAG,CAACyB,WAAJ,CAAgBhC,SAAhB,EAA2B,UAA3B,CAAX;UACA,MAAMiC,KAAK,GAAGF,EAAE,CAACG,WAAH,CAAelC,SAAf,CAAd;UACA,MAAMmC,MAAM,GAAGF,KAAK,CAAChqB,GAAN,CAAUgoB,QAAV,CAAf;;UACAkC,MAAM,CAACrB,SAAP,GAAmB,MAAML,OAAO,CAAC0B,MAAM,CAACpB,MAAP,IAAiB,IAAlB,CAAhC;;UACAoB,MAAM,CAACnB,OAAP,GAAiB,MAAMP,OAAO,CAAC,IAAD,CAA9B;QACD,CAND,CAME,OAAO2B,KAAP,EAAc;UACdT,OAAO,CAACV,KAAR,CAAc,0CAA0CjB,SAAS,GAAjE,EAAsEoC,KAAtE;UACA3B,OAAO,CAAC,IAAD,CAAP;QACD;MACF,CAXuB,CAAxB;MAaA,MAAM4B,iBAAiB,GAAG,UAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEC,YAAX,KAA2B,IAArD;MAEA,IAAIC,kBAAkB,GAAkB,IAAxC;;MACA,IAAI;QACFA,kBAAkB,SAASrC,iBAAiB,EAA5C;MACD,CAFD,CAEE,OAAOsC,GAAP,EAAY;QACZb,OAAO,CAACV,KAAR,CAAc,8CAAd,EAA8DuB,GAA9D;QACA,IAAIjC,EAAJ,EAAQA,EAAE,CAACc,KAAH;;QAER,IAAIS,SAAJ,EAAe;UACb,OAAOzB,QAAQ,CAACyB,SAAD,EAAY,IAAZ,CAAf;QACD;;QACD,MAAMF,KAAK,SAASzB,SAAS,EAA7B;QACA,OAAOE,QAAQ,CAACuB,KAAD,EAAQ,KAAR,CAAf;MACD;;MAED,IAAIW,kBAAkB,IAAIA,kBAAkB,KAAKF,iBAAjD,EAAoE;QAClE,MAAMT,KAAK,SAASzB,SAAS,EAA7B;;QAEA,IAAI;UACF,MAAM,IAAIK,OAAJ,CAAkB,CAACC,OAAD,EAAUC,MAAV,KAAoB;YAC1C,MAAMqB,EAAE,GAAGxB,EAAG,CAACyB,WAAJ,CAAgBhC,SAAhB,EAA2B,WAA3B,CAAX;YACA,MAAMiC,KAAK,GAAGF,EAAE,CAACG,WAAH,CAAelC,SAAf,CAAd;YACA,MAAMyC,SAAS,GAAGrC,WAAW,CAACwB,KAAD,EAAQW,kBAAR,CAA7B;YAEAN,KAAK,CAACS,GAAN,CAAUD,SAAV;;YAEAV,EAAE,CAACY,UAAH,GAAgB,MAAMlC,OAAO,EAA7B;;YACAsB,EAAE,CAACf,OAAH,GAAa,MAAMN,MAAM,CAACqB,EAAE,CAACd,KAAJ,CAAzB;UACD,CATK,CAAN;QAUD,CAXD,CAWE,OAAO2B,SAAP,EAAkB;UAClBjB,OAAO,CAACV,KAAR,CAAc,sCAAsCjB,SAAS,GAA7D,EAAkE4C,SAAlE;QACD;;QAED,IAAIrC,EAAJ,EAAQA,EAAE,CAACc,KAAH;QACR,OAAOhB,QAAQ,CAACuB,KAAD,EAAQ,KAAR,CAAf;MAED,CArBD,MAqBO;QACL,IAAIrB,EAAJ,EAAQA,EAAE,CAACc,KAAH;QACR,OAAOhB,QAAQ,CAACyB,SAAD,EAAY,IAAZ,CAAf;MACD;IAEF,CA9FD,CA8FE,OAAOU,GAAP,EAAY;MACZb,OAAO,CAACV,KAAR,CAAc,wBAAwBjB,SAAS,GAA/C,EAAoDwC,GAApD;MACA,IAAIjC,EAAJ,EAAQA,EAAE,CAACc,KAAH;;MAER,IAAI;QACF,MAAMO,KAAK,SAASzB,SAAS,EAA7B;QACA,OAAOE,QAAQ,CAACuB,KAAD,EAAQ,KAAR,CAAf;MACD,CAHD,CAGE,OAAOiB,QAAP,EAAiB;QACjBlB,OAAO,CAACV,KAAR,CAAc,qCAAd,EAAqD4B,QAArD;QACA,MAAMA,QAAN;MACD;IACF;EACF,C", "names": ["HttpParams", "FormControl", "FormGroup", "moment", "EMPTY_GUID", "PROPERTY_BUDGET_FILTER", "VALIDATION_CLEAR", "VALIDATION_SET", "BHKType", "DateRange", "DoneStatus", "EnquiryType", "Facing", "FurnishStatus", "Gender", "LeadSource", "MaritalStatusType", "OfferType", "PossessionType", "PropertyPriceFilter", "PropertyStatus", "PurposeType", "SaleType", "environment", "env", "getEnvDetails", "isDomain", "extension", "defaultDomain", "envt", "getTenantName", "subDomain", "location", "href", "split", "length", "includes", "localStorage", "getItem", "getAppName", "appName", "getAppImages", "appFavIcon", "appLogo", "appText", "appFull", "appLoader", "getPages", "totalCount", "pageSize", "Math", "ceil", "capitalizeFirstLetter", "string", "char<PERSON>t", "toUpperCase", "slice", "patchFormControlValue", "form", "formControlName", "value", "patchValue", "getTimeZoneDate", "date", "userTimeZone", "returnType", "offset", "formats", "timeWithMeridiem", "dayMonthYear", "dayMonthYearText", "fullDateTime", "dateTimeDefault", "dateWithTime", "dateWithFullTime", "monthYear", "day<PERSON><PERSON><PERSON>", "ISO", "format", "utcOffset", "setTimeZoneDate", "baseUTcOffset", "updatedDate", "Date", "setHours", "formattedDate", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getSeconds", "Offset", "getSystemTimeOffset", "convertedTime", "convertToUtc", "patchTimeZoneDate", "dateString", "dateConverted", "totalOffsetMinutes", "sign", "startsWith", "offsetParts", "replace", "map", "Number", "adjustedDate", "getTime", "localTime", "localDate", "utc", "utcDate", "clone", "duration", "asMinutes", "setTimeZoneDateWithTime", "localeTimeString", "patchTimeZoneWithTime", "utcDateInput", "isNaN", "Error", "utcDateString", "toISOString", "localTimeString", "convertToLocalTime", "utcTime", "offsetMinutes", "setTimeZoneTime", "isNegative", "offsetHours", "getTimezoneOffset", "utcHours", "getUTCHours", "utcMinutes", "getUTCMinutes", "utcSeconds", "getUTCSeconds", "adjustedTotalMinutes", "adjustedHours", "floor", "adjustedMinutes", "formattedTime", "patchTime", "timeString", "currentDate", "time", "modifier", "hours", "minutes", "seconds", "baseoffset", "totalOffsetMillis", "adjustedTime", "getTimeZoneTime", "baseDate", "baseOffset", "isNegativeOffset", "toLocaleTimeString", "hour", "minute", "hour12", "offsetInMinutes", "abs", "getSystemTimeZoneId", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "convertedDate", "timezoneOffset", "offsetInMs", "changeCalendar", "startAt", "getUTCFullYear", "getUTCMonth", "getUTCDate", "offsetString", "formatDateToCustomString", "type", "monthNames", "day", "monthIndex", "year", "onPickerOpened", "observer", "MutationObserver", "highlightCustomToday", "setTimeout", "calendarElement", "document", "querySelector", "observe", "childList", "subtree", "calendarCells", "querySelectorAll", "formattedMinAllowedDate", "for<PERSON>ach", "cell", "aria<PERSON><PERSON><PERSON>", "getAttribute", "cellDate", "formattedAriaLabelDate", "classList", "contains", "cellContent", "add", "getISODateFormat", "dateTime", "getISOOnlyDate", "getISODate", "validate<PERSON>ll<PERSON>orm<PERSON><PERSON>s", "formGroup", "Object", "keys", "controls", "field", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "onlySelf", "formatCurrency", "amount", "locale", "currencySymbol", "toLocaleString", "sortArrayByKey", "array", "keyName", "sort", "a", "b", "getLeadStatusId", "status", "statusList", "filter", "item", "actionName", "id", "toggleValidation", "validationType", "validators", "clearValidators", "setValidators", "updateValueAndValidity", "getDateDiffInDays", "date1", "date2", "isSameOrBefore", "isEmptyObject", "obj", "constructor", "setPropertySubTypeList", "pType", "propertyTypeList", "isProject", "propertySubTypeList", "property", "prop", "displayName", "Set", "childTypes", "setPropertyBhkTypeList", "propertyBhkTypeList", "bhkType", "getPropertyTypeIds", "propertyType", "propertySubTypes", "selectedPropertyType", "find", "_a", "pSubType", "getPropertyTypeId", "propertySubType", "selectedPropertySubType", "istFormat", "toDate", "getFormattedDate", "isValidFilePath", "urlString", "getAWSImagePath", "imagePath", "s3ImageBucketURL", "getAreaUnit", "areaUnitList", "isTokenExpired", "idToken", "expirationTime", "JSON", "parse", "atob", "exp", "isExpiredToken", "isEmptyGuid", "guid", "getAssignedToDetails", "userInfo", "usersList", "returnNameOnly", "user", "firstName", "lastName", "_b", "matchValidator", "controlTwo", "match_password", "groupBy", "xs", "key", "reduce", "rv", "x", "reverse", "push", "getIndexes", "indexOf", "formatBudget", "budget", "currency", "formatInternationalCurrency", "symbol", "toString", "toFixed", "formatINRCurrency", "only<PERSON><PERSON>bers", "event", "keyCode", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "onlyNumbersWithDecimal", "currentValue", "isNumberKey", "isDecimalKey", "LeadTemplateMsg", "template", "leadData", "tenantName", "defaultCurrency", "header", "footer", "allUserList", "userData", "module", "combinedMsg", "transformedAttributes", "replacements", "match", "toLowerCase", "normalizeVar", "variable", "processReplacements", "msg", "Array", "isArray", "var", "normalizedVar", "replaceAll", "text", "trim", "processLeadData", "dataItem", "index", "remTime", "remTimeStr", "scheduledDate", "scheduleDate", "today", "attributes", "attribute", "attributeName", "name", "email", "phoneNumber", "timeZoneInfo", "_c", "properties", "_d", "_e", "getMSUrl", "serialNo", "join", "projects", "_f", "_g", "project", "_h", "_j", "_k", "_l", "_m", "title", "enquiry", "_o", "lowerBudget", "_p", "_q", "_r", "_s", "upperBudget", "_t", "_u", "_v", "_w", "enquiryTypes", "_x", "_y", "_z", "_0", "addresses", "_1", "address", "getLocationDetailsByObj", "alternateContactNo", "contactNo", "landLine", "referralName", "referralContactNo", "referralEmail", "companyName", "assignTo", "secondaryUserId", "closingManager", "sourcingManager", "channelPartners", "partner", "firmName", "designation", "_2", "saleType", "_3", "leadSource", "_4", "prospectSource", "_5", "_6", "subSource", "_7", "propertyTypes", "_8", "_9", "_10", "_11", "childType", "_12", "_13", "bhKs", "_14", "_15", "bhk", "getBHKDisplayString", "_16", "_17", "bhkTypes", "_18", "_19", "_20", "_21", "beds", "_22", "_23", "_24", "bed", "_25", "_26", "baths", "_27", "_28", "bath", "_29", "_30", "furnished", "_31", "floors", "_32", "_33", "_34", "_35", "offerType", "_36", "carpetArea", "_37", "_38", "carpetAreaUnit", "_39", "_40", "saleableArea", "_41", "_42", "saleableAreaUnit", "_43", "_44", "builtUpArea", "_45", "_46", "builtUpAreaUnit", "_47", "_48", "propertyArea", "_49", "_50", "propertyAreaUnit", "_51", "_52", "netArea", "_53", "_54", "netAreaUnit", "_55", "_56", "unitName", "_57", "clusterName", "_58", "purpose", "_59", "nationality", "possessionType", "possesionType", "possessionDate", "possesionDate", "_60", "agencies", "campaigns", "_61", "_62", "_63", "_64", "_65", "gender", "maritalStatus", "dateOfBirth", "WhatsAppTemplateMsg", "replaceData", "media", "s", "regex", "RegExp", "body", "headerValues", "values", "bodyValues", "message", "fileName", "mediaUrl", "mediaType", "PropertyTemplateMsg", "propertyData", "areaSizeUnits", "canViewOwnerDetails", "processPropertyData", "propertyLinks", "links", "link", "enquired<PERSON><PERSON>", "noOfBHK", "getBRDisplayString", "dimension", "area", "areaUnitId", "unit", "carpetAreaId", "buildUpArea", "buildUpAreaId", "saleableAreaId", "netAreaUnitId", "breadth", "isReadyToMove", "monetaryInfo", "expectedPrice", "brokerage", "brokerageCurrency", "notes", "aboutProperty", "propertyOwnerDetails", "owner", "phone", "furnishStatus", "facing", "leadName", "ProjectTemplateMsg", "projectData", "unitInfo", "replacementText", "processProjectData", "getValueBasedOnKey", "projectLinks", "currentStatus", "projectType", "possessionDateObj", "currentDateObj", "isPast", "builderDetail", "pointOfContact", "facings", "carpetAreaUnitId", "superBuildUpArea", "superBuildUpAreaUnit", "furnishingStatus", "undefined", "isListing", "userName", "preferred_username", "previewType", "sortAssignedUsers", "assignedUser", "allActiveUsers", "assignedUserIds", "assignedUsers", "unshift", "nameA", "nameB", "localeCompare", "unassignedUsers", "concat", "assignToSort", "assignedToUserId", "fullName", "userId", "sub", "loggedInUser", "activeUsers", "inactiveUsers", "isActive", "getTotalCountForReports", "items", "total", "projectTitle", "agencyName", "source", "allCount", "activeCount", "newCount", "pendingCount", "overdueCount", "callbackCount", "meetingScheduledCount", "siteVisitScheduledCount", "meetingDoneCount", "meetingNotDoneCount", "siteVisitDoneCount", "siteVisitNotDoneCount", "bookedCount", "notInterestedCount", "droppedCount", "meetingDoneUniqueCount", "meetingNotDoneUniqueCount", "siteVisitDoneUniqueCount", "siteVisitNotDoneUniqueCount", "averageWorkingHours", "callsInitiatedCount", "callsInitiatedLeadsCount", "whatsAppInitiatedCount", "whatsAppInitiatedLeadsCount", "emailsInitiatedCount", "emailsInitiatedLeadsCount", "smsInitiatedCount", "smsInitiatedLeadsCount", "statusEditsCount", "statusEditsLeadsCount", "formEditsCount", "formEditsLeadsCount", "notesAddedCount", "notesAddedLeadsCount", "callbackScheduledLeadsCount", "bookedLeadsCount", "notInterestedLeadsCount", "droppedLeadsCount", "hotLeadsCount", "warmLeadsCount", "coldLeadsCount", "escalatedLeadsCount", "highlightedLeadsCount", "aboutToConvertLeadsCount", "all", "active", "overdue", "callback", "busy", "toScheduleAMeeting", "followUp", "toScheduleSiteVisit", "planPostponed", "needMoreInfo", "notAnswered", "notReachable", "dropped", "notLooking", "ringingNotReceived", "wrongOrInvalidNo", "purchasedFromOthers", "meetingScheduled", "onCall", "online", "in<PERSON><PERSON>", "others", "notInterested", "differentLocation", "differentRequirements", "unmatchedBudget", "siteVisitScheduled", "firstVisit", "reVisit", "pending", "booked", "new", "notInterestedAfterMeetingDone", "notInterestedAfterSiteVisitDone", "droppedAfterMeetingDone", "droppedAfterSiteVisitDone", "incomingAnswered", "incomingMissed", "totalIncomingCalls", "outgoingAnswered", "outgoingNotConnected", "totalOutgoingCalls", "totalCalls", "totalTalkTime", "averageTalkTime", "maxTalkTime", "minTalkTime", "convertedDataCount", "calculateTotalForStatus", "statusId", "dataArray", "data", "dataCount", "sum", "calculateTotalForSubSource", "sourceName", "count", "calculateTotalForSource", "obj<PERSON><PERSON>y", "entries", "entry", "workingHours", "asSeconds", "formatDuration", "asHours", "secondsStr", "getDaysInMonth", "month", "UTC", "monthDays", "setUTCDate", "contains<PERSON>nly<PERSON><PERSON><PERSON><PERSON>", "input", "emojiRegex", "test", "object", "subLocality", "locality", "subCommunity", "community", "towerName", "city", "district", "state", "country", "postalCode", "addressParts", "part", "endsWith", "getLocalityDetailsByObj", "enquiredLocComponents", "enquiredLoc", "isStringSubsetInArray", "inputString", "arrayOfStrings", "i", "validateScheduleTime", "selectedTime", "setSeconds", "required", "currentTime", "invalidTime", "bhkNo", "isBHKText", "displayBHKNo", "displayType", "brNo", "isBRText", "getBRDisplay", "brNumbers", "n", "parseFloat", "br", "getBedsDisplay", "bedNo", "bedNumbers", "formattedBeds", "getBHKDisplay", "bhkNumbers", "formattedBHKs", "generateLeadSourcesArray", "leadSourcesArray", "snakeToCamel", "snakeCase", "components", "camelCaseString", "word", "getDateRange", "range", "startDate", "endDate", "Today", "Yesterday", "setDate", "Last7Days", "CurrentMonth", "TillDate", "hexToRgba", "hex", "opacity", "r", "parseInt", "g", "validOpacity", "min", "max", "atLeastTwoSelectedValidator", "teamUsers", "atLeastTwoSelected", "onFilterChanged", "nodes", "api", "forEachNodeAfterFilter", "node", "isSelected", "getSelectedNodes", "setSelected", "isUrl", "str", "urlPattern", "convertUrlsToLinks", "applyLinkColor", "urlRegex", "url", "clickableUrl", "atLeastOneSelectedValidator", "controlValue", "atLeastOneSelected", "generateFloorOptions", "buildHttpParams", "payload", "params", "indexes", "element", "append", "set", "generateEnumList", "enumData", "displayNames", "enumValue", "getFormattedLocation", "allowLandlineInput", "allowed", "handleCachedData", "storeName", "cache<PERSON>ey", "fetchLastModified", "fetchData", "buildRecord", "getItems", "dbN<PERSON>", "db", "Promise", "resolve", "reject", "request", "indexedDB", "open", "onsuccess", "result", "onerror", "error", "objectStoreNames", "newVersion", "version", "close", "onupgradeneeded", "upgradeDb", "target", "createObjectStore", "keyP<PERSON>", "console", "fresh", "storeCreateErr", "localData", "tx", "transaction", "store", "objectStore", "getReq", "txErr", "localLastModified", "lastModified", "serverLastModified", "err", "newRecord", "put", "oncomplete", "updateErr", "fetchErr"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Desktop\\reports\\Leadrat-Black-Web\\src\\app\\core\\utils\\common.util.ts"], "sourcesContent": ["import { HttpParams } from '@angular/common/http';\r\nimport {\r\n  AbstractControl,\r\n  FormControl,\r\n  FormGroup,\r\n  ValidationErrors,\r\n  ValidatorFn,\r\n} from '@angular/forms';\r\nimport * as moment from 'moment';\r\n\r\nimport {\r\n  EMPTY_GUID,\r\n  PROPERTY_BUDGET_FILTER,\r\n  VALIDATION_CLEAR,\r\n  VALIDATION_SET,\r\n} from 'src/app/app.constants';\r\nimport {\r\n  BHKType,\r\n  DateRange,\r\n  DoneStatus,\r\n  EnquiryType,\r\n  Facing,\r\n  FurnishStatus,\r\n  Gender,\r\n  LeadSource,\r\n  MaritalStatusType,\r\n  OfferType,\r\n  PossessionType,\r\n  PropertyPriceFilter,\r\n  PropertyStatus,\r\n  PurposeType,\r\n  SaleType,\r\n} from 'src/app/app.enum';\r\nimport { MasterAreaUnitType } from 'src/app/core/interfaces/master-data.interface';\r\nimport { environment as env } from 'src/environments/environment';\r\n\r\nexport const getEnvDetails = (isDomain: boolean = false): string => {\r\n  let extension: string = '';\r\n  let defaultDomain: string = '';\r\n  switch (env.envt) {\r\n    case 'dev':\r\n      extension = '.leadratd.com';\r\n      defaultDomain = 'alpha';\r\n      break;\r\n    case 'qa':\r\n      extension = '.leadrat.info';\r\n      defaultDomain = 'helium';\r\n      break;\r\n    case 'uat':\r\n      // extension = '.leadrat.app';\r\n      extension = '.dhinwa.com';\r\n      defaultDomain = 'uat';\r\n      break;\r\n    case 'prod':\r\n      extension = '.leadrat.com';\r\n      defaultDomain = 'black';\r\n      break;\r\n  }\r\n  return isDomain ? defaultDomain : extension;\r\n};\r\n\r\nexport const getTenantName = (): string => {\r\n  let subDomain: string = '';\r\n  if (location.href.split('.').length > 1) {\r\n    if (location.href.split('.')[0].includes('www')) {\r\n      subDomain = location.href.split('.')[1];\r\n    } else {\r\n      subDomain = location.href.split('.')[0].split('//')[1];\r\n    }\r\n  }\r\n  return (\r\n    subDomain || localStorage.getItem('subDomain') || getEnvDetails(true) || ''\r\n  );\r\n};\r\n\r\nexport const getAppName = () => {\r\n  let appName = '';\r\n  switch (env.envt) {\r\n    case 'dev':\r\n      appName = 'leadrat';\r\n      break;\r\n    case 'qa':\r\n      appName = 'leadrat';\r\n      break;\r\n    case 'uat':\r\n      appName = 'dhinwa';\r\n      break;\r\n    case 'prod':\r\n      appName = 'leadrat';\r\n      break;\r\n  }\r\n  return appName;\r\n};\r\n\r\nexport const getAppImages = () => {\r\n  let subDomain = getTenantName();\r\n\r\n  switch (true) {\r\n    // case env.envt == 'dev' && subDomain == 'carbon':\r\n    //   return {\r\n    //     appFavIcon: 'assets/images/favicon.ico',\r\n    //     appLogo: 'assets/images/app-logo-hj.svg',\r\n    //     appText: 'assets/images/app-logo-text-hj.svg',\r\n    //     appFull: 'assets/images/app-logo-text-hj-blue.svg',\r\n    //     appLoader: 'assets/images/app-text-shadow.svg',\r\n    //   };\r\n\r\n    case env.envt == 'qa' && subDomain == 'helium':\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-kunj4u.svg',\r\n        appText: 'assets/images/app-text-kunj4u.svg',\r\n        appFull: 'assets/images/app-logo-text-kunj4u.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n\r\n    // case env.envt == 'uat' && subDomain == 'app':\r\n    //   return {\r\n    //     appFavIcon: 'assets/images/favicon.ico',\r\n    //     appLogo: 'assets/images/app-logo-dhinwa.svg',\r\n    //     appText: 'assets/images/app-logo-text-dhinwa.svg',\r\n    //     appFull: 'assets/images/app-logo-text-dhinwa-red.svg',\r\n    //     appLoader: 'assets/images/app-text-dhinwa.svg',\r\n    //   };\r\n\r\n    case env.envt == 'prod' && subDomain == 'hj':\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-hj.svg',\r\n        appText: 'assets/images/app-logo-text-hj.svg',\r\n        appFull: 'assets/images/app-logo-text-hj-blue.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n\r\n    case env.envt == 'prod' && subDomain == 'prowinproperties':\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-prowin.svg',\r\n        appText: 'assets/images/app-text-prowin.svg',\r\n        appFull: 'assets/images/app-logo-text-prowin.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n    case env.envt == 'prod' && subDomain == 'realtors-hub':\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-rh.svg',\r\n        appText: 'assets/images/app-text-rh.svg',\r\n        appFull: 'assets/images/app-logo-text-rh.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n    case env.envt == 'prod' && subDomain == 'kunj4u':\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-kunj4u.svg',\r\n        appText: 'assets/images/app-text-kunj4u.svg',\r\n        appFull: 'assets/images/app-logo-text-kunj4u.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n    default:\r\n      return {\r\n        appFavIcon: 'assets/images/favicon.ico',\r\n        appLogo: 'assets/images/app-logo-green.svg',\r\n        appText: 'assets/images/app-logo-text-green.svg',\r\n        appFull: 'assets/images/app-logo-text-green.svg',\r\n        appLoader: 'assets/images/app-text-shadow.svg',\r\n      };\r\n  }\r\n};\r\n\r\nexport const getPages = (totalCount: number, pageSize: number) =>\r\n  Math.ceil(totalCount / pageSize);\r\n\r\nexport const capitalizeFirstLetter = (string: string) =>\r\n  string.charAt(0).toUpperCase() + string.slice(1);\r\n\r\nexport const patchFormControlValue = (\r\n  form: FormGroup,\r\n  formControlName: string,\r\n  value: any\r\n) => {\r\n  form.patchValue({ [formControlName]: value });\r\n};\r\n\r\nexport const getTimeZoneDate = (\r\n  date: Date,\r\n  userTimeZone: string,\r\n  returnType?: string\r\n) => {\r\n  // 2024-10-03T10:00:00Z\r\n  const offset = (userTimeZone?.charAt(0) === '-' ? '' : '+') + userTimeZone;\r\n\r\n  const formats: { [key: string]: string } = {\r\n    timeWithMeridiem: 'hh:mm A',\r\n    dayMonthYear: 'DD-MM-YYYY',\r\n    dayMonthYearText: 'DD MMM, YYYY',\r\n    fullDateTime: 'DD-MM-YYYY hh:mm:ss A',\r\n    dateTimeDefault: 'DD-MM-YYYY hh:mm A',\r\n    dateWithTime: 'DD MMM, YYYY | hh:mm A',\r\n    dateWithFullTime: 'DD MMM, YYYY | hh:mm:ss A',\r\n    monthYear: 'MMM yyyy',\r\n    dayMonth: 'DD MMM',\r\n    ISO: 'YYYY-MM-DDTHH:mm:ss',\r\n  };\r\n  const format = formats[returnType] || formats['dateTimeDefault'];\r\n  return moment(date).utcOffset(offset).format(format);\r\n};\r\n\r\nexport const setTimeZoneDate = (date: Date, baseUTcOffset: string) => {\r\n  if (!date) return null;\r\n  const updatedDate = new Date(date);\r\n  updatedDate.setHours(0, 0, 0, 0);\r\n  const formattedDate =\r\n    updatedDate.getFullYear() +\r\n    '-' +\r\n    String(updatedDate.getMonth() + 1).padStart(2, '0') +\r\n    '-' +\r\n    String(updatedDate.getDate()).padStart(2, '0') +\r\n    'T' +\r\n    String(updatedDate.getHours()).padStart(2, '0') +\r\n    ':' +\r\n    String(updatedDate.getMinutes()).padStart(2, '0') +\r\n    ':' +\r\n    String(updatedDate.getSeconds()).padStart(2, '0');\r\n\r\n  const Offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\r\n  const convertedTime = convertToUtc(formattedDate, Offset);\r\n  return convertedTime;\r\n};\r\n\r\nexport const patchTimeZoneDate = (\r\n  dateString: string,\r\n  baseUTcOffset: string\r\n) => {\r\n  // 2024-10-15T12:00:00Z\r\n  if (!dateString) {\r\n    return null;\r\n  }\r\n\r\n  const date = dateConverted(new Date(dateString));\r\n\r\n  let totalOffsetMinutes: number;\r\n\r\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\r\n  const sign = offset.startsWith('-') ? -1 : 1;\r\n  const offsetParts = offset.replace('-', '').split(':').map(Number);\r\n  totalOffsetMinutes = sign * (offsetParts[0] * 60 + offsetParts[1]);\r\n\r\n  const adjustedDate = new Date(\r\n    date.getTime() + totalOffsetMinutes * 60 * 1000\r\n  );\r\n\r\n  // Tue Oct 15 2024 00:00:00 GMT+1400 (Line Islands Time)\r\n  return adjustedDate;\r\n};\r\n\r\nexport const convertToUtc = (localTime: string, baseUTcOffset: string) => {\r\n  const localDate = moment.utc(localTime);\r\n  const utcDate = localDate\r\n    .clone()\r\n    .utcOffset(-moment.duration(baseUTcOffset).asMinutes());\r\n  return utcDate.format('YYYY-MM-DDTHH:mm:ss[Z]');\r\n};\r\n\r\nexport const setTimeZoneDateWithTime = (date: Date, baseUTcOffset: string) => {\r\n  if (!date) return null;\r\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\r\n  const localeTimeString = new Date(date);\r\n  const formattedDate =\r\n    localeTimeString.getFullYear() +\r\n    '-' +\r\n    String(localeTimeString.getMonth() + 1).padStart(2, '0') +\r\n    '-' +\r\n    String(localeTimeString.getDate()).padStart(2, '0') +\r\n    'T' +\r\n    String(localeTimeString.getHours()).padStart(2, '0') +\r\n    ':' +\r\n    String(localeTimeString.getMinutes()).padStart(2, '0') +\r\n    ':' +\r\n    String(localeTimeString.getSeconds()).padStart(2, '0');\r\n  const convertedTime = convertToUtc(formattedDate, offset);\r\n  return convertedTime;\r\n};\r\n\r\nexport const patchTimeZoneWithTime = (\r\n  utcDateInput: Date,\r\n  baseUTcOffset: string\r\n) => {\r\n  if (!utcDateInput) return null;\r\n  const utcDate = new Date(utcDateInput);\r\n  if (isNaN(utcDate.getTime())) {\r\n    throw new Error('Invalid Date');\r\n  }\r\n\r\n  if (!baseUTcOffset) return utcDate;\r\n  const utcDateString = utcDate.toISOString();\r\n  const localTimeString = convertToLocalTime(utcDateString, baseUTcOffset);\r\n  return new Date(localTimeString);\r\n};\r\n\r\nexport const convertToLocalTime = (utcTime: string, offset: string) => {\r\n  const utcDate = moment.utc(utcTime);\r\n  const offsetMinutes = moment.duration(offset).asMinutes();\r\n  const localTime = utcDate.clone().utcOffset(offsetMinutes);\r\n  return localTime.format('YYYY-MM-DDTHH:mm:ss');\r\n};\r\n\r\nexport const setTimeZoneTime = (date: Date, baseUTcOffset: string) => {\r\n  if (!date) return null;\r\n\r\n  const offset = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\r\n  const isNegative = offset.startsWith('-');\r\n  const [offsetHours, offsetMinutes] = offset\r\n    .replace('+', '')\r\n    .replace('-', '')\r\n    .split(':')\r\n    .map(Number);\r\n  const totalOffsetMinutes = offsetHours * 60 + offsetMinutes;\r\n  const utcDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);\r\n\r\n  const utcHours = utcDate.getUTCHours();\r\n  const utcMinutes = utcDate.getUTCMinutes();\r\n  const utcSeconds = utcDate.getUTCSeconds();\r\n\r\n  let adjustedTotalMinutes;\r\n  if (isNegative) {\r\n    adjustedTotalMinutes = utcHours * 60 + utcMinutes + totalOffsetMinutes;\r\n  } else {\r\n    adjustedTotalMinutes = utcHours * 60 + utcMinutes - totalOffsetMinutes;\r\n  }\r\n\r\n  const adjustedHours = Math.floor(adjustedTotalMinutes / 60) % 24;\r\n  const adjustedMinutes = adjustedTotalMinutes % 60;\r\n\r\n  const formattedTime = `${String((adjustedHours + 24) % 24).padStart(\r\n    2,\r\n    '0'\r\n  )}:${String((adjustedMinutes + 60) % 60).padStart(2, '0')}:${String(\r\n    utcSeconds\r\n  ).padStart(2, '0')}`;\r\n  return formattedTime;\r\n};\r\n\r\nexport const patchTime = (timeString: string, offset: string) => {\r\n  //time - 15:30:00\r\n  if (!timeString) return null;\r\n  const currentDate = new Date();\r\n  const [time, modifier] = timeString.split(' ');\r\n  let [hours, minutes, seconds] = time.split(':').map(Number);\r\n\r\n  if (modifier === 'PM' && hours < 12) hours += 12;\r\n  if (modifier === 'AM' && hours === 12) hours = 0;\r\n\r\n  currentDate.setHours(hours, minutes, seconds || 0);\r\n  const baseoffset = offset ? offset : getSystemTimeOffset();\r\n  const isNegative = baseoffset.startsWith('-');\r\n\r\n  const [offsetHours, offsetMinutes] = baseoffset\r\n    .replace('+', '')\r\n    .replace('-', '')\r\n    .split(':')\r\n    .map(Number);\r\n  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;\r\n\r\n  const adjustedTime = isNegative\r\n    ? new Date(currentDate.getTime() - totalOffsetMillis)\r\n    : new Date(currentDate.getTime() + totalOffsetMillis);\r\n  // Thu Oct 24 2024 11:51:28 GMT+0530 (India Standard Time)\r\n  return adjustedTime;\r\n};\r\n\r\nexport const getTimeZoneTime = (timeString: string, offset: string | null) => {\r\n  if (!timeString) return null;\r\n\r\n  const [time, modifier] = timeString.split(' ');\r\n  let [hours, minutes, seconds] = time.split(':').map(Number);\r\n\r\n  if (modifier === 'PM' && hours < 12) hours += 12;\r\n  if (modifier === 'AM' && hours === 12) hours = 0;\r\n\r\n  const baseDate = new Date();\r\n  baseDate.setHours(hours, minutes, seconds || 0);\r\n\r\n  const baseOffset = offset ? offset : getSystemTimeOffset();\r\n  const isNegativeOffset = baseOffset.startsWith('-');\r\n\r\n  const [offsetHours, offsetMinutes] = baseOffset\r\n    .replace('+', '')\r\n    .replace('-', '')\r\n    .split(':')\r\n    .map(Number);\r\n  const totalOffsetMillis = (offsetHours * 60 * 60 + offsetMinutes * 60) * 1000;\r\n\r\n  const adjustedDate = isNegativeOffset\r\n    ? new Date(baseDate.getTime() - totalOffsetMillis)\r\n    : new Date(baseDate.getTime() + totalOffsetMillis);\r\n\r\n  return adjustedDate.toLocaleTimeString('en-US', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: true,\r\n  });\r\n};\r\n\r\nexport const getSystemTimeOffset: () => string = () => {\r\n  const offsetInMinutes = new Date().getTimezoneOffset();\r\n  const hours = Math.floor(Math.abs(offsetInMinutes) / 60);\r\n  const minutes = Math.abs(offsetInMinutes) % 60;\r\n  const sign = offsetInMinutes <= 0 ? '' : '-';\r\n  return `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(\r\n    2,\r\n    '0'\r\n  )}:00`;\r\n};\r\n\r\nexport const getSystemTimeZoneId: () => string = () => {\r\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n};\r\n\r\nexport const dateConverted = (date: Date): Date => {\r\n  if (!date) return null;\r\n\r\n  let convertedDate = new Date(date);\r\n  const timezoneOffset = convertedDate.getTimezoneOffset();\r\n  const offsetInMs = timezoneOffset * 60 * 1000;\r\n  convertedDate = new Date(convertedDate.getTime() + offsetInMs);\r\n  return convertedDate;\r\n};\r\n\r\nexport const changeCalendar = (baseUTcOffset: string): Date => {\r\n  const startAt = new Date();\r\n\r\n  const utcTime = new Date(\r\n    startAt.getUTCFullYear(),\r\n    startAt.getUTCMonth(),\r\n    startAt.getUTCDate(),\r\n    startAt.getUTCHours(),\r\n    startAt.getUTCMinutes(),\r\n    startAt.getUTCSeconds()\r\n  );\r\n  const offsetString = baseUTcOffset ? baseUTcOffset : getSystemTimeOffset();\r\n  const [hours, minutes] = offsetString.split(':').map(Number);\r\n  const offsetMinutes = hours * 60 + (minutes || 0);\r\n  const adjustedTime = new Date(utcTime.getTime() + offsetMinutes * 60000);\r\n  return adjustedTime;\r\n};\r\n\r\nexport const formatDateToCustomString = (date: Date, type?: string): string => {\r\n  const monthNames = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December',\r\n  ];\r\n\r\n  const day = date.getDate();\r\n  const monthIndex = date.getMonth();\r\n  const year = date.getFullYear();\r\n\r\n  if (type === 'month') {\r\n    return `${monthNames[monthIndex]} ${year}`;\r\n  }\r\n  return `${day} ${monthNames[monthIndex]} ${year}`;\r\n};\r\n\r\nexport const onPickerOpened = (date: Date, type?: string): void => {\r\n  const observer = new MutationObserver(() => {\r\n    highlightCustomToday(date, type);\r\n  });\r\n\r\n  setTimeout(() => {\r\n    const calendarElement = document.querySelector('.owl-dt-calendar');\r\n    if (calendarElement) {\r\n      observer.observe(calendarElement, { childList: true, subtree: true });\r\n      highlightCustomToday(date, type);\r\n    }\r\n  }, 100);\r\n};\r\n\r\nconst highlightCustomToday = (date: Date, type?: string): void => {\r\n  const calendarCells = document.querySelectorAll('.owl-dt-calendar-cell');\r\n  const formattedMinAllowedDate = formatDateToCustomString(date, type);\r\n\r\n  calendarCells.forEach((cell) => {\r\n    const ariaLabel = cell.getAttribute('aria-label');\r\n\r\n    if (ariaLabel) {\r\n      const cellDate = new Date(ariaLabel);\r\n\r\n      if (!isNaN(cellDate.getTime())) {\r\n        const formattedAriaLabelDate = formatDateToCustomString(cellDate, type);\r\n\r\n        if (formattedAriaLabelDate === formattedMinAllowedDate) {\r\n          if (!cell.classList.contains('owl-dt-calendar-cell-selected')) {\r\n            const cellContent = cell.querySelector(\r\n              '.owl-dt-calendar-cell-content'\r\n            );\r\n            if (cellContent) {\r\n              cellContent.classList.add('custom-today');\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nexport const getISODateFormat = (dateTime: Date) => {\r\n  if (!dateTime) {\r\n    return null;\r\n  }\r\n  return `${moment(dateTime).format('YYYY-MM-DD')}T${moment(dateTime).format(\r\n    'HH:mm'\r\n  )}:00.000Z`;\r\n};\r\n\r\nexport const getISOOnlyDate = (dateTime: any) => {\r\n  if (!dateTime) {\r\n    return null;\r\n  }\r\n  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00.000Z`;\r\n};\r\nexport const getISODate = (dateTime: Date) => {\r\n  if (!dateTime) {\r\n    return null;\r\n  }\r\n  return `${moment(dateTime).format('YYYY-MM-DD')}T00:00:00`;\r\n};\r\nexport const validateAllFormFields = (formGroup: FormGroup) => {\r\n  Object.keys(formGroup.controls).forEach((field) => {\r\n    const control = formGroup.get(field);\r\n    if (control instanceof FormControl) {\r\n      control.markAsTouched({ onlySelf: true });\r\n    } else if (control instanceof FormGroup) {\r\n      validateAllFormFields(control);\r\n    }\r\n  });\r\n};\r\nexport const formatCurrency = (\r\n  amount: string,\r\n  locale: string,\r\n  currencySymbol: string\r\n) => {\r\n  if (amount == null || amount == '') return '';\r\n  return `${currencySymbol} ${Number(amount).toLocaleString(locale)}`;\r\n};\r\nexport const sortArrayByKey = (array: any[], keyName: string) => {\r\n  return array.sort((a: any, b: any) => (a[keyName] > b[keyName] ? 1 : -1));\r\n};\r\nexport const getLeadStatusId = (status: string, statusList: any) =>\r\n  statusList.filter((item: any) => item.actionName == status)[0].id;\r\nexport const toggleValidation = (\r\n  validationType: string,\r\n  formGroup: FormGroup,\r\n  field: string,\r\n  validators?: Array<ValidatorFn>\r\n) => {\r\n  const control = formGroup.get(field);\r\n  if (control instanceof FormControl) {\r\n    switch (validationType) {\r\n      case VALIDATION_CLEAR:\r\n        control.clearValidators();\r\n        break;\r\n      case VALIDATION_SET:\r\n        control.setValidators(validators);\r\n        break;\r\n    }\r\n    control.updateValueAndValidity();\r\n  }\r\n};\r\nexport const getDateDiffInDays: any = (date1: any, date2: any) =>\r\n  moment(date1).isSameOrBefore(moment(date2), 'days');\r\nexport const isEmptyObject = (obj: any) =>\r\n  Object.keys(obj).length === 0 && obj.constructor === Object;\r\nexport const setPropertySubTypeList: any = (\r\n  pType: string,\r\n  propertyTypeList: any[],\r\n  isProject: boolean = false\r\n) => {\r\n  let propertySubTypeList;\r\n  if (!propertyTypeList?.length) {\r\n    return;\r\n  }\r\n  const [property]: any = propertyTypeList.filter(\r\n    (prop: any) => prop.displayName === pType\r\n  );\r\n  if (property?.displayName === 'Residential' && !isProject) {\r\n    propertySubTypeList =\r\n      [\r\n        ...new Set(property?.childTypes.map((item: any) => item.displayName)),\r\n      ].map((item: any) => {\r\n        return { displayName: item };\r\n      }) || [];\r\n  } else {\r\n    propertySubTypeList = property?.childTypes || [];\r\n  }\r\n  return propertySubTypeList;\r\n};\r\nexport const setPropertyBhkTypeList: any = (\r\n  pType: string,\r\n  propertyTypeList: any[]\r\n) => {\r\n  let propertyBhkTypeList;\r\n  const [property]: any = propertyTypeList.filter(\r\n    (prop: any) => prop.displayName === pType\r\n  );\r\n  if (property?.displayName === 'Residential') {\r\n    propertyBhkTypeList =\r\n      [...new Set(property?.childTypes.map((item: any) => item.bhkType))]\r\n        .map((item: any) => {\r\n          return item;\r\n        })\r\n        .filter((item: string) => {\r\n          if (item != null) {\r\n            return { bhkType: item };\r\n          }\r\n          return item;\r\n        }) || [];\r\n  }\r\n  return propertyBhkTypeList;\r\n};\r\n\r\nexport const getPropertyTypeIds = (\r\n  propertyTypeList: any[],\r\n  propertyType: string,\r\n  propertySubTypes: string[]\r\n): any[] => {\r\n  const selectedPropertyType = propertyTypeList.find(\r\n    (pType) => pType.displayName === propertyType\r\n  );\r\n\r\n  if (!selectedPropertyType?.childTypes?.length) return [];\r\n\r\n  if (selectedPropertyType.displayName === 'Residential') {\r\n    return selectedPropertyType.childTypes\r\n      .filter((pSubType: any) =>\r\n        propertySubTypes.includes(pSubType.displayName)\r\n      )\r\n      .map((pSubType: any) => pSubType.id);\r\n  } else {\r\n    return selectedPropertyType.childTypes\r\n      .filter((pSubType: any) =>\r\n        propertySubTypes.includes(pSubType.displayName)\r\n      )\r\n      .map((pSubType: any) => pSubType.id);\r\n  }\r\n};\r\n\r\nexport const getPropertyTypeId: any = (\r\n  propertyTypeList: any[],\r\n  propertyType: string,\r\n  propertySubType: string\r\n) => {\r\n  const [selectedPropertyType]: any = propertyTypeList.filter(\r\n    (pType: any) => pType.displayName == propertyType\r\n  );\r\n  if (!selectedPropertyType?.childTypes.length) return;\r\n  else if (\r\n    selectedPropertyType?.displayName == 'Residential' &&\r\n    propertySubType != 'Plot'\r\n  ) {\r\n    const [selectedPropertySubType]: any =\r\n      selectedPropertyType?.childTypes.filter(\r\n        (pSubType: any) => pSubType.displayName == propertySubType\r\n      );\r\n    return selectedPropertySubType?.id;\r\n  } else {\r\n    const [selectedPropertySubType]: any =\r\n      selectedPropertyType?.childTypes.filter(\r\n        (pSubType: any) => pSubType.displayName == propertySubType\r\n      );\r\n    return selectedPropertySubType?.id;\r\n  }\r\n};\r\n\r\nexport const istFormat: any = (utc: any) => moment.utc(utc).toDate();\r\nexport const getFormattedDate = (dateTime: Date, format: string) =>\r\n  moment(new Date(dateTime)).utcOffset(0).format(format);\r\nexport const isValidFilePath = (urlString: string) =>\r\n  urlString.includes('data:');\r\n\r\nexport const getAWSImagePath = (imagePath: string) => {\r\n  return isValidFilePath(imagePath)\r\n    ? imagePath\r\n    : `${env.s3ImageBucketURL}${imagePath}`;\r\n};\r\nexport const getAreaUnit: any = (\r\n  id: string,\r\n  areaUnitList: Array<MasterAreaUnitType>\r\n) => areaUnitList.filter((item: MasterAreaUnitType) => item.id === id)[0];\r\n\r\nexport const isTokenExpired = () => {\r\n  const idToken = localStorage.getItem('idToken');\r\n  if (idToken && idToken.length) {\r\n    const expirationTime = JSON.parse(atob(idToken.split('.')[1])).exp;\r\n    const isExpiredToken =\r\n      Math.floor(new Date().getTime() / 1000) >= expirationTime;\r\n    return isExpiredToken;\r\n  }\r\n  return true;\r\n};\r\n\r\nexport const isEmptyGuid = (guid: string) => guid === EMPTY_GUID;\r\n\r\nexport const getAssignedToDetails = (\r\n  userInfo: string,\r\n  usersList: any,\r\n  returnNameOnly: boolean = false\r\n) => {\r\n  if (!userInfo || isEmptyGuid(userInfo) || !usersList?.length) return null;\r\n\r\n  const user = usersList?.filter(\r\n    (user: any) =>\r\n      user.id == userInfo ||\r\n      (user.firstName == userInfo?.split(' ')?.[0] &&\r\n        user.lastName == userInfo?.split(' ')?.[1]) ||\r\n      `${user.firstName} ${user.lastName}` === userInfo\r\n  )?.[0];\r\n\r\n  return user\r\n    ? returnNameOnly\r\n      ? `${user.firstName} ${user.lastName}`\r\n      : user\r\n    : null;\r\n};\r\n\r\nexport const matchValidator = (\r\n  control: AbstractControl,\r\n  controlTwo: AbstractControl\r\n): ValidatorFn => {\r\n  return () => {\r\n    if (control.value !== controlTwo.value)\r\n      return { match_password: 'Password and Confirm Password does not match' };\r\n    return null;\r\n  };\r\n};\r\n\r\nexport const groupBy = (xs: any, key: any) =>\r\n  xs.reduce((rv: any, x: any) => {\r\n    (rv[x[key]] = rv[x[key]] || []).reverse().push(x);\r\n    return rv;\r\n  }, {});\r\n\r\nexport const getIndexes = (property: string, array: any[]) => {\r\n  if (\r\n    property === 'Source' ||\r\n    property === 'Sources' ||\r\n    property === 'LeadSources'\r\n  ) {\r\n    array = array.map((item) => {\r\n      return LeadSource[item];\r\n    });\r\n  } else if (property === 'BHKTypes') {\r\n    array = array.map((item) => {\r\n      return BHKType[item];\r\n    });\r\n  } else if (property === 'Budget') {\r\n    array = array.map((item) => {\r\n      return PROPERTY_BUDGET_FILTER.indexOf(item);\r\n    });\r\n  } else if (property === 'PriceRange') {\r\n    array = array.map((item) => {\r\n      return PropertyPriceFilter[item];\r\n    });\r\n  } else if (property === 'enquiredFor') {\r\n    array = array.map((item) => {\r\n      return EnquiryType[item];\r\n    });\r\n  } else if (property === 'MeetingOrVisitStatuses') {\r\n    array = array.map((item) => {\r\n      return DoneStatus[item];\r\n    });\r\n  } else if (property === 'FurnishStatuses' || property === 'Furnished') {\r\n    array = array.map((item) => {\r\n      return FurnishStatus[item];\r\n    });\r\n  } else if (property === 'OfferTypes') {\r\n    array = array.map((item) => {\r\n      return OfferType[item];\r\n    });\r\n  } else if (property === 'Purposes') {\r\n    array = array.map((item) => {\r\n      return PurposeType[item];\r\n    });\r\n  }\r\n  return array;\r\n};\r\n\r\nexport const formatBudget = (budget: number, currency: string | null) => {\r\n  const formatInternationalCurrency = (budget: number, symbol: string) => {\r\n    if (budget < 1000) {\r\n      return symbol + ' ' + budget?.toString();\r\n    } else if (budget >= 1000 && budget < 1000000) {\r\n      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';\r\n    } else if (budget >= 1000000 && budget < 1000000000) {\r\n      return symbol + ' ' + (budget / 1000000).toFixed(2) + ' M';\r\n    } else {\r\n      return symbol + ' ' + (budget / 1000000000).toFixed(2) + ' B';\r\n    }\r\n  };\r\n  const formatINRCurrency = (budget: number, symbol: string) => {\r\n    if (budget < 1000) {\r\n      return symbol + ' ' + budget?.toString();\r\n    } else if (budget >= 1000 && budget < 100000) {\r\n      return symbol + ' ' + (budget / 1000).toFixed(2) + ' K';\r\n    } else if (budget >= 100000 && budget < 10000000) {\r\n      return symbol + ' ' + (budget / 100000).toFixed(2) + ' Lacs';\r\n    } else {\r\n      return symbol + ' ' + (budget / 10000000).toFixed(2) + ' Cr';\r\n    }\r\n  };\r\n\r\n  if (currency === null) {\r\n    return budget?.toString();\r\n  }\r\n\r\n  if (currency?.includes('INR')) {\r\n    return formatINRCurrency(budget, 'INR');\r\n  } else {\r\n    return formatInternationalCurrency(budget, currency);\r\n  }\r\n};\r\n\r\nexport const onlyNumbers = (event: any) => {\r\n  const keyCode = event.keyCode;\r\n  const excludedKeys = [8, 37, 39, 46];\r\n  if (\r\n    !(\r\n      (keyCode >= 48 && keyCode <= 57) ||\r\n      (keyCode >= 96 && keyCode <= 105) ||\r\n      excludedKeys.includes(keyCode)\r\n    )\r\n  ) {\r\n    event.preventDefault();\r\n  }\r\n};\r\n\r\nexport const onlyNumbersWithDecimal = (event: any, currentValue: string) => {\r\n  const keyCode = event.keyCode;\r\n  const excludedKeys = [8, 9, 37, 39, 46];\r\n  const isNumberKey = (keyCode >= 48 && keyCode <= 57) || (keyCode >= 96 && keyCode <= 105);\r\n  const isDecimalKey = keyCode === 190 || keyCode === 110;\r\n  if (!(isNumberKey || isDecimalKey || excludedKeys.includes(keyCode))) {\r\n    event.preventDefault();\r\n    return;\r\n  }\r\n  if ((keyCode === 190 || keyCode === 110) && currentValue.includes('.')) {\r\n    event.preventDefault();\r\n  }\r\n};\r\n\r\nexport const LeadTemplateMsg = (\r\n  template: any,\r\n  leadData: any,\r\n  tenantName: string,\r\n  defaultCurrency: string,\r\n  header?: any,\r\n  footer?: any,\r\n  allUserList?: any,\r\n  userData?: any,\r\n  currentDate?: any,\r\n  module?: string\r\n) => {\r\n  let combinedMsg = '';\r\n  const transformedAttributes: any = {};\r\n\r\n  let replacements: any = [];\r\n\r\n  template = template?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  header = header?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  footer = footer?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n\r\n  const normalizeVar = (variable: string) =>\r\n    variable.toLowerCase()?.replace(/[\\s-]+/g, '');\r\n\r\n  const processReplacements = (msg: any, replacements: any) => {\r\n    if (msg) {\r\n      replacements.forEach((item: any) => {\r\n        if (Array.isArray(item.var)) {\r\n          item.var.forEach((variable: string) => {\r\n            const normalizedVar = normalizeVar(variable);\r\n            msg = msg?.replaceAll(normalizedVar, item?.text?.trim());\r\n          });\r\n        } else {\r\n          const normalizedVar = normalizeVar(item.var);\r\n          msg = msg?.replaceAll(normalizedVar, item?.text?.trim());\r\n        }\r\n      });\r\n    }\r\n\r\n    return msg;\r\n  };\r\n\r\n  const processLeadData = (dataItem: any, index: number) => {\r\n    let date;\r\n    let remTime;\r\n    let remTimeStr;\r\n    if (dataItem?.scheduledDate || dataItem?.scheduleDate) {\r\n      date = new Date(dataItem?.scheduledDate || dataItem?.scheduleDate);\r\n      let today = new Date(currentDate);\r\n\r\n      remTime = date.getTime() - today.getTime();\r\n      remTimeStr = '';\r\n\r\n      if (remTime > 0) {\r\n        remTimeStr = Math.floor(remTime / (1000 * 60 * 60)) + ' hours ';\r\n        remTime = remTime % (1000 * 60 * 60);\r\n        remTimeStr += Math.floor(remTime / (1000 * 60)) + ' mins';\r\n      }\r\n    }\r\n\r\n    let msg = template;\r\n    if (dataItem.attributes?.length) {\r\n      for (const attribute of dataItem.attributes) {\r\n        if (!attribute?.attributeName.startsWith('Is')) {\r\n          transformedAttributes[attribute?.attributeName] = attribute.value;\r\n        }\r\n      }\r\n    }\r\n    replacements = [\r\n      { var: '#leadName#', text: dataItem?.name?.replace(/\\s+/g, ' ').trim() },\r\n      {\r\n        var: '#userName#',\r\n        text: `${userData?.firstName} ${userData?.lastName}`,\r\n      },\r\n      { var: '#userEmail#', text: userData?.email },\r\n      { var: '#userMobile#', text: userData?.phoneNumber },\r\n      { var: '#tenantName#', text: tenantName || '' },\r\n      {\r\n        var: ['#date#', '#Schedule Date#'],\r\n        text:\r\n          dataItem?.scheduledDate || dataItem?.scheduleDate\r\n            ? getTimeZoneDate(\r\n              dataItem?.scheduledDate || dataItem?.scheduleDate,\r\n              userData?.timeZoneInfo?.baseUTcOffset,\r\n              'dayMonthYear'\r\n            )\r\n            : '',\r\n      },\r\n      {\r\n        var: '#PropertyMicrositeUrl#',\r\n        text: dataItem?.properties?.length\r\n          ? dataItem?.properties\r\n            ?.map((property: any) => getMSUrl(property.serialNo))\r\n            .join(' , ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#ProjectMicrositeUrl#',\r\n        text: dataItem?.projects?.length\r\n          ? dataItem?.projects\r\n            ?.map((project: any) => getMSUrl(project.serialNo, true))\r\n            .join(' , ')\r\n          : '',\r\n      },\r\n      {\r\n        var: ['#time#', '#ScheduleTime#'],\r\n        text:\r\n          dataItem?.scheduledDate || dataItem?.scheduleDate\r\n            ? getTimeZoneDate(\r\n              dataItem?.scheduledDate || dataItem?.scheduleDate,\r\n              userData?.timeZoneInfo?.baseUTcOffset,\r\n              'timeWithMeridiem'\r\n            )\r\n            : '',\r\n      },\r\n      {\r\n        var: ['#projectName#', '#Projects#'],\r\n        text: dataItem?.projects?.length\r\n          ? dataItem?.projects?.map((project: any) => project.name).join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: ['#propertyName#', '#Properties#'],\r\n        text: dataItem?.properties?.length\r\n          ? dataItem?.properties\r\n            ?.map((property: any) => property.title)\r\n            .join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#LowerBudget#',\r\n        text: dataItem?.enquiry?.lowerBudget\r\n          ? dataItem?.enquiry?.lowerBudget +\r\n          ' (' +\r\n          formatBudget(\r\n            dataItem?.enquiry?.lowerBudget,\r\n            dataItem?.enquiry?.currency || defaultCurrency\r\n          ) +\r\n          ')'\r\n          : '',\r\n      },\r\n      {\r\n        var: ['#priceRange#', '#UpperBudget#'],\r\n        text: dataItem?.enquiry?.upperBudget\r\n          ? dataItem?.enquiry?.upperBudget +\r\n          ' (' +\r\n          formatBudget(\r\n            dataItem?.enquiry?.upperBudget,\r\n            dataItem?.enquiry?.currency || defaultCurrency\r\n          ) +\r\n          ')'\r\n          : '',\r\n      },\r\n      {\r\n        var: [\r\n          '#enquiryType#',\r\n          '#EnquiryTypes#',\r\n          '#EnquiredFor#',\r\n          '#Enquired For#',\r\n          '#SaleType#',\r\n          '#Sale Type#',\r\n        ],\r\n        text: dataItem?.enquiry?.enquiryTypes\r\n          ? [...dataItem?.enquiry?.enquiryTypes]\r\n            ?.map((type: any) => EnquiryType[type])\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      { var: '#link#', text: 'link' },\r\n      {\r\n        var: '#remainingTime#',\r\n        text: remTime > 0 ? remTimeStr : '00:00 hrs',\r\n      },\r\n      {\r\n        var: [\r\n          '#enquiredLocation#',\r\n          '#Addresses#',\r\n          '#Address#',\r\n          '#PreferredLocation#',\r\n        ],\r\n        text: dataItem?.enquiry?.addresses\r\n          ? dataItem?.enquiry?.addresses\r\n            .map((address: any) => getLocationDetailsByObj(address))\r\n            .join(', ')\r\n          : '--',\r\n      },\r\n      {\r\n        var: '#Lead Alternate Contact No#',\r\n        text: dataItem?.alternateContactNo || '',\r\n      },\r\n      {\r\n        var: ['#Lead Contact No#', '#LeadFullContactNo#'],\r\n        text: dataItem?.contactNo || '',\r\n      },\r\n      {\r\n        var: '#Lead Landline Number#',\r\n        text: dataItem?.landLine || '',\r\n      },\r\n      {\r\n        var: '#Lead Email#',\r\n        text: dataItem?.email || '',\r\n      },\r\n      {\r\n        var: '#Referral Name#',\r\n        text: dataItem?.referralName || '',\r\n      },\r\n      {\r\n        var: '#Referral Contact No#',\r\n        text: dataItem?.referralContactNo || '',\r\n      },\r\n      {\r\n        var: '#Referral Email#',\r\n        text: dataItem?.referralEmail || '',\r\n      },\r\n      {\r\n        var: '#Company Name#',\r\n        text: dataItem?.companyName || '',\r\n      },\r\n      {\r\n        var: ['#Primary Owner#', '#Assign To#'],\r\n        text: (() => {\r\n          const user = allUserList?.find(\r\n            (user: any) => user.id === dataItem?.assignTo\r\n          );\r\n          return user ? user.firstName + ' ' + user.lastName : '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#Secondary Owner#',\r\n        text: (() => {\r\n          const user = allUserList?.find(\r\n            (user: any) => user.id === dataItem?.secondaryUserId\r\n          );\r\n          return user ? user.firstName + ' ' + user.lastName : '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#Closing Manager#',\r\n        text: (() => {\r\n          const user = allUserList?.find(\r\n            (user: any) => user.id === dataItem?.closingManager\r\n          );\r\n          return user ? user.firstName + ' ' + user.lastName : '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#Sourcing Manager#',\r\n        text: (() => {\r\n          const user = allUserList?.find(\r\n            (user: any) => user.id === dataItem?.sourcingManager\r\n          );\r\n          return user ? user.firstName + ' ' + user.lastName : '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#Channel Partner Name#',\r\n        text: dataItem?.channelPartners\r\n          ? dataItem.channelPartners\r\n            .map((partner: any) => partner.firmName)\r\n            .join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Designation#',\r\n        text: dataItem?.designation || '',\r\n      },\r\n      {\r\n        var: '#Sale Type#',\r\n        text: SaleType[dataItem?.enquiry?.saleType] || '',\r\n      },\r\n      {\r\n        var: '#Lead Source#',\r\n        text:\r\n          LeadSource[dataItem?.enquiry?.leadSource] ||\r\n          dataItem?.enquiry?.prospectSource?.displayName ||\r\n          '',\r\n      },\r\n      {\r\n        var: '#Sub Source#',\r\n        text: dataItem?.enquiry?.subSource || '',\r\n      },\r\n      {\r\n        var: '#Property Type#',\r\n        text: dataItem?.enquiry?.propertyTypes?.[0]?.displayName || '',\r\n      },\r\n      {\r\n        var: '#PropertySubType#',\r\n        text:\r\n          dataItem?.enquiry?.propertyTypes\r\n            ?.map((item: any) => item?.childType?.displayName)\r\n            ?.join(', ') || '',\r\n      },\r\n      {\r\n        var: ['#noOfBhk#', '#BHKs#'],\r\n        text: dataItem?.enquiry?.bhKs\r\n          ? [...dataItem?.enquiry?.bhKs]\r\n            ?.map((bhk: any) => getBHKDisplayString(bhk))\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      /* BR field commented out\r\n      {\r\n        var: '#BR#',\r\n        text: dataItem?.enquiry?.bhKs\r\n          ? [...dataItem?.enquiry?.bhKs]\r\n            ?.map((br: any) => getBRDisplayString(br))\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      */\r\n      {\r\n        var: ['#bHKType#', '#BHKTypes#'],\r\n        text: dataItem?.enquiry?.bhkTypes\r\n          ? [...dataItem?.enquiry?.bhkTypes]\r\n            ?.map((type: any) => BHKType[type])\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Beds#',\r\n        text: dataItem?.enquiry?.beds?.length\r\n          ? [...dataItem.enquiry?.beds]\r\n            ?.map((bed: any) => (bed === 0 || bed === '0' ? 'Studio' : bed))\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Baths#',\r\n        text: dataItem?.enquiry?.baths\r\n          ? [...dataItem?.enquiry?.baths]?.map((bath: any) => bath)?.join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Furnish Status#',\r\n        text: FurnishStatus[dataItem?.enquiry?.furnished] || '',\r\n      },\r\n      {\r\n        var: '#Preferred Floor#',\r\n        text: dataItem?.enquiry?.floors\r\n          ? [...dataItem?.enquiry?.floors]\r\n            ?.map((floor: any) => floor)\r\n            ?.join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Offering Type#',\r\n        text: OfferType[dataItem?.enquiry?.offerType] || '',\r\n      },\r\n      {\r\n        var: '#Carpet Area#',\r\n        text:\r\n          (dataItem?.enquiry?.carpetArea ?? '') +\r\n          ' ' +\r\n          (dataItem?.enquiry?.carpetAreaUnit ?? ''),\r\n      },\r\n      {\r\n        var: '#Saleable Area#',\r\n        text:\r\n          (dataItem?.enquiry?.saleableArea ?? '') +\r\n          ' ' +\r\n          (dataItem?.enquiry?.saleableAreaUnit ?? ''),\r\n      },\r\n      {\r\n        var: '#BuiltUp Area#',\r\n        text:\r\n          (dataItem?.enquiry?.builtUpArea ?? '') +\r\n          ' ' +\r\n          (dataItem?.enquiry?.builtUpAreaUnit ?? ''),\r\n      },\r\n      {\r\n        var: '#Property Area#',\r\n        text:\r\n          (dataItem?.enquiry?.propertyArea ?? '') +\r\n          ' ' +\r\n          (dataItem?.enquiry?.propertyAreaUnit ?? ''),\r\n      },\r\n      {\r\n        var: '#Net Area#',\r\n        text:\r\n          (dataItem?.enquiry?.netArea ?? '') +\r\n          ' ' +\r\n          (dataItem?.enquiry?.netAreaUnit ?? ''),\r\n      },\r\n      {\r\n        var: '#Unit Number or Name#',\r\n        text: dataItem?.enquiry?.unitName || '',\r\n      },\r\n      {\r\n        var: '#Cluster Name#',\r\n        text: dataItem?.enquiry?.clusterName || '',\r\n      },\r\n      {\r\n        var: '#Purpose#',\r\n        text: PurposeType[dataItem?.enquiry?.purpose]\r\n          ? PurposeType[dataItem?.enquiry?.purpose]\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Nationality#',\r\n        text: dataItem?.nationality || '',\r\n      },\r\n      {\r\n        var: '#Possession Date#',\r\n        text: (() => {\r\n          const possessionType =\r\n            module?.includes('data') ? dataItem?.enquiry?.possesionType : dataItem?.possesionType;\r\n          const possessionDate =\r\n            module?.includes('data') ? dataItem?.possesionDate : dataItem?.enquiry?.possessionDate;\r\n          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\r\n            if (possessionType === PossessionType['Custom Date'] && possessionDate) {\r\n              return getTimeZoneDate(\r\n                possessionDate,\r\n                userData?.timeZoneInfo?.baseUTcOffset,\r\n                'dayMonthYear'\r\n              );\r\n            }\r\n            return PossessionType[possessionType];\r\n          }\r\n          return '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#Currency#',\r\n        text: dataItem?.enquiry?.currency || '',\r\n      },\r\n      {\r\n        var: '#Agencies#',\r\n        text: dataItem?.agencies\r\n          ? dataItem?.agencies.map((item: any) => item.name).join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Campaigns#',\r\n        text: dataItem?.campaigns\r\n          ? dataItem?.campaigns.map((item: any) => item.name).join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Custom Lead Status#',\r\n        text:\r\n          dataItem?.status?.childType?.displayName ??\r\n          dataItem?.status?.displayName ??\r\n          '',\r\n      },\r\n      {\r\n        var: '#Gender#',\r\n        text: Gender[dataItem?.gender] || '',\r\n      },\r\n      {\r\n        var: '#Marital Status#',\r\n        text: MaritalStatusType[dataItem?.maritalStatus] || '',\r\n      },\r\n      {\r\n        var: '#Date of Birth#',\r\n        text: dataItem?.dateOfBirth ? getTimeZoneDate(dataItem?.dateOfBirth, '00:00:00', 'dayMonthYear') : '',\r\n      },\r\n    ];\r\n    return processReplacements(msg, replacements);\r\n  };\r\n\r\n  if (Array.isArray(leadData)) {\r\n    leadData?.forEach((dataItem: any, index: number) => {\r\n      const msg = processLeadData(dataItem, index);\r\n      combinedMsg += index + 1 + '. ' + msg;\r\n      if (index < leadData.length - 1) {\r\n        combinedMsg += '\\n';\r\n      }\r\n    });\r\n  } else {\r\n    combinedMsg = processLeadData(leadData, 0);\r\n  }\r\n  if (header) {\r\n    header = processReplacements(header, replacements);\r\n  }\r\n\r\n  if (footer) {\r\n    footer = processReplacements(footer, replacements);\r\n  }\r\n\r\n  return combinedMsg\r\n    ? (header ? header?.replace(/\\n/g, '\\\\n') + '\\\\n' : '') +\r\n    combinedMsg?.replace(/\\n/g, '\\\\n') +\r\n    (footer ? '\\\\n' + footer?.replace(/\\n/g, '\\\\n') : '')\r\n    : '';\r\n};\r\n\r\nexport const WhatsAppTemplateMsg = (\r\n  template: any,\r\n  leadData: any,\r\n  tenantName: string,\r\n  defaultCurrency: string,\r\n  replaceData?: any,\r\n  media?: any,\r\n  allUserList?: any,\r\n  userData?: any,\r\n  currentDate?: any\r\n) => {\r\n  let combinedMsg = '';\r\n\r\n  let replacements: any = [];\r\n  const processReplacements = (msg: any, replacements: any) => {\r\n    if (typeof msg === 'string') {\r\n      replacements.forEach((item: any) => {\r\n        if (item.var === \"\\\"#BodyValues#\\\"\") {\r\n          msg = msg.replace(\r\n            \"\\\"#BodyValues#\\\"\",\r\n            item?.text?.map((s: string) => `\"${s}\"`).join(',') || ''\r\n          );\r\n        } else {\r\n          const regex = new RegExp(item.var, 'gi');\r\n          msg = msg.replaceAll(regex, item?.text?.trim());\r\n        }\r\n      });\r\n    }\r\n    return msg;\r\n  };\r\n\r\n  const processLeadData = (dataItem: any, index: number) => {\r\n    let msg = replaceData;\r\n    let header: any, body: any;\r\n    if (template?.headerValues) {\r\n      header = Object.values(template?.headerValues);\r\n      header = header.join(', ');\r\n    }\r\n    if (template?.bodyValues) {\r\n      body = Object.values(template?.bodyValues).map((item: any) =>\r\n        LeadTemplateMsg(\r\n          item || 'default',\r\n          leadData,\r\n          tenantName,\r\n          defaultCurrency,\r\n          null,\r\n          null,\r\n          allUserList,\r\n          userData,\r\n          currentDate\r\n        )\r\n      );\r\n    }\r\n    replacements = [\r\n      {\r\n        var: '#LeadFullContactNo#',\r\n        text: dataItem?.contactNo,\r\n      },\r\n      // {\r\n      //   var: '\\n',\r\n      //   text: '\\\\n',\r\n      // },\r\n      {\r\n        var: '#HeaderValues#',\r\n        text: LeadTemplateMsg(\r\n          header || 'default',\r\n          leadData,\r\n          tenantName,\r\n          defaultCurrency,\r\n          null,\r\n          null,\r\n          allUserList,\r\n          userData,\r\n          currentDate\r\n        ),\r\n      },\r\n      {\r\n        var: '\\\"#BodyValues#\\\"',\r\n        text: body,\r\n      },\r\n      {\r\n        var: '#Message#',\r\n        text:\r\n          LeadTemplateMsg(\r\n            template?.message,\r\n            leadData,\r\n            tenantName,\r\n            defaultCurrency,\r\n            template?.header,\r\n            template?.footer,\r\n            allUserList,\r\n            userData,\r\n            currentDate\r\n          ) || 'None',\r\n      },\r\n      { var: '#TemplateName#', text: template?.title || null },\r\n      { var: '#TemplateId#', text: template?.id },\r\n      { var: '#FileName#', text: media?.fileName || 'default' },\r\n      { var: '#MediaUrl#', text: media?.mediaUrl || 'default' },\r\n      { var: '#MessageType#', text: media?.mediaType || '' },\r\n      { var: '#leadName#', text: leadData?.name },\r\n    ];\r\n\r\n    return processReplacements(msg, replacements);\r\n  };\r\n\r\n  if (Array.isArray(leadData)) {\r\n    leadData?.forEach((dataItem: any, index: number) => {\r\n      const msg = processLeadData(dataItem, index);\r\n      combinedMsg += index + 1 + '. ' + msg;\r\n      if (index < leadData.length - 1) {\r\n        combinedMsg += '\\n';\r\n      }\r\n    });\r\n  } else {\r\n    combinedMsg = processLeadData(leadData, 0);\r\n  }\r\n\r\n  return combinedMsg ? combinedMsg : '';\r\n};\r\n\r\nexport const PropertyTemplateMsg = (\r\n  template: any,\r\n  propertyData: any,\r\n  areaSizeUnits: string,\r\n  tenantName: string,\r\n  header: any,\r\n  footer: any,\r\n  canViewOwnerDetails: boolean,\r\n  defaultCurrency: string,\r\n  userData?: any,\r\n  currentDate?: any\r\n) => {\r\n  let combinedMsg = '';\r\n  const transformedAttributes: any = {};\r\n  let replacements: any = [];\r\n\r\n  template = template?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  header = header?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  footer = footer?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n\r\n  const normalizeVar = (variable: string) =>\r\n    variable.toLowerCase()?.replace(/[\\s-]+/g, '');\r\n\r\n  const processReplacements = (msg: any, replacements: any) => {\r\n    if (msg) {\r\n      replacements.forEach((item: any) => {\r\n        if (Array.isArray(item.var)) {\r\n          item.var.forEach((variable: string) => {\r\n            const normalizedVar = normalizeVar(variable);\r\n            msg = msg?.replaceAll(normalizedVar, item?.text?.trim());\r\n          });\r\n        } else {\r\n          const normalizedVar = normalizeVar(item.var);\r\n          msg = msg?.replaceAll(normalizedVar, item?.text?.trim());\r\n        }\r\n      });\r\n    }\r\n    return msg;\r\n  };\r\n\r\n  const processPropertyData = (dataItem: any, index: number) => {\r\n    let msg = template;\r\n    if (dataItem.attributes?.length) {\r\n      for (const attribute of dataItem.attributes) {\r\n        if (!attribute?.attributeName.startsWith('Is')) {\r\n          transformedAttributes[attribute?.attributeName] = attribute.value;\r\n        }\r\n      }\r\n    }\r\n\r\n    let propertyLinks = '';\r\n\r\n    dataItem?.links?.map((link: any) => {\r\n      propertyLinks += link + ', ';\r\n    });\r\n    propertyLinks = propertyLinks.slice(0, -2);\r\n\r\n    replacements = [\r\n      {\r\n        var: '#PropertyStatus#',\r\n        text: dataItem?.status == 1 ? 'Sold' : PropertyStatus[dataItem?.status],\r\n      },\r\n      {\r\n        var: '#EnquiredFor#',\r\n        text: dataItem?.enquiredFor ? EnquiryType[dataItem?.enquiredFor] : '',\r\n      },\r\n      {\r\n        var: '#PropertyType#',\r\n        text: dataItem.propertyType?.displayName\r\n          ? dataItem.propertyType?.displayName\r\n          : '',\r\n      },\r\n      {\r\n        var: '#PropertySubType#',\r\n        text: dataItem.propertyType?.childType?.displayName\r\n          ? dataItem.propertyType?.childType?.displayName\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfBHK#',\r\n        text: dataItem?.noOfBHK ? getBHKDisplayString(dataItem?.noOfBHK) : '',\r\n      },\r\n      {\r\n        var: '#NoOfBR#',\r\n        text: dataItem?.noOfBHK ? getBRDisplayString(dataItem?.noOfBHK) : '',\r\n      },\r\n      {\r\n        var: '#BHKType#',\r\n        text: dataItem?.bhkType ? BHKType[dataItem?.bhkType] : '',\r\n      },\r\n      {\r\n        var: '#BRType#',\r\n        text: dataItem?.bhkType ? BHKType[dataItem?.bhkType] : '',\r\n      },\r\n      { var: '#Title#', text: dataItem?.title ? dataItem?.title : '' },\r\n      {\r\n        var: ['#PropertyArea#', '#PropertySize#'],\r\n        text: `${dataItem?.dimension?.area ? dataItem?.dimension?.area : ''} ${dataItem.dimension?.areaUnitId\r\n          ? getAreaUnit(dataItem.dimension?.areaUnitId, areaSizeUnits)?.unit\r\n          : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#CarpetArea#',\r\n        text: `${dataItem?.dimension?.carpetArea ? dataItem?.dimension?.carpetArea : ''\r\n          } ${dataItem.dimension?.carpetAreaId\r\n            ? getAreaUnit(dataItem.dimension?.carpetAreaId, areaSizeUnits)?.unit\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#BuiltUpArea#',\r\n        text: `${dataItem?.dimension?.buildUpArea\r\n          ? dataItem?.dimension?.buildUpArea\r\n          : ''\r\n          } ${dataItem.dimension?.buildUpAreaId\r\n            ? getAreaUnit(dataItem.dimension?.buildUpAreaId, areaSizeUnits)\r\n              ?.unit\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#SaleableArea#',\r\n        text: `${dataItem?.dimension?.saleableArea\r\n          ? dataItem?.dimension?.saleableArea\r\n          : ''\r\n          } ${dataItem.dimension?.saleableAreaId\r\n            ? getAreaUnit(dataItem.dimension?.saleableAreaId, areaSizeUnits)\r\n              ?.unit\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#NetArea#',\r\n        text: `${dataItem?.dimension?.netArea ? dataItem?.dimension?.netArea : ''\r\n          } ${dataItem.dimension?.netAreaUnitId\r\n            ? getAreaUnit(dataItem.dimension?.netAreaUnitId, areaSizeUnits)\r\n              ?.unit\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#Dimension#',\r\n        text: `${dataItem?.dimension?.length ? 'L-' + dataItem?.dimension?.length : ''\r\n          } ${dataItem?.dimension?.breadth && dataItem?.dimension?.length ? 'X' : ''\r\n          } ${dataItem?.dimension?.breadth\r\n            ? 'B-' + dataItem?.dimension?.breadth\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#SaleType#',\r\n        text: dataItem.saleType ? SaleType[dataItem.saleType] : '',\r\n      },\r\n      {\r\n        var: '#PossessionDate#',\r\n        text: (() => {\r\n          const possessionType = dataItem?.possesionType;\r\n          const possessionDate = dataItem?.possessionDate;\r\n          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\r\n            if (possessionType === PossessionType['Custom Date'] && possessionDate) {\r\n              const isReadyToMove = getTimeZoneDate(possessionDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') <= getTimeZoneDate(currentDate, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')\r\n              return isReadyToMove\r\n                ? 'Ready To Move'\r\n                : getTimeZoneDate(\r\n                  possessionDate,\r\n                  userData?.timeZoneInfo?.baseUTcOffset,\r\n                  'dayMonthYear'\r\n                );\r\n            }\r\n            return PossessionType[possessionType];\r\n          }\r\n          return '';\r\n        })(),\r\n      },\r\n      {\r\n        var: '#TotalPrice#',\r\n        text: dataItem?.monetaryInfo?.expectedPrice\r\n          ? dataItem?.monetaryInfo?.expectedPrice +\r\n          ' (' +\r\n          formatBudget(\r\n            dataItem?.monetaryInfo?.expectedPrice,\r\n            dataItem?.monetaryInfo?.currency || defaultCurrency\r\n          ) +\r\n          ')'\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Brokerage#',\r\n        text: `${dataItem?.monetaryInfo?.brokerage\r\n          ? dataItem?.monetaryInfo?.brokerage\r\n          : ''\r\n          } ${dataItem?.monetaryInfo?.brokerageCurrency\r\n            ? dataItem?.monetaryInfo?.brokerageCurrency\r\n            : ''\r\n          }`,\r\n      },\r\n      { var: '#Notes#', text: dataItem?.notes ? dataItem?.notes : '' },\r\n      {\r\n        var: '#AboutProperty#',\r\n        text: dataItem?.aboutProperty ? dataItem?.aboutProperty : '',\r\n      },\r\n      {\r\n        var: '#Address#',\r\n        text: dataItem?.address\r\n          ? getLocationDetailsByObj(dataItem?.address)\r\n          : '',\r\n      },\r\n      {\r\n        var: '#OwnerName#',\r\n        text:\r\n          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails\r\n            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.name?.trim())?.map((owner: any) => owner.name).join(', ')\r\n            : '',\r\n      },\r\n      {\r\n        var: '#OwnerPhone#',\r\n        text:\r\n          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails\r\n            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.phone?.trim()).map((owner: any) => owner.phone).join(', ')\r\n            : '',\r\n      },\r\n      {\r\n        var: '#OwnerEmail#',\r\n        text:\r\n          dataItem?.propertyOwnerDetails?.length && canViewOwnerDetails\r\n            ? dataItem?.propertyOwnerDetails?.filter((owner: any) => owner.email?.trim())?.map((owner: any) => owner.email).join(', ')\r\n            : '',\r\n      },\r\n      {\r\n        var: '#TotalFloors#',\r\n        text: transformedAttributes['numberOfFloors']\r\n          ? transformedAttributes['numberOfFloors']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#FloorNumber#',\r\n        text: transformedAttributes['floorNumber']\r\n          ? transformedAttributes['floorNumber']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfBathRooms#',\r\n        text: transformedAttributes['numberOfBathrooms']\r\n          ? transformedAttributes['numberOfBathrooms']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfBedRooms#',\r\n        text: transformedAttributes['numberOfBedrooms']\r\n          ? transformedAttributes['numberOfBedrooms']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfKitchens#',\r\n        text: transformedAttributes['numberOfKitchens']\r\n          ? transformedAttributes['numberOfKitchens']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfLivingRooms#',\r\n        text: transformedAttributes['numberOfLivingRooms']\r\n          ? transformedAttributes['numberOfLivingRooms']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfUtilities#',\r\n        text: transformedAttributes['numberOfUtilities']\r\n          ? transformedAttributes['numberOfUtilities']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoOfBalconies#',\r\n        text: transformedAttributes['numberOfBalconies']\r\n          ? transformedAttributes['numberOfBalconies']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#NoofParking#',\r\n        text: transformedAttributes['numberOfParking']\r\n          ? transformedAttributes['numberOfParking']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#FurnishStatus#',\r\n        text: dataItem?.furnishStatus\r\n          ? FurnishStatus[dataItem?.furnishStatus]\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Facing#',\r\n        text: dataItem?.facing ? Facing[dataItem?.facing] : '',\r\n      },\r\n      {\r\n        var: '#MicrositeUrl#',\r\n        text: getMSUrl(dataItem?.serialNo, false, true),\r\n      },\r\n      { var: '#Property URL#', text: propertyLinks },\r\n      {\r\n        var: '#userName#',\r\n        text: `${userData?.firstName} ${userData?.lastName}`,\r\n      },\r\n      { var: '#userEmail#', text: userData?.email },\r\n      { var: '#userMobile#', text: userData?.phoneNumber },\r\n      { var: '#tenantName#', text: tenantName || '' },\r\n      { var: '#LeadName#', text: dataItem?.leadName || '' },\r\n    ];\r\n    return processReplacements(msg, replacements);\r\n  };\r\n\r\n  if (Array.isArray(propertyData)) {\r\n    propertyData?.forEach((dataItem: any, index: number) => {\r\n      const msg = processPropertyData(dataItem, index);\r\n      combinedMsg += index + 1 + '. ' + msg;\r\n      if (index < propertyData.length - 1) {\r\n        combinedMsg += '\\n';\r\n      }\r\n    });\r\n  } else {\r\n    combinedMsg = processPropertyData(propertyData, 0);\r\n  }\r\n\r\n  if (header) {\r\n    header = processReplacements(header, replacements);\r\n  }\r\n\r\n  if (footer) {\r\n    footer = processReplacements(footer, replacements);\r\n  }\r\n\r\n  return combinedMsg\r\n    ? (header ? header + '\\n' : '') +\r\n    combinedMsg +\r\n    (footer ? '\\n' + footer : '')\r\n    : '';\r\n};\r\n\r\nexport const ProjectTemplateMsg = (\r\n  template: string,\r\n  projectData: any,\r\n  areaSizeUnits: any,\r\n  tenantName: string,\r\n  header: string | null,\r\n  footer: string | null,\r\n  unitInfo: any,\r\n  key: string,\r\n  userData?: any,\r\n  currentDate?: any\r\n) => {\r\n  let combinedMsg = '';\r\n  const transformedAttributes: any = {};\r\n  let replacements: any = [];\r\n\r\n  template = template?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  header = header?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n  footer = footer?.replace(/#([\\w\\s-]+)#/g, (match: string) => {\r\n    return (\r\n      '#' +\r\n      match\r\n        .slice(1, -1)\r\n        .toLowerCase()\r\n        ?.replace(/[\\s-]+/g, '') +\r\n      '#'\r\n    );\r\n  });\r\n\r\n  const normalizeVar = (variable: string) =>\r\n    variable.toLowerCase()?.replace(/[\\s-]+/g, '');\r\n\r\n  const processReplacements = (msg: any, replacements: any) => {\r\n    if (msg) {\r\n      replacements.forEach((item: any) => {\r\n        if (Array.isArray(item.var)) {\r\n          item.var.forEach((variable: string) => {\r\n            const normalizedVar = normalizeVar(variable);\r\n            const replacementText =\r\n              typeof item?.text === 'string' ? item.text.trim() : item.text;\r\n            msg = msg?.replaceAll(normalizedVar, replacementText);\r\n          });\r\n        } else {\r\n          const normalizedVar = normalizeVar(item.var);\r\n          const replacementText =\r\n            typeof item?.text === 'string' ? item.text.trim() : item.text;\r\n          msg = msg?.replaceAll(normalizedVar, replacementText);\r\n        }\r\n      });\r\n    }\r\n    return msg;\r\n  };\r\n\r\n  const processProjectData = (dataItem: any, index: number) => {\r\n    let msg = template;\r\n\r\n    if (dataItem.attributes?.length) {\r\n      for (const attribute of dataItem.attributes) {\r\n        if (!attribute?.attributeName.startsWith('Is')) {\r\n          transformedAttributes[attribute?.attributeName] = attribute.value;\r\n        }\r\n      }\r\n    }\r\n\r\n    function getValueBasedOnKey(property: any) {\r\n      if (key === 'share-project' || key === 'share-matching-lead') {\r\n        return unitInfo?.[0]?.[property] ? unitInfo?.[0]?.[property] : '';\r\n      } else {\r\n        return dataItem?.unitInfo?.[property] || dataItem?.[property] || '';\r\n      }\r\n    }\r\n    let projectLinks = '';\r\n\r\n    dataItem?.links?.map((link: any) => {\r\n      projectLinks += link + ', ';\r\n    });\r\n    projectLinks = projectLinks.slice(0, -2);\r\n\r\n    replacements = [\r\n      {\r\n        var: '#Project Status#',\r\n        text:\r\n          dataItem?.currentStatus == 1\r\n            ? 'Sold'\r\n            : PropertyStatus[dataItem?.status],\r\n      },\r\n      {\r\n        var: '#ProjectType#',\r\n        text: dataItem.projectType?.displayName\r\n          ? dataItem.projectType?.displayName\r\n          : '',\r\n      },\r\n      {\r\n        var: '#ProjectSubType#',\r\n        text: dataItem.projectType?.childType?.displayName\r\n          ? dataItem.projectType?.childType?.displayName\r\n          : '',\r\n      },\r\n      { var: '#Name#', text: dataItem?.name ? dataItem?.name : '' },\r\n      {\r\n        var: '#PossessionDate#',\r\n        text: (() => {\r\n          const possessionType = dataItem?.possesionType;\r\n          const possessionDate = dataItem?.possessionDate;\r\n          if (possessionType && possessionType > 0 && PossessionType[possessionType]) {\r\n            if (possessionType === PossessionType['Custom Date'] && possessionDate) {\r\n              const possessionDateObj = new Date(possessionDate);\r\n              const currentDateObj = new Date(currentDate);\r\n              const isPast = possessionDateObj <= currentDateObj;\r\n              return isPast\r\n                ? 'Ready To Move'\r\n                : getTimeZoneDate(\r\n                  possessionDate,\r\n                  userData?.timeZoneInfo?.baseUTcOffset,\r\n                  'dayMonthYear'\r\n                );\r\n            }\r\n            return PossessionType[possessionType];\r\n          }\r\n          return '';\r\n        })(),\r\n      },\r\n      // {\r\n      //   var: '#TotalPrice#',\r\n      //   text: dataItem?.monetaryInfo?.expectedPrice\r\n      //     ? dataItem?.monetaryInfo?.expectedPrice +\r\n      //     ' (' +\r\n      //     formatBudget(\r\n      //       dataItem?.monetaryInfo?.expectedPrice,\r\n      //       dataItem?.monetaryInfo?.currency\r\n      //     ) +\r\n      //     ')'\r\n      //     : '',\r\n      // },\r\n      {\r\n        var: '#Brokerage#',\r\n        text: `${dataItem?.monetaryInfo?.brokerage\r\n          ? dataItem?.monetaryInfo?.brokerage\r\n          : ''\r\n          } ${dataItem?.monetaryInfo?.brokerageCurrency\r\n            ? dataItem?.monetaryInfo?.brokerageCurrency\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#LandArea#',\r\n        text: `${dataItem?.area ? dataItem?.area : ''} ${dataItem.areaUnitId\r\n          ? getAreaUnit(dataItem.areaUnitId, areaSizeUnits)?.unit\r\n          : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#Address#',\r\n        text: dataItem?.address\r\n          ? getLocationDetailsByObj(dataItem?.address)\r\n          : '',\r\n      },\r\n      {\r\n        var: '#BuilderName#',\r\n        text: dataItem?.builderDetail?.name\r\n          ? dataItem?.builderDetail?.name\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Notes#',\r\n        text: dataItem?.notes ? dataItem?.notes : '',\r\n      },\r\n      {\r\n        var: '#BuilderPhone#',\r\n        text: dataItem?.builderDetail?.contactNo\r\n          ? dataItem?.builderDetail?.contactNo\r\n          : '',\r\n      },\r\n      {\r\n        var: '#BuilderPointOfContact#',\r\n        text: dataItem?.builderDetail?.pointOfContact\r\n          ? dataItem?.builderDetail?.pointOfContact\r\n          : '',\r\n      },\r\n      {\r\n        var: '#TotalFloors#',\r\n        text: transformedAttributes['numberOfFloors']\r\n          ? transformedAttributes['numberOfFloors']\r\n          : '',\r\n      },\r\n      {\r\n        var: '#Facing#',\r\n        text: dataItem?.facings?.length\r\n          ? dataItem?.facings.map((index: number) => Facing[index]).join(', ')\r\n          : '',\r\n      },\r\n      {\r\n        var: '#UnitName#',\r\n        text: getValueBasedOnKey('name'),\r\n      },\r\n      {\r\n        var: '#UnitArea#',\r\n        text: `${dataItem?.area ? dataItem?.area : ''} ${dataItem.areaUnitId\r\n          ? getAreaUnit(dataItem.areaUnitId, areaSizeUnits)?.unit\r\n          : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#CarpetArea#',\r\n        text: `${dataItem?.carpetArea ? dataItem?.carpetArea : ''} ${dataItem.carpetAreaUnitId\r\n          ? getAreaUnit(dataItem.carpetAreaUnitId, areaSizeUnits)?.unit\r\n          : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#BuiltupArea#',\r\n        text: `${dataItem?.buildUpArea ? dataItem?.buildUpArea : ''} ${dataItem.buildUpAreaId\r\n          ? getAreaUnit(dataItem.buildUpAreaId, areaSizeUnits)?.unit\r\n          : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#SuperBuiltupArea#',\r\n        text: `${dataItem?.superBuildUpArea ? dataItem?.superBuildUpArea : ''\r\n          } ${dataItem.superBuildUpAreaUnit\r\n            ? getAreaUnit(dataItem.superBuildUpAreaUnit, areaSizeUnits)?.unit\r\n            : ''\r\n          }`,\r\n      },\r\n      {\r\n        var: '#MaintenanceCost#',\r\n        text:\r\n          getValueBasedOnKey('maintenanceCost') +\r\n          '(' +\r\n          formatBudget(\r\n            getValueBasedOnKey('maintenanceCost'),\r\n            getValueBasedOnKey('currency')\r\n          ) +\r\n          ')',\r\n      },\r\n      {\r\n        var: '#PricePerUnit#',\r\n        text:\r\n          getValueBasedOnKey('pricePerUnit') +\r\n          '(' +\r\n          formatBudget(\r\n            getValueBasedOnKey('pricePerUnit'),\r\n            getValueBasedOnKey('currency')\r\n          ) +\r\n          ')',\r\n      },\r\n      {\r\n        var: '#TotalPrice#',\r\n        text:\r\n          getValueBasedOnKey('price') +\r\n          '(' +\r\n          formatBudget(\r\n            getValueBasedOnKey('price'),\r\n            getValueBasedOnKey('currency')\r\n          ) +\r\n          ')',\r\n      },\r\n      {\r\n        var: '#UnitType#',\r\n        text: getValueBasedOnKey('unitType?.displayName'),\r\n      },\r\n      {\r\n        var: '#UnitSubType#',\r\n        text: getValueBasedOnKey('unitType?.childType?.displayName'),\r\n      },\r\n      {\r\n        var: '#Bhk#',\r\n        text:\r\n          key !== 'share-project'\r\n            ? dataItem?.unitInfo?.noOfBHK\r\n              ? getBHKDisplayString(dataItem.unitInfo.noOfBHK)\r\n              : getBHKDisplayString(dataItem?.noOfBHK)\r\n            : dataItem?.unitInfo?.[0]?.noOfBHK\r\n              ? getBHKDisplayString(dataItem.unitInfo[0].noOfBHK)\r\n              : '',\r\n      },\r\n      {\r\n        var: '#BhkType#',\r\n        text:\r\n          key === 'share-project'\r\n            ? dataItem?.unitInfo?.bhkType\r\n              ? BHKType[dataItem?.unitInfo?.bhkType]\r\n              : BHKType[dataItem?.bhkType]\r\n            : unitInfo?.[0]?.bhkType\r\n              ? BHKType[unitInfo?.[0]?.bhkType]\r\n              : '',\r\n      },\r\n      {\r\n        var: '#Facing#',\r\n        text:\r\n          key !== 'share-project'\r\n            ? dataItem?.unitInfo?.facings?.length\r\n              ? dataItem.unitInfo.facings\r\n                .map((index: number) => Facing[index])\r\n                .join(', ')\r\n              : ''\r\n            : unitInfo?.[0]?.facings?.length\r\n              ? unitInfo?.[0]?.facings\r\n                .map((index: number) => Facing[index])\r\n                .join(', ')\r\n              : '',\r\n      },\r\n      {\r\n        var: '#FurnishingStatus#',\r\n        text:\r\n          key !== 'share-project'\r\n            ? dataItem?.unitInfo?.furnishingStatus !== undefined\r\n              ? FurnishStatus[dataItem.unitInfo.furnishingStatus]\r\n              : FurnishStatus[dataItem?.furnishingStatus]\r\n            : unitInfo?.[0]?.furnishingStatus !== undefined\r\n              ? FurnishStatus[unitInfo[0].furnishingStatus]\r\n              : '',\r\n      },\r\n      {\r\n        var: '#MicrositeUrl#',\r\n        text: getMSUrl(dataItem?.serialNo, true),\r\n      },\r\n      { var: '#Project URL#', text: projectLinks },\r\n      {\r\n        var: '#userName#',\r\n        text: `${userData?.firstName} ${userData?.lastName}`,\r\n      },\r\n      { var: '#userEmail#', text: userData?.email },\r\n      { var: '#userMobile#', text: userData?.phoneNumber },\r\n      { var: '#tenantName#', text: tenantName || '' },\r\n      { var: '#LeadName#', text: dataItem?.leadName || '' },\r\n    ];\r\n\r\n    return processReplacements(msg, replacements);\r\n  };\r\n  if (Array.isArray(projectData)) {\r\n    projectData?.forEach((dataItem: any, index: number) => {\r\n      const msg = processProjectData(dataItem, index);\r\n      combinedMsg += index + 1 + '. ' + msg;\r\n      if (index < projectData.length - 1) {\r\n        combinedMsg += '\\n';\r\n      }\r\n    });\r\n  } else {\r\n    combinedMsg = processProjectData(projectData, 0);\r\n  }\r\n\r\n  if (header) {\r\n    header = processReplacements(header, replacements);\r\n  }\r\n\r\n  if (footer) {\r\n    footer = processReplacements(footer, replacements);\r\n  }\r\n\r\n  return combinedMsg\r\n    ? (header ? header + '\\n' : '') +\r\n    combinedMsg +\r\n    (footer ? '\\n' + footer : '')\r\n    : '';\r\n};\r\n\r\nexport const getMSUrl = (\r\n  serialNo: string,\r\n  isProject: boolean = false,\r\n  isListing: boolean = false\r\n) => {\r\n  const subDomain = getTenantName();\r\n  const userName: any = JSON.parse(\r\n    localStorage.getItem('userDetails')\r\n  )?.preferred_username;\r\n\r\n  let previewType = 'property-preview'; // default\r\n\r\n  if (isProject) {\r\n    previewType = 'project-preview';\r\n  } else if (isListing) {\r\n    previewType = 'listing-preview';\r\n  }\r\n\r\n  return `https://${subDomain + getEnvDetails()}/external/${previewType}/${userName}/${serialNo}`;\r\n};\r\n\r\nexport const sortAssignedUsers = (assignedUser: any, allActiveUsers: any[]) => {\r\n  const assignedUserIds = Array.isArray(assignedUser.value)\r\n    ? assignedUser.value\r\n    : [assignedUser.value];\r\n  const assignedUsers: any[] = [];\r\n\r\n  allActiveUsers.forEach((user) => {\r\n    if (assignedUserIds.includes(user.id)) {\r\n      if (user.firstName === 'You') {\r\n        assignedUsers.unshift(user);\r\n      } else {\r\n        assignedUsers.push(user);\r\n      }\r\n    }\r\n  });\r\n  assignedUsers.sort((a, b) => {\r\n    if (a.firstName === 'You') return -1;\r\n    if (b.firstName === 'You') return 1;\r\n    const nameA = `${a.firstName} ${a.lastName}`;\r\n    const nameB = `${b.firstName} ${b.lastName}`;\r\n    return nameA.localeCompare(nameB);\r\n  });\r\n\r\n  const unassignedUsers = allActiveUsers.filter(\r\n    (user) => !assignedUserIds.includes(user.id)\r\n  );\r\n  unassignedUsers.sort((a, b) => {\r\n    if (a.firstName === 'You') return -1;\r\n    if (b.firstName === 'You') return 1;\r\n    const nameA = `${a.firstName} ${a.lastName}`;\r\n    const nameB = `${b.firstName} ${b.lastName}`;\r\n    return nameA.localeCompare(nameB);\r\n  });\r\n\r\n  return assignedUsers.concat(unassignedUsers);\r\n};\r\n\r\nexport const assignToSort = (\r\n  allUserList: Array<Object>,\r\n  assignedToUserId: string = '',\r\n  fullName: boolean = false\r\n) => {\r\n  let userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;\r\n  let loggedInUser,\r\n    assignedUser,\r\n    activeUsers: Array<Object> = [],\r\n    inactiveUsers: Array<Object> = [],\r\n    usersList: Array<Object> = [];\r\n\r\n  allUserList?.map((user: any) => {\r\n    if (user.id === userId) {\r\n      loggedInUser = {\r\n        ...user,\r\n        ...(fullName ? {} : { firstName: 'You', lastName: '' }),\r\n      };\r\n    }\r\n    if (user.id === assignedToUserId && user.id !== userId) {\r\n      assignedUser = user;\r\n    }\r\n    if (user.id !== userId && user.id !== assignedToUserId && user.isActive)\r\n      activeUsers.push(user);\r\n    if (user.id !== userId && user.id !== assignedToUserId && !user.isActive)\r\n      inactiveUsers.push(user);\r\n  });\r\n\r\n  if (assignedUser) usersList.push(assignedUser);\r\n  if (loggedInUser) usersList.push(loggedInUser);\r\n\r\n  return allUserList ? [...usersList, ...activeUsers, ...inactiveUsers] : [];\r\n};\r\n\r\nexport const getTotalCountForReports = (items: any, statusList?: any) => {\r\n  let total: any = {\r\n    projectTitle: 'Total',\r\n    firstName: 'Total',\r\n    lastName: '',\r\n    userName: 'Total',\r\n    subSource: 'Total',\r\n    agencyName: 'Total',\r\n    source: 'Total',\r\n    name: 'Total',\r\n    allCount: 0,\r\n    activeCount: 0,\r\n    newCount: 0,\r\n    totalCount: 0,\r\n    pendingCount: 0,\r\n    overdueCount: 0,\r\n    callbackCount: 0,\r\n    meetingScheduledCount: 0,\r\n    siteVisitScheduledCount: 0,\r\n    meetingDoneCount: 0,\r\n    meetingNotDoneCount: 0,\r\n    siteVisitDoneCount: 0,\r\n    siteVisitNotDoneCount: 0,\r\n    bookedCount: 0,\r\n    notInterestedCount: 0,\r\n    droppedCount: 0,\r\n    meetingDoneUniqueCount: 0,\r\n    meetingNotDoneUniqueCount: 0,\r\n    siteVisitDoneUniqueCount: 0,\r\n    siteVisitNotDoneUniqueCount: 0,\r\n    averageWorkingHours: 0,\r\n    callsInitiatedCount: 0,\r\n    callsInitiatedLeadsCount: 0,\r\n    whatsAppInitiatedCount: 0,\r\n    whatsAppInitiatedLeadsCount: 0,\r\n    emailsInitiatedCount: 0,\r\n    emailsInitiatedLeadsCount: 0,\r\n    smsInitiatedCount: 0,\r\n    smsInitiatedLeadsCount: 0,\r\n    statusEditsCount: 0,\r\n    statusEditsLeadsCount: 0,\r\n    formEditsCount: 0,\r\n    formEditsLeadsCount: 0,\r\n    notesAddedCount: 0,\r\n    notesAddedLeadsCount: 0,\r\n    callbackScheduledLeadsCount: 0,\r\n    bookedLeadsCount: 0,\r\n    notInterestedLeadsCount: 0,\r\n    droppedLeadsCount: 0,\r\n    hotLeadsCount: 0,\r\n    warmLeadsCount: 0,\r\n    coldLeadsCount: 0,\r\n    escalatedLeadsCount: 0,\r\n    highlightedLeadsCount: 0,\r\n    aboutToConvertLeadsCount: 0,\r\n    all: 0,\r\n    active: 0,\r\n    overdue: 0,\r\n    callback: 0,\r\n    busy: 0,\r\n    toScheduleAMeeting: 0,\r\n    followUp: 0,\r\n    toScheduleSiteVisit: 0,\r\n    planPostponed: 0,\r\n    needMoreInfo: 0,\r\n    notAnswered: 0,\r\n    notReachable: 0,\r\n    dropped: 0,\r\n    notLooking: 0,\r\n    ringingNotReceived: 0,\r\n    wrongOrInvalidNo: 0,\r\n    purchasedFromOthers: 0,\r\n    meetingScheduled: 0,\r\n    onCall: 0,\r\n    online: 0,\r\n    inPerson: 0,\r\n    others: 0,\r\n    notInterested: 0,\r\n    differentLocation: 0,\r\n    differentRequirements: 0,\r\n    unmatchedBudget: 0,\r\n    siteVisitScheduled: 0,\r\n    firstVisit: 0,\r\n    reVisit: 0,\r\n    pending: 0,\r\n    booked: 0,\r\n    new: 0,\r\n    notInterestedAfterMeetingDone: 0,\r\n    notInterestedAfterSiteVisitDone: 0,\r\n    droppedAfterMeetingDone: 0,\r\n    droppedAfterSiteVisitDone: 0,\r\n    incomingAnswered: 0,\r\n    incomingMissed: 0,\r\n    totalIncomingCalls: 0,\r\n    outgoingAnswered: 0,\r\n    outgoingNotConnected: 0,\r\n    totalOutgoingCalls: 0,\r\n    totalCalls: 0,\r\n    totalTalkTime: 0,\r\n    averageTalkTime: 0,\r\n    maxTalkTime: 0,\r\n    minTalkTime: 0,\r\n    convertedDataCount: 0,\r\n  };\r\n\r\n  const calculateTotalForStatus = (\r\n    items: any[],\r\n    statusId: string,\r\n    total: any\r\n  ) => {\r\n    items.forEach((item: any) => {\r\n      const dataArray = (item?.data || []).filter(\r\n        (data: any) => data?.statusId === statusId\r\n      );\r\n      const dataCount = dataArray.reduce(\r\n        (sum: number, data: any) => sum + (data?.dataCount || 0),\r\n        0\r\n      );\r\n      total[`${statusId}DataCount`] =\r\n        (total[`${statusId}DataCount`] || 0) + dataCount;\r\n    });\r\n  };\r\n\r\n  const calculateTotalForSubSource = (\r\n    items: any[],\r\n    statusId: string,\r\n    total: any\r\n  ) => {\r\n    items.forEach((item: any) => {\r\n      const dataArray = (item?.subSource || []).filter(\r\n        (data: any) =>\r\n          `${data?.subSource}(${data?.sourceName})` === statusId?.toString()\r\n      );\r\n      const dataCount = dataArray?.reduce(\r\n        (sum: number, data: any) => sum + (data?.count || 0),\r\n        0\r\n      );\r\n      total[`${statusId}DataCount`] =\r\n        (total[`${statusId}DataCount`] || 0) + dataCount;\r\n    });\r\n  };\r\n\r\n  const calculateTotalForSource = (\r\n    items: any[],\r\n    statusId: string,\r\n    total: any\r\n  ) => {\r\n    items.forEach((item: any) => {\r\n      const dataArray = (item?.source || []).filter(\r\n        (data: any) => data?.displayName === statusId?.toString()\r\n      );\r\n      const dataCount = dataArray?.reduce(\r\n        (sum: number, data: any) => sum + (data?.count || 0),\r\n        0\r\n      );\r\n      total[`${statusId}DataCount`] =\r\n        (total[`${statusId}DataCount`] || 0) + dataCount;\r\n    });\r\n  };\r\n  statusList?.forEach((status: any) => {\r\n    total[`${status}DataCount`] = 0;\r\n    total[`${status}DataCount`] = 0;\r\n    calculateTotalForSource(items, status, total);\r\n    calculateTotalForSubSource(items, status, total);\r\n  });\r\n\r\n  statusList?.forEach((status: any) => {\r\n    calculateTotalForStatus(items, status, total);\r\n  });\r\n\r\n  items?.map((item: any) => {\r\n    let objArray = Object.entries(item);\r\n    objArray.map((entry: any) => {\r\n      if (typeof entry[1] === 'number') {\r\n        if (!total?.[entry[0]]) {\r\n          total[entry[0]] = 0;\r\n        }\r\n        total[entry[0]] += entry[1];\r\n      } else if (\r\n        entry[0] === 'averageWorkingHours' ||\r\n        entry[0] === 'minTalkTime' ||\r\n        entry[0] === 'maxTalkTime' ||\r\n        entry[0] === 'averageTalkTime' ||\r\n        entry[0] === 'totalTalkTime'\r\n      ) {\r\n        const workingHours = moment.duration(entry[1]);\r\n        total[entry[0]] += workingHours.asSeconds();\r\n      }\r\n    });\r\n  });\r\n\r\n  const formatDuration = (seconds: number) => {\r\n    const duration = moment.duration(seconds, 'seconds');\r\n    const hours = Math.floor(duration?.asHours())?.toString()?.padStart(2, '0');\r\n    const minutes = duration?.minutes()?.toString()?.padStart(2, '0');\r\n    const secondsStr = duration?.seconds()?.toString()?.padStart(2, '0');\r\n    return `${hours}:${minutes}:${secondsStr}`;\r\n  };\r\n\r\n  if (items?.length > 1) {\r\n    [\r\n      'averageWorkingHours',\r\n      'minTalkTime',\r\n      'maxTalkTime',\r\n      'averageTalkTime',\r\n      'totalTalkTime',\r\n    ].forEach((field) => {\r\n      if (total[field] !== undefined) {\r\n        total[field] = formatDuration(total[field]);\r\n      }\r\n    });\r\n    return [...items, total];\r\n  }\r\n  return items;\r\n};\r\n\r\nexport const getDaysInMonth = (year: number, month: number) => {\r\n  let date = new Date(Date.UTC(year, month, 1));\r\n  let monthDays = [];\r\n  while (date.getUTCMonth() === month) {\r\n    monthDays.push(new Date(date));\r\n    date.setUTCDate(date.getUTCDate() + 1);\r\n  }\r\n  return monthDays;\r\n};\r\n\r\nexport const containsOnlyEmojis = (input: any): boolean => {\r\n  const emojiRegex =\r\n    /^(?:[\\u2700-\\u27bf]|(?:\\ud83c[\\udde6-\\uddff]){2}|[\\ud800-\\udbff][\\udc00-\\udfff]|[\\u0023-\\u0039]\\ufe0f?\\u20e3|\\u3299|\\u3297|\\u303d|\\u3030|\\u24c2|\\ud83c[\\udd70-\\udd71]|\\ud83c[\\udd7e-\\udd7f]|\\ud83c\\udd8e|\\ud83c[\\udd91-\\udd9a]|\\ud83c[\\udde6-\\uddff]|[\\ud83c[\\ude01-\\ude02]|\\ud83c\\ude1a|\\ud83c\\ude2f|[\\ud83c[\\ude32-\\ude3a]|[\\ud83c[\\ude50-\\ude51]|\\u203c|\\u2049|[\\u25aa-\\u25ab]|\\u25b6|\\u25c0|[\\u25fb-\\u25fe]|\\u00a9|\\u00ae|\\u2122|\\u2139|\\ud83c\\udc04|[\\u2600-\\u26FF]|\\u2b05|\\u2b06|\\u2b07|\\u2b1b|\\u2b1c|\\u2b50|\\u2b55|\\u231a|\\u231b|\\u2328|\\u23cf|[\\u23e9-\\u23f3]|[\\u23f8-\\u23fa]|\\ud83c\\udccf|\\u2934|\\u2935|[\\u2190-\\u21ff])+$/;\r\n\r\n  return emojiRegex.test(input);\r\n};\r\n\r\nexport const getLocationDetailsByObj = (object: any): string => {\r\n  const {\r\n    subLocality,\r\n    locality,\r\n    subCommunity,\r\n    community,\r\n    towerName,\r\n    city,\r\n    district,\r\n    state,\r\n    country,\r\n    postalCode,\r\n  } = object || {};\r\n  const addressParts = [\r\n    subLocality,\r\n    locality && locality !== subLocality && locality,\r\n    city && city !== subLocality && city !== locality && city,\r\n    subCommunity,\r\n    community,\r\n    towerName,\r\n    district,\r\n    state,\r\n    country,\r\n    postalCode,\r\n  ]\r\n    .filter((part) => part)\r\n    .map((part) => part.trim())\r\n    .join(', ');\r\n  if (addressParts.trim().endsWith(',')) {\r\n    return addressParts.trim().slice(0, -1);\r\n  }\r\n  return addressParts || '';\r\n};\r\n\r\nexport const getLocalityDetailsByObj = (obj: any) => {\r\n  if (!obj) {\r\n    return null;\r\n  }\r\n  const subLocality = obj.subLocality;\r\n  const locality = obj.locality;\r\n  const city = obj.city;\r\n  const enquiredLocComponents = [];\r\n\r\n  if (subLocality !== locality) enquiredLocComponents.push(subLocality);\r\n  if (locality !== city) enquiredLocComponents.push(locality);\r\n\r\n  let enquiredLoc = enquiredLocComponents.join(', ');\r\n  enquiredLoc = enquiredLoc.trim().endsWith(',')\r\n    ? enquiredLoc.trim().slice(0, -1)\r\n    : enquiredLoc;\r\n\r\n  return enquiredLoc || null;\r\n};\r\n\r\n/**\r\n * A function to check if a string contains\r\n * any element in a given array\r\n * @returns boolean\r\n */\r\nexport const isStringSubsetInArray = (\r\n  inputString: string,\r\n  arrayOfStrings: string[]\r\n): boolean => {\r\n  for (let i = 0; i < arrayOfStrings.length; i++) {\r\n    if (inputString.includes(arrayOfStrings[i])) {\r\n      return true;\r\n    }\r\n  }\r\n  return false;\r\n};\r\n\r\n/**\r\n * validator function to check if selected date with time is lesser than current date and time\r\n * @returns\r\n */\r\nexport const validateScheduleTime = (currentDate: any): ValidatorFn => {\r\n  return (control: AbstractControl): ValidationErrors | null => {\r\n    const selectedTime = new Date(control?.value);\r\n    selectedTime.setSeconds(0, 0);\r\n\r\n    if (!control?.value) {\r\n      return { required: true };\r\n    }\r\n\r\n    const currentTime = new Date(currentDate);\r\n    currentTime.setSeconds(0, 0);\r\n\r\n    if (currentTime >= selectedTime) {\r\n      return { invalidTime: true };\r\n    }\r\n\r\n    return null;\r\n  };\r\n};\r\n\r\nexport const getBHKDisplayString = (\r\n  bhkNo: string,\r\n  isBHKText: boolean = false\r\n): string => {\r\n  const displayBHKNo = bhkNo == '0.5' ? '1' : bhkNo;\r\n  const displayType = bhkNo == '0.5' ? 'RK' : 'BHK';\r\n  return !isBHKText ? `${displayBHKNo} ${displayType}` : `${displayBHKNo}`;\r\n};\r\n\r\nexport const getBRDisplayString = (\r\n  brNo: string,\r\n  isBRText: boolean = false\r\n): string => {\r\n  if (brNo == '0.5') {\r\n    return 'Studio';\r\n  }\r\n  return isBRText ? `${brNo}` : `${brNo} BR`;\r\n};\r\n\r\n//History\r\nexport const getBRDisplay = (brNo: string): string => {\r\n  if (!brNo) return '';\r\n\r\n  const brNumbers = brNo\r\n    .split(',')\r\n    .map((n) => parseFloat(n.trim()))\r\n    .filter((n) => !isNaN(n));\r\n\r\n  return brNumbers.length > 0\r\n    ? brNumbers.map((br) => `${br} BR`).join(', ')\r\n    : '';\r\n};\r\n\r\nexport const getBedsDisplay = (bedNo: string): string => {\r\n  if (!bedNo) return '';\r\n  const bedNumbers = bedNo?.split(',')?.map((n) => parseFloat(n.trim()));\r\n\r\n  const formattedBeds = bedNumbers?.map((bed) => {\r\n    if (bed === 0) {\r\n      return 'Studio';\r\n    } else {\r\n      return bed?.toString();\r\n    }\r\n  });\r\n\r\n  return formattedBeds?.join(', ') || '';\r\n};\r\n\r\n//History\r\nexport const getBHKDisplay = (bhkNo: string): string => {\r\n  if (!bhkNo) return '';\r\n  const bhkNumbers = bhkNo?.split(',')?.map((n) => parseFloat(n.trim()));\r\n\r\n  const formattedBHKs = bhkNumbers?.map((bhk) => {\r\n    if (bhk === 0.5) {\r\n      return '1RK';\r\n    } else if (bhk) {\r\n      return `${bhk} BHK`;\r\n    } else {\r\n      return bhk?.toString();\r\n    }\r\n  });\r\n\r\n  return formattedBHKs?.join(', ') || '';\r\n};\r\n\r\nexport const generateLeadSourcesArray = (): Array<string> => {\r\n  const leadSourcesArray: Array<string> = Object.keys(LeadSource)\r\n    .slice(44)\r\n    .sort();\r\n  return leadSourcesArray;\r\n};\r\n\r\nexport const snakeToCamel = (snakeCase: string): string => {\r\n  if (!snakeCase) return snakeCase;\r\n\r\n  const components = snakeCase.split('_');\r\n  const camelCaseString =\r\n    components[0] +\r\n    components\r\n      .slice(1)\r\n      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join('');\r\n\r\n  return camelCaseString;\r\n};\r\n\r\nexport const getDateRange = (range: DateRange, currentDate: Date): Date[] => {\r\n  const today = new Date(currentDate);\r\n  let startDate: Date;\r\n  let endDate: Date;\r\n\r\n  switch (range) {\r\n    case DateRange.Today:\r\n      startDate = new Date(today);\r\n      endDate = new Date(today);\r\n      break;\r\n\r\n    case DateRange.Yesterday:\r\n      startDate = new Date(today);\r\n      startDate.setDate(today.getDate() - 1);\r\n      endDate = new Date(today);\r\n      endDate.setDate(today.getDate() - 1);\r\n      break;\r\n\r\n    case DateRange.Last7Days:\r\n      startDate = new Date(today);\r\n      startDate.setDate(today.getDate() - 6);\r\n      endDate = new Date(today);\r\n      break;\r\n\r\n    case DateRange.CurrentMonth:\r\n      startDate = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      endDate = new Date(today);\r\n      break;\r\n\r\n    case DateRange.TillDate:\r\n      startDate = null;\r\n      endDate = new Date(today);\r\n      break;\r\n\r\n    default:\r\n      startDate = new Date(today);\r\n      endDate = new Date(today);\r\n      break;\r\n  }\r\n  return [startDate, endDate];\r\n};\r\n\r\nexport const hexToRgba = (hex: string, opacity: number) => {\r\n  hex = hex?.replace('#', '');\r\n  const r = parseInt(hex.slice(0, 2), 16);\r\n  const g = parseInt(hex.slice(2, 4), 16);\r\n  const b = parseInt(hex.slice(4, 6), 16);\r\n  const validOpacity = Math.min(1, Math.max(0, opacity));\r\n  return `rgba(${r}, ${g}, ${b}, ${validOpacity})`;\r\n};\r\n\r\nexport const atLeastTwoSelectedValidator: ValidatorFn = (\r\n  control: AbstractControl\r\n): ValidationErrors | null => {\r\n  const teamUsers = control.value;\r\n  if (!teamUsers || teamUsers.length < 2) {\r\n    return { atLeastTwoSelected: true };\r\n  }\r\n  return null;\r\n};\r\n\r\nexport function onFilterChanged(event: any) {\r\n  const nodes: any = [];\r\n  event.api.forEachNodeAfterFilter((node: any) => {\r\n    if (node.isSelected()) {\r\n      nodes.push(node);\r\n    }\r\n  });\r\n  event.api.getSelectedNodes().forEach((node: any) => {\r\n    if (!nodes.includes(node)) {\r\n      node.setSelected(false);\r\n    }\r\n  });\r\n}\r\n\r\nexport function isUrl(str: string): boolean {\r\n  const urlPattern = new RegExp(\r\n    '^(http(s)?:\\\\/\\\\/)?' + // http:// or https:// (optional)\r\n    '(www\\\\.)?' + // www. (optional)\r\n    '[a-zA-Z0-9@:%._\\\\+~#=]{2,256}\\\\.[a-z]{2,6}\\\\b' + // domain.tld\r\n    '([-a-zA-Z0-9@:%_\\\\+.~#?&//=]*)$', // path (optional)\r\n    'i' // Case-insensitive flag\r\n  );\r\n\r\n  return urlPattern.test(str);\r\n}\r\n\r\nexport function convertUrlsToLinks(\r\n  text: string,\r\n  applyLinkColor: boolean = false\r\n): string {\r\n  if (!text) return '';\r\n  const urlRegex =\r\n    /(\\b(?:https?:\\/\\/|www\\.|[a-zA-Z0-9-]+\\.[a-z]{2,})(?:[^\\s]*))/g;\r\n  return text.replace(urlRegex, (url) => {\r\n    let clickableUrl = url;\r\n    if (\r\n      !url.startsWith('http://') &&\r\n      !url.startsWith('https://') &&\r\n      !url.startsWith('www.')\r\n    ) {\r\n      clickableUrl = `http://${url}`;\r\n    }\r\n    return applyLinkColor\r\n      ? `<a href=\"${clickableUrl}\" target=\"_blank\" class=\"text-accent-green\">${url}</a>`\r\n      : `<a href=\"${clickableUrl}\" target=\"_blank\">${url}</a>`;\r\n  });\r\n}\r\n\r\nexport const atLeastOneSelectedValidator: ValidatorFn = (\r\n  control: AbstractControl\r\n): ValidationErrors | null => {\r\n  const controlValue = control.value;\r\n  if (!controlValue || controlValue.length < 1) {\r\n    return { atLeastOneSelected: true };\r\n  }\r\n  return null;\r\n};\r\n\r\nexport function generateFloorOptions(): string[] {\r\n  const floors = ['Upper Basement', 'Lower Basement', 'Ground'];\r\n  for (let i = 1; i <= 200; i++) {\r\n    floors.push(i.toString());\r\n  }\r\n  return floors;\r\n}\r\n\r\nexport function buildHttpParams(payload: any): any {\r\n  let params = new HttpParams();\r\n\r\n  Object.entries(payload).forEach(([key, value]: any) => {\r\n    if (value || value === 0) {\r\n      if (Array.isArray(value)) {\r\n        const indexes = getIndexes(key, value);\r\n        indexes.forEach((element: any) => {\r\n          params = params.append(key, element);\r\n        });\r\n      } else {\r\n        params = params.set(key, value);\r\n      }\r\n    }\r\n  });\r\n\r\n  return params;\r\n}\r\n\r\nexport function generateEnumList(\r\n  enumData: any,\r\n  displayNames?: string[]\r\n): Array<any> {\r\n  return Object.keys(enumData)\r\n    .filter((key) => isNaN(Number(key)) && key !== 'None')\r\n    .map((key, index) => ({\r\n      enumValue: enumData[key],\r\n      name: key,\r\n      displayName: displayNames?.[index] || key,\r\n    }));\r\n}\r\n\r\nexport function getFormattedLocation(location: string): string {\r\n  if (!location) {\r\n    return '';\r\n  }\r\n  location = location.replace(/^,+/, '');\r\n  location = location.replace(/,+/g, ',');\r\n  return location.trim();\r\n}\r\n\r\nexport function allowLandlineInput(event: KeyboardEvent) {\r\n  const allowed = /[0-9\\-]/;\r\n  if (!allowed.test(event.key)) {\r\n    event.preventDefault();\r\n  }\r\n}\r\n\r\n\r\nexport async function handleCachedData(\r\n  storeName: string,\r\n  cacheKey: string,\r\n  fetchLastModified: () => Promise<string | null>,\r\n  fetchData: () => Promise<any>,\r\n  buildRecord: (data: any, lastModified: string | null) => any,\r\n  getItems: (data: any, isLocalData: boolean) => any[],\r\n): Promise<any[]> {\r\n\r\n  const dbName = 'CachingDb';\r\n  let db: IDBDatabase | null = null;\r\n\r\n  try {\r\n    db = await new Promise((resolve, reject) => {\r\n      const request = indexedDB.open(dbName);\r\n      request.onsuccess = () => resolve(request.result);\r\n      request.onerror = () => reject(request.error);\r\n    });\r\n\r\n    if (!db.objectStoreNames.contains(storeName)) {\r\n      const newVersion = db.version + 1;\r\n      db.close();\r\n\r\n      try {\r\n        db = await new Promise((resolve, reject) => {\r\n          const request = indexedDB.open(dbName, newVersion);\r\n          request.onupgradeneeded = (event: any) => {\r\n            const upgradeDb = event.target.result;\r\n            if (!upgradeDb.objectStoreNames.contains(storeName)) {\r\n              upgradeDb.createObjectStore(storeName, { keyPath: 'id' });\r\n            }\r\n          };\r\n          request.onsuccess = () => resolve(request.result);\r\n          request.onerror = () => reject(request.error);\r\n        });\r\n\r\n        if (!db.objectStoreNames.contains(storeName)) {\r\n          console.error(`[Cache] Failed to create store: ${storeName}. Fetching fresh data.`);\r\n          if (db) db.close();\r\n          const fresh = await fetchData();\r\n          return getItems(fresh, false);\r\n        }\r\n\r\n      } catch (storeCreateErr) {\r\n        console.error(`[Cache] Store creation error for ${storeName}:`, storeCreateErr);\r\n        if (db) db.close();\r\n        const fresh = await fetchData();\r\n        return getItems(fresh, false);\r\n      }\r\n    }\r\n\r\n    const localData = await new Promise<any>((resolve) => {\r\n      try {\r\n        const tx = db!.transaction(storeName, 'readonly');\r\n        const store = tx.objectStore(storeName);\r\n        const getReq = store.get(cacheKey);\r\n        getReq.onsuccess = () => resolve(getReq.result || null);\r\n        getReq.onerror = () => resolve(null);\r\n      } catch (txErr) {\r\n        console.error(`[Cache] Transaction error reading from ${storeName}:`, txErr);\r\n        resolve(null);\r\n      }\r\n    });\r\n\r\n    const localLastModified = localData?.lastModified || null;\r\n\r\n    let serverLastModified: string | null = null;\r\n    try {\r\n      serverLastModified = await fetchLastModified();\r\n    } catch (err) {\r\n      console.error(`[Cache] Failed to fetch server lastModified:`, err);\r\n      if (db) db.close();\r\n\r\n      if (localData) {\r\n        return getItems(localData, true);\r\n      }\r\n      const fresh = await fetchData();\r\n      return getItems(fresh, false);\r\n    }\r\n\r\n    if (serverLastModified && serverLastModified !== localLastModified) {\r\n      const fresh = await fetchData();\r\n\r\n      try {\r\n        await new Promise<void>((resolve, reject) => {\r\n          const tx = db!.transaction(storeName, 'readwrite');\r\n          const store = tx.objectStore(storeName);\r\n          const newRecord = buildRecord(fresh, serverLastModified);\r\n\r\n          store.put(newRecord);\r\n\r\n          tx.oncomplete = () => resolve();\r\n          tx.onerror = () => reject(tx.error);\r\n        });\r\n      } catch (updateErr) {\r\n        console.error(`[Cache] Failed to update cache for ${storeName}:`, updateErr);\r\n      }\r\n\r\n      if (db) db.close();\r\n      return getItems(fresh, false);\r\n\r\n    } else {\r\n      if (db) db.close();\r\n      return getItems(localData, true);\r\n    }\r\n\r\n  } catch (err) {\r\n    console.error(`[Cache] DB error for ${storeName}:`, err);\r\n    if (db) db.close();\r\n\r\n    try {\r\n      const fresh = await fetchData();\r\n      return getItems(fresh, false);\r\n    } catch (fetchErr) {\r\n      console.error(`[Cache] Failed to fetch fresh data:`, fetchErr);\r\n      throw fetchErr;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n"]}, "metadata": {}, "sourceType": "module"}