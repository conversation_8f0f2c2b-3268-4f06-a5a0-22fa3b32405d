import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  OwlDateTimeModule,
  OwlNativeDateTimeModule,
} from '@danielmoncada/angular-datetime-picker';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { AgGridModule } from 'ag-grid-angular';
import { DragScrollModule } from 'ngx-drag-scroll';
import { NgxFileDropModule } from 'ngx-file-drop';
import { LottieModule } from 'ngx-lottie';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { WebcamModule } from 'ngx-webcam';

import { HttpLoaderFactory, playerFactory } from 'src/app/app.imports';
import { LeadsActionsComponent } from 'src/app/features/leads/leads-actions/leads-actions.component';
import { AmenitiesListComponent } from 'src/app/shared/components/amenities-list/amenities-list.component';
import { BrowseDropUploadComponent } from 'src/app/shared/components/browse-drop-upload/browse-drop-upload.component';
import { ExcelUploadComponent } from 'src/app/shared/components/excel-upload/excel-upload.component';
import { ExportAttendanceTrackerComponent } from 'src/app/shared/components/export-attendance-tracker/export-attendance-tracker.component';
import { ExportLeadsTrackerComponent } from 'src/app/shared/components/export-leads-tracker/export-leads-tracker.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { ExportPropertyTrackerComponent } from 'src/app/shared/components/export-property-tracker/export-property-tracker.component';
import { ExportReportsTrackerComponent } from 'src/app/shared/components/export-reports-tracker/export-reports-tracker.component';
import { ExportUsersTrackerComponent } from 'src/app/shared/components/export-users-tracker/export-users-tracker.component';
import { FileUploadComponent } from 'src/app/shared/components/file-upload/file-upload.component';
import { FormErrorModule } from 'src/app/shared/components/form-error-wrapper/form-error.module';
import { GalleryCarouselComponent } from 'src/app/shared/components/gallery-carousel/gallery-carousel.component';
import { IvrCallingComponent } from 'src/app/shared/components/ivr-calling/ivr-calling.component';
import { LeadInformationComponent } from 'src/app/shared/components/lead-information/lead-information.component';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { Loader } from 'src/app/shared/components/loader/loader.component';
import { MigrationBulkUploadComponent } from 'src/app/shared/components/migration-bulk-upload/migration-bulk-upload.component';
import { PaginationComponent } from 'src/app/shared/components/pagination/pagination.component';
import { PropertyDetailsSubmitComponent } from 'src/app/shared/components/property-details-submit/property-details-submit.component';
import { QrGeneratorComponent } from 'src/app/shared/components/qr-code/qr-generator.component';
import { RatingComponent } from 'src/app/shared/components/rating/rating.component';
import { RefreshAppComponent } from 'src/app/shared/components/refresh-app/refresh-app.component';
import { RemoveDocumentComponent } from 'src/app/shared/components/remove-document/remove-document.component';
import { SaveChangesComponent } from 'src/app/shared/components/save-changes/save-changes.component';
import { ShareExternalComponent } from 'src/app/shared/components/share-external/share-external.component';
import { UnassignedComponent } from 'src/app/shared/components/unassigned/unassigned.component';
import { UnderConstructionComponent } from 'src/app/shared/components/under-construction/under-construction.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { WebcamComponent } from 'src/app/shared/components/web-cam/web-cam.component';
import { ClickThrottleDirective } from 'src/app/shared/directives/click-throttle.directive';
import { ResizableDropdownDirective } from 'src/app/shared/directives/dropdownPanel-Resize.directive';
import { AppImageURLDirective } from 'src/app/shared/directives/image.directive';
import { InViewDirective } from 'src/app/shared/directives/in-view.directive';
import { IntegrationAssignmentComponent } from '../features/global-config/integration/integration-assignment/integration-assignment.component';
import { ReprtAutomationExportTrackerComponent } from '../features/global-config/settings/module/report-automation/reprt-automation-export-tracker/reprt-automation-export-tracker.component';
import { BookingFormComponent } from '../features/leads/booking-form/booking-form.component';
import { BulkLeadsEmailShareComponent } from '../features/leads/bulk-leads-email-share/bulk-leads-email-share.component';
import { BulkUpdateStatusLeadsComponent } from '../features/leads/bulk-update-status-leads/bulk-update-status-leads.component';
import { CustomStatusChangeComponent } from '../features/leads/custom-status-change/custom-status-change.component';
import { CustomizationAddLeadComponent } from '../features/leads/customization-add-lead/customization-add-lead.component';
import { DuplicateAssignInfoComponent } from '../features/leads/duplicate-assign-info/duplicate-assign-info.component';
import { ExcelUploadedStatusComponent } from '../features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { IndividualReassignComponent } from '../features/leads/individual-reassign/individual-reassign.component';
import { LeadAppointmentComponent } from '../features/leads/lead-appointment/lead-appointment.component';
import { LeadBulkShareComponent } from '../features/leads/lead-bulk-share/lead-bulk-share.component';
import { LeadCallComponent } from '../features/leads/lead-call/lead-call.component';
import { LeadHistoryComponent } from '../features/leads/lead-history/lead-history.component';
import { LeadNotesComponent } from '../features/leads/lead-notes/lead-notes.component';
import { LeadsBulkUpdateComponent } from '../features/leads/leads-bulk-update/leads-bulk-update';
import { LeadsDocumentUploadComponent } from '../features/leads/leads-document-upload/leads-document-upload.component';
import { LeadsEmailShareComponent } from '../features/leads/leads-email-share/leads-email-share.component';
import { LeadsTemplateShareComponent } from '../features/leads/leads-template-share/leads-template-share.component';
import { MatchingPropertiesComponent } from '../features/leads/matching-properties/matching-properties.component';
import { PropertiesShareDataComponent } from '../features/leads/matching-properties/properties-share-data/properties-share-data.component';
import { MeetingSiteVisitDoneComponent } from '../features/leads/meeting-site-visit-done/meeting-site-visit-done.component';
import { StatusChangeComponent } from '../features/leads/status-change/status-change.component';
import { WhatsappChatBulkComponent } from '../features/leads/whatsapp-chat-bulk/whatsapp-chat-bulk.component';
import { WhatsappChatComponent } from '../features/leads/whatsapp-chat/whatsapp-chat.component';
import { AppTourComponent } from './components/app-tour/app-tour.component';
import { ApplicationLoaderComponent } from './components/application-loader/application-loader.component';
import { BulkOperationTrackerComponent } from './components/bulk-operation-tracker/bulk-operation-tracker.component';
import { BulkUploadComponent } from './components/bulk-upload/bulk-upload.component';
import { CustomAmenitiesAttributesComponent } from './components/custom-amenities-attributes/custom-amenities-attributes.component';
import { DeletedUsersTrackerComponent } from './components/deleted-users-tracker/deleted-users-tracker.component';
import { ExportFacebookTrackerComponent } from './components/export-facebook-tracker/export-facebook-tracker.component';
import { ExportMarketingTrackerComponent } from './components/export-marketing-tracker/export-marketing-tracker.component';
import { ExportProjectTrackerComponent } from './components/export-project-tracker/export-project-tracker.component';
import { ExportTeamsTrackerComponent } from './components/export-teams-tracker/export-teams-tracker.component';
import { InformationComponent } from './components/information/information.component';
import { PossessionFilterComponent } from './components/possession-filter/possession-filter.component';
import { QualityScoreChartComponent } from './components/quality-score-chart/quality-score-chart.component';
import { SavedFilterComponent } from './components/saved-filter/saved-filter.component';
import { DebounceInputDirective } from './directives/debounce-input.directive';
import { RelativeDayPipe } from './directives/relative-day.pipe';
import { RelativeTimePipe } from './directives/relative-time.pipe';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';

@NgModule({
  declarations: [
    ExcelUploadComponent,
    ShareExternalComponent,
    AmenitiesListComponent,
    FileUploadComponent,
    PaginationComponent,
    RatingComponent,
    Loader,
    UserConfirmationComponent,
    BrowseDropUploadComponent,
    UnassignedComponent,
    AppImageURLDirective,
    InViewDirective,
    ResizableDropdownDirective,
    IvrCallingComponent,
    SaveChangesComponent,
    RemoveDocumentComponent,
    PropertyDetailsSubmitComponent,
    UnderConstructionComponent,
    GalleryCarouselComponent,
    ExportMailComponent,
    QrGeneratorComponent,
    LeadInformationComponent,
    ExportLeadsTrackerComponent,
    ExportReportsTrackerComponent,
    RefreshAppComponent,
    ExportPropertyTrackerComponent,
    ExportAttendanceTrackerComponent,
    ExportUsersTrackerComponent,
    UserAlertPopupComponent,
    WebcamComponent,
    MigrationBulkUploadComponent,
    ClickThrottleDirective,
    LeadPreviewComponent,
    LeadsActionsComponent,
    WhatsappChatComponent,
    IndividualReassignComponent,
    LeadHistoryComponent,
    StatusChangeComponent,
    LeadNotesComponent,
    LeadCallComponent,
    LeadsBulkUpdateComponent,
    LeadBulkShareComponent,
    MatchingPropertiesComponent,
    PropertiesShareDataComponent,
    BulkUpdateStatusLeadsComponent,
    LeadsDocumentUploadComponent,
    LeadsTemplateShareComponent,
    MeetingSiteVisitDoneComponent,
    ExcelUploadedStatusComponent,
    DuplicateAssignInfoComponent,
    LeadAppointmentComponent,
    BulkUploadComponent,
    BookingFormComponent,
    CustomStatusChangeComponent,
    BulkLeadsEmailShareComponent,
    LeadsEmailShareComponent,
    WhatsappChatBulkComponent,
    RelativeTimePipe,
    RelativeDayPipe,
    ExportFacebookTrackerComponent,
    ExportTeamsTrackerComponent,
    BulkOperationTrackerComponent,
    DeletedUsersTrackerComponent,
    CustomAmenitiesAttributesComponent,
    ExportMarketingTrackerComponent,
    AppTourComponent,
    CustomizationAddLeadComponent,
    InformationComponent,
    DebounceInputDirective,
    ApplicationLoaderComponent,
    SavedFilterComponent,
    IntegrationAssignmentComponent,
    ExportProjectTrackerComponent,
    ReprtAutomationExportTrackerComponent,
    ExportProjectTrackerComponent,
    IntegrationAssignmentComponent,
    PossessionFilterComponent,
    QualityScoreChartComponent,
  ],
  imports: [
    FormsModule,
    CommonModule,
    NgSelectModule,
    FormErrorModule,
    NgxFileDropModule,
    ReactiveFormsModule,
    WebcamModule,
    BsDropdownModule.forRoot(),
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      // isolate: true,
      // to extend the I18n to use global en.json
      extend: true,
    }),
    LottieModule.forRoot({ player: playerFactory }),
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    MatProgressBarModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    AgGridModule,
    DragScrollModule,
    NgxMatIntlTelInputComponent,
    GoogleMapsModule,
  ],
  exports: [
    NgSelectModule,
    FormErrorModule,
    NgxFileDropModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    ExcelUploadComponent,
    ShareExternalComponent,
    AmenitiesListComponent,
    RatingComponent,
    FileUploadComponent,
    PaginationComponent,
    Loader,
    UserConfirmationComponent,
    BrowseDropUploadComponent,
    AppImageURLDirective,
    InViewDirective,
    ResizableDropdownDirective,
    SaveChangesComponent,
    RemoveDocumentComponent,
    PropertyDetailsSubmitComponent,
    UnderConstructionComponent,
    GalleryCarouselComponent,
    LeadInformationComponent,
    WebcamComponent,
    ExportLeadsTrackerComponent,
    ClickThrottleDirective,
    RelativeTimePipe,
    RelativeDayPipe,
    MigrationBulkUploadComponent,
    WhatsappChatComponent,
    LeadPreviewComponent,
    LeadsBulkUpdateComponent,
    LeadsActionsComponent,
    ApplicationLoaderComponent,
    MigrationBulkUploadComponent,
    CustomAmenitiesAttributesComponent,
    ExportTeamsTrackerComponent,
    AppTourComponent,
    DebounceInputDirective,
    SavedFilterComponent,
    IntegrationAssignmentComponent,
    PossessionFilterComponent,
    QualityScoreChartComponent,
    BsDropdownModule
  ],
  providers: [LeadPreviewComponent],
})
export class SharedModule { }
