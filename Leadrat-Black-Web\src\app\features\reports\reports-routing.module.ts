import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { ActivityDashboardReportComponent } from './combined/activity-dashboard-report/activity-dashboard-report.component';
import { CombinedActivityReportComponent } from './combined/activity-dashboard-report/combined-activity-report/combined-activity-report.component';
import { DataActivityReportComponent } from './data/data-activity-report/data-activity-report.component';
import { DataCallReportComponent } from './data/data-call-report/data-call-report.component';
import { DataProjectReportComponent } from './data/data-project-report/data-project-report.component';
import { DataSourceReportComponent } from './data/data-source-report/data-source-report.component';
import { DataStatusReportComponent } from './data/data-status-report/data-status-report.component';
import { DataSubsourceReportComponent } from './data/data-sub-source-report/data-sub-source-report.component';
import { ActivityReportComponent } from './leads/activity-report/activity-report.component';
import { AgencyReportComponent } from './leads/agency-report/agency-report.component';
import { CallReportComponent } from './leads/call-report/call-report.component';
import { CityReportComponent } from './leads/city-report/city-report.component';
import { ProjectReportComponent } from './leads/project-report/project-report.component';
import { ProjectSubstatusComponent } from './leads/project-substatus/project-substatus.component';
import { ReceivedSourceComponent } from './leads/received-source/received-date-source.component';
import { SourceReportComponent } from './leads/source-report/source-report.component';
import { StatusReportComponent } from './leads/status-report/status-report.component';
import { SubSourceReportComponent } from './leads/sub-source-report/sub-source-report.component';
import { SubStatusReportComponent } from './leads/sub-status-report/sub-status-report.component';
import { SubstatusSubsourceComponent } from './leads/substatus-subsource/substatus-subsource.component';
import { UserSourceReportComponent } from './leads/user-source-report/user-source-report.component';
import { UserSubsourceReportComponent } from './leads/user-subsource-report/user-subsource-report.component';
import { VisitMeetingReportComponent } from './leads/visit-meeting-report/visit-meeting-report.component';
import { reportsRootLayoutComponent } from './reports-root.component';
import { ReportGraphComponent } from './reports-graph/reports-graph.component';

export const routes: Routes = [
  { path: '', redirectTo: 'status-report', pathMatch: 'full' },
  { path: 'status-report', component: StatusReportComponent },
  { path: 'project-report', component: ProjectReportComponent },
  { path: 'visit-meeting-report', component: VisitMeetingReportComponent },
  { path: 'source-report', component: SourceReportComponent },
  { path: 'sub-source-report', component: SubSourceReportComponent },
  { path: 'agency-report', component: AgencyReportComponent },
  { path: 'activity-report', component: ActivityReportComponent },
  { path: 'sub-status-report', component: SubStatusReportComponent },
  { path: 'substatus-subsource', component: SubstatusSubsourceComponent },
  { path: 'project-substatus', component: ProjectSubstatusComponent },
  { path: 'call-report', component: CallReportComponent },
  { path: 'user-source-report', component: UserSourceReportComponent },
  { path: 'user-subsource-report', component: UserSubsourceReportComponent },
  { path: 'received-date-source', component: ReceivedSourceComponent },
  { path: 'data-status-report', component: DataStatusReportComponent },
  { path: 'data-project-report', component: DataProjectReportComponent },
  { path: 'data-source-report', component: DataSourceReportComponent },
  { path: 'data-subsource-report', component: DataSubsourceReportComponent },
  { path: 'data-call-report', component: DataCallReportComponent },
  { path: 'data-activity-report', component: DataActivityReportComponent },
  { path: 'merge-activity-report', component: ActivityDashboardReportComponent },
  { path: 'city-report', component: CityReportComponent },
  {
    path: 'combined-activity-report',
    component: CombinedActivityReportComponent,
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class reportsRoutingModule { }

export const REPORTS_DECLARATIONS = [
  reportsRootLayoutComponent,
  StatusReportComponent,
  ProjectReportComponent,
  VisitMeetingReportComponent,
  SourceReportComponent,
  SubSourceReportComponent,
  AgencyReportComponent,
  ActivityReportComponent,
  SubStatusReportComponent,
  SubstatusSubsourceComponent,
  ProjectSubstatusComponent,
  CallReportComponent,
  UserSourceReportComponent,
  UserSubsourceReportComponent,
  ReceivedSourceComponent,
  DataStatusReportComponent,
  DataProjectReportComponent,
  DataSourceReportComponent,
  DataSubsourceReportComponent,
  DataCallReportComponent,
  DataActivityReportComponent,
  CombinedActivityReportComponent,
  CityReportComponent,
  ActivityDashboardReportComponent,
  ReportGraphComponent
];
