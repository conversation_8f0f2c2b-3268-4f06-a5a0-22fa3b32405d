import { Component, EventE<PERSON>ter, On<PERSON><PERSON><PERSON>, OnInit, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { Title } from '@angular/platform-browser';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { AddTeamComponent } from 'src/app/features/teams/manage-team/add-team/add-team.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { DeleteTeams, FetchTeamExportTracker, FetchTeamList, TeamsFilterPayload } from 'src/app/reducers/teams/teams.actions';
import { getAllTeams, getAllTeamsIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment } from 'src/environments/environment';
import { ExportTeamsTrackerComponent } from 'src/app/shared/components/export-teams-tracker/export-teams-tracker.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'manage-team',
  templateUrl: './manage-team.component.html',
})
export class ManageTeamComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  searchSubject = new Subject<string>();
  allTeams: any[];
  selectedTeams: any[] = [];
  public pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  offset: number = 0;
  title: any;
  gridOptions: any;
  gridApi: any;
  gridColumnApi: any;
  rowData: any = [];
  filteredTeams: any[] = [];
  defaultColDef: any;
  s3BucketUrl: string = environment.s3ImageBucketURL;
  canView: boolean = false;
  canAdd: boolean = false;
  canEdit: boolean = false;
  canDelete: boolean = false;
  canExport: boolean = false;
  canSearch: boolean = false;
  canBulkDelete: boolean = false;
  teamListIsLoading: boolean = true;
  canViewAllLeads: boolean = false;

  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
    private modalService: BsModalService,
    public router: Router,
    public metaTitle: Title,
    public trackingService: TrackingService
  ) {
  }

  ngOnInit() {
    this.metaTitle.setTitle('CRM | Teams');
    this.headerTitle.setLangTitle('Teams');
    this._store.dispatch(new TeamsFilterPayload({}));
    this._store.dispatch(new FetchTeamList({}));
    this.searchSubject.pipe(debounceTime(300)).subscribe(() => {
      this.trackingService.trackFeature(`Web.Team.DataEntry.Search.DataEntry`)
    })

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canAdd = permissionsSet.has('Permissions.Teams.Create');
        this.canExport = permissionsSet.has('Permissions.Teams.Export');
        this.canView = permissionsSet.has('Permissions.Teams.View');
        this.canEdit = permissionsSet.has('Permissions.Teams.Update');
        this.canDelete = permissionsSet.has('Permissions.Teams.Delete');
        this.canBulkDelete = permissionsSet.has('Permissions.Teams.BulkDelete');
        this.canViewAllLeads = permissionsSet.has('Permissions.Leads.ViewAllLeads');
      });

    this._store
      .select(getAllTeams)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allTeams = data;
        this.filteredTeams = this.allTeams;
        const teamCount = this.allTeams?.length || 0;
        this.title = `All Teams (${teamCount})`;
      });

    this._store
      .select(getAllTeamsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.teamListIsLoading = isLoading;
      });
    this.trackingService.trackFeature(`Web.Team.Page.Team.Visit`)
  }

  toggleTeamSelection(team: any, event: any): void {
    if (event.target.checked) {
      this.selectedTeams.push(team);
    } else {
      this.selectedTeams = this.selectedTeams.filter(t => t !== team);
    }
  }

  editTeam(data: any) {
    this.trackingService.trackFeature(`Web.Team.Button.Edit.Click`)
    const initialState = {
      selectedTeam: data,
    };
    this.modalRef = this.modalService.show(AddTeamComponent, {
      class: 'right-modal modal-350',
      initialState,

    });
  }

  removeTeam(id: string): void {
    const node = this.gridApi?.getSelectedNodes()?.filter(
      (team: any) => team?.data?.id === id
    );
    this.gridApi?.deselectNode(node?.[0]);

    this.selectedTeams = this.selectedTeams?.filter(
      (team: any) => team?.id !== id
    );
    if (this.selectedTeams?.length <= 0) {
      this.bulkDeleteModalRef.hide();
    }
  }

  openConfirmDeleteModal(teamName: string, teamId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: teamName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeTeam(teamId);
        }
      });
    }
  }

  navigateToLeads(user: any): void {
    if (user && Array.isArray(user?.users)) {
      const usersIds = user?.users?.map((item: any) => {
        return item?.id;
      });
      const payload = {
        assignTo: usersIds
      };
      this.router.navigate(['/leads'], { queryParams: payload });
    }
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    let initialState: any = {
      data: this.selectedTeams,
      class: 'right-modal modal-300',
    };
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
  }

  deleteTeam(data: any) {
    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: `Delete team permanently?`,
        message: `You are about to delete the team <b>"${data?.teamName}"</b> permanently. 🚫`,
        description: 'Delete all information associated with this team. Once the team is deleted, this action is irreversible, and team recovery will not be possible.',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );

    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.trackingService.trackFeature(`Web.Team.Button.Delete.Click`)
          this._store.dispatch(new DeleteTeams([data?.id]));
        }
      });
    }
  }

  bulkDelete(): void {
    this.trackingService.trackFeature(`Web.Team.Button.BulkDelete.Click`)
    if (this.bulkDeleteModalRef)
      this.bulkDeleteModalRef.hide();

    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: `Delete ${this.selectedTeams?.length} team(s) permanently?`,
        message: `You are about to delete the ${this.selectedTeams?.length} team(s) permanently. 🚫`,
        description: 'Delete all information associated with this team(s). Once the team(s) are deleted, this action is irreversible, and team(s) recovery will not be possible.',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );

    if (this.modalRef?.onHide)
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          const ids = this.selectedTeams
            .map((team: any) => team?.id);
          this._store.dispatch(new DeleteTeams(ids));
          this.deselectAll();
        }
      });
  }

  deselectAll() {
    this.selectedTeams = [];
  }

  isTeamSelected(team: any): boolean {
    return this.selectedTeams.some(t => t.id === team.id);
  }

  navigateToMember(data: any) {
    this.trackingService.trackFeature(`Web.Team.Button.ViewMember.Click`)
    const id: string = data?.id;
    this.router.navigate(['teams/manage-member', id], {
      state: { id }
    });
  }

  onSearchTeam(searchTerm: string): void {
    this.searchSubject.next('')
    if (!searchTerm) {
      this.filteredTeams = this.allTeams;
    } else {
      this.filteredTeams = this.allTeams.filter(team =>
        team.teamName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
  }

  addTeamModal() {
    this.trackingService.trackFeature(`Web.Team.Button.AddTeam.Click`)
    this.modalService.show(AddTeamComponent, {
      class: 'right-modal modal-400 ip-modal-unset',
    });
  }

  openTeamsTracker() {
    this.trackingService.trackFeature(`Web.Team.Button.ExportTracker.Click`)
    this._store.dispatch(new FetchTeamExportTracker(1, 10));
    this.modalService.show(ExportTeamsTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
