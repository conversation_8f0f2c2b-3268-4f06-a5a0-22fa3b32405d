<!-- quality-score-chart.component.html -->
<div class="d-flex align-center">
    <div class="position-relative">
        <svg width="30" height="30" viewBox="0 0 60 60">
            <!-- Background segments (inactive) -->
            <g *ngFor="let segment of getSegmentsArray()">
                <path [attr.d]="getSegmentPath(segment)" fill="none" stroke="#e6e6e6" stroke-width="5"
                    stroke-linecap="round">
                </path>
            </g>

            <!-- Active segments -->
            <g *ngFor="let segment of getSegmentsArray()">
                <path *ngIf="isSegmentActive(segment)" [attr.d]="getSegmentPath(segment)" fill="none"
                    [attr.stroke]="getScoreColor()" stroke-width="5" stroke-linecap="round">
                </path>
            </g>

            <!-- Center text -->
            <text x="30" y="30" text-anchor="middle" dominant-baseline="middle" font-size="12" font-weight="bold">
                {{score}}%
            </text>
        </svg>
    </div>
    <h5 class="ml-10 ph-d-none" [style.color]="getScoreColor()">
        {{score}}%
    </h5>
    <h6 class="ml-4 ph-d-none fw-400">Quality Score</h6>
</div>