$sizes: (
    ) !default;
$sizes: map-merge((0:0px,
            8: 8px,
            10: 10px,
            14pr: 14%,
            16: 16px,
            16pr: 16%,
            18: 18px,
            18pr: 18%,
            20px: 20px,
            24: 24px,
            20: 20%,
            26: 26px,
            28: 28px,
            30px: 30px,
            30: 30%,
            32: 32px,
            35: 35%,
            35px: 35px,
            33: 33%,
            40pr: 40%,
            40: 40px,
            45px: 45px,
            45: 45%,
            46: 46px,
            47px: 47px,
            48: 48px,
            50: 50%,
            50px: 50px,
            52: 52px,
            55px: 55px,
            55 :55%,
            60pr: 60%,
            60: 60px,
            62pr: 62%,
            65: 65%,
            66: 66%,
            67: 67%,
            68: 68px,
            70: 70%,
            70px: 70px,
            75px: 75px,
            80pr: 80%,
            80: 80px,
            85: 85px,
            88: 88px,
            90: 90px,
            90pr:90%,
            95: 95px,
            99: 99%,
            100px: 100px,
            105: 105px,
            110: 110px,
            115: 115px,
            120: 120px,
            125: 125px,
            130: 130px,
            135: 135px,
            137: 137px,
            138: 138px,
            140: 140px,
            150: 150px,
            160: 160px,
            170: 170px,
            180: 180px,
            190: 190px,
            200: 200px,
            210: 210px,
            220: 220px,
            240: 240px,
            250: 250px,
            260: 260px,
            270: 270px,
            280: 280px,
            300: 300px,
            310: 310px,
            330: 330px,
            345: 345px,
            350: 350px,
            355: 355px,
            360: 360px,
            365: 365px,
            370: 370px,
            375: 375px,
            380: 380px,
            385: 385px,
            390: 390px,
            400: 400px,
            430: 430px,
            450: 450px,
            460: 460px,
            480: 480px,
            490: 490px,
            500: 500px,
            550: 550px,
            590: 590px,
            600: 600px,
            650: 650px,
            661: 661px,
            685: 685px,
            700: 700px,
            710: 710px,
            730: 730px,
            750: 750px,
            770: 770px,
            900: 900px,
            1000: 1000px,
            1200: 1200px,
            1250: 1250px,
            1260: 1260px,
            auto: auto,
        ),
        $sizes
);

@each $prop,
$abbrev in (width: w,
    height: h,
    min-width: min-w,
    max-width: max-w,
    min-height: min-h,
    max-height: max-h) {

    @each $size,
    $length in $sizes {
        .#{$abbrev}-#{$size} {
            #{$prop}: $length !important;
        }
    }
}

//height, width calcation
$additional_sizes: (
    0: 0,
    16: 16px,
    30:30px,
    33:33px,
    40:40px,
    44:44px,
    46:46px,
    48:48px,
    60:60px,
    64:64px,
    70:70px,
    74:74px,
    80:80px,
    88:88px,
    90:90px,
    91:91px,
    97:97px,
    100:100px,
    107:107px,
    108:108px,
    110:110px,
    114:114px,
    115:115px,
    130:130px,
    140:140px,
    144:144px,
    150:150px,
    152:152px,
    155:155px,
    160:160px,
    170:170px,
    176:176px,
    180:180px,
    182:182px,
    186:186px,
    189:189px,
    190:190px,
    200:200px,
    206:206px,
    207:207px,
    210:210px,
    215:215px,
    220:220px,
    225:225px,
    233:233px,
    240:240px,
    246:246px,
    250:250px,
    255:255px,
    260:260px,
    265:265px,
    267:267px,
    270:270px,
    277:277px,
    280:280px,
    282:282px,
    285:285px,
    290:290px,
    293:293px,
    294:294px,
    298:298px,
    300:300px,
    307:307px,
    309:309px,
    310:310px,
    315:315px,
    320:320px,
    323:323px,
    332:332px,
    333:333px,
    337:337px,
    340:340px,
    342:342px,
    347:347px,
    350:350px,
    354:354px,
    355:355px,
    360:360px,
    370:370px,
    372:372px,
    377:377px,
    380:380px,
    388:388px,
    390:390px,
    393:393px,
    400:400px,
    410:410px,
    415:415px,
    420:420px,
    423:423px,
    433:433px,
    440:440px,
    442:442px,
    448:448px,
    450:450px,
    455:455px,
    460:460px,
    465:465px,
    469:469px,
    470:470px,
    480:480px,
    485:485px,
    490:490px,
    493:493px,
    505:505px,
    547:547px,
    550:550px,
    560:560px,
    570:570px,
    600:600px,
    612:612px,
    620:620px,
    660:660px,
    680:680px,
    735:735px,
    770:770px,
    790:790px,
);

@each $prop, $abbrev in (height: h, min-height: min-h, max-height: max-h) {
    @each $size, $length in $additional_sizes {
        .#{$abbrev}-100-#{$size} {
            #{$prop}:calc(100dvh - #{$length}) !important;
        }
    }
}

@each $prop, $abbrev in (width: w, min-width: min-w, max-width: max-w) {
    @each $size, $length in $additional_sizes {
        .#{$abbrev}-100-#{$size} {
            #{$prop}:calc(100dvw - #{$length}) !important;
        }
    }
}