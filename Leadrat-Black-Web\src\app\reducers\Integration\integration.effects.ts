import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { EXCEPTION_MESSAGES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import {
  AddBayut,
  AddCommonFloor,
  AddDubizzle,
  AddFacebookAccount,
  AddIntegration,
  AddIntegrationGoogleLanding,
  AddIntegrationGoogleLandingSuccess,
  AddIntegrationGoogleLead,
  AddIntegrationGoogleLeadSuccess,
  AddIntegrationSuccess,
  AddJustLead,
  AddMicrositeLead,
  AddMicrositeLeadSuccess,
  AddProjectMicrositeLead,
  AddProjectMicrositeLeadSuccess,
  AddPropertyFinder,
  AddWebhook,
  AddWebhookSuccess,
  AgencyName,
  AgencyNameSuccess,
  AutomateFBAdIntegration,
  AutomateFBAdIntegrationSuccess,
  AutomateFBFormsIntegration,
  AutomateFBFormsIntegrationSuccess,
  AutomateIntegration,
  AutomateIntegrationSuccess,
  ClickToCall,
  CommonClickToCall,
  CommonIvrIntegration,
  CommonIvrIntegrationSuccess,
  DeleteFacebookIntegration,
  DeleteGoogleIntegration,
  DeleteIntegration,
  DoesExistAccountName,
  DoesExistAccountNameSuccess,
  FetchAgencyNameList,
  FetchAgencyNameListSuccess,
  FetchAgents,
  FetchAgentsSuccess,
  FetchBulkLeadsFromFb,
  FetchBulkLeadsFromFbSuccess,
  FetchExportFacebookStatus,
  FetchExportFacebookStatusSuccess,
  FetchFacebookAccounts,
  FetchFacebookAccountsSuccess,
  FetchFacebookMarketing,
  FetchFacebookMarketingSuccess,
  FetchFbAccount,
  FetchFbAccountForms,
  FetchFbAccountFormsSuccess,
  FetchFbAccountSuccess,
  FetchGoogleCampaignAccounts,
  FetchGoogleCampaignAccountsSuccess,
  FetchGoogleCampaignMarketing,
  FetchGoogleCampaignMarketingSuccess,
  DeleteGoogleCampaignAccount,
  DeleteGoogleCampaignAccountSuccess,
  FetchGoogleIntegrationById,
  FetchGoogleIntegrationByIdSuccess,
  FetchGoogleIntegrationList,
  FetchGoogleIntegrationListSuccess,
  GoogleCampaignLogin,
  GoogleCampaignLoginSuccess,
  FetchIVRIntegrationCount,
  FetchIVRIntegrationCountSuccess,
  FetchIVRList,
  FetchIVRListSuccess,
  FetchIntegrationAssignmentDetails,
  FetchIntegrationAssignmentDetailsSuccess,
  FetchIntegrationById,
  FetchIntegrationByIdSuccess,
  FetchIntegrationCount,
  FetchIntegrationCountSuccess,
  FetchIntegrationList,
  FetchIntegrationListSuccess,
  FetchIntegrationProjects,
  FetchIntegrationProjectsSuccess,
  FetchIvrDetailsById,
  FetchIvrDetailsByIdSuccess,
  FetchIvrServiceProviders,
  FetchIvrServiceProvidersSuccess,
  FetchSubscribedForms,
  FetchSubscribedFormsSuccess,
  FetchVNAssignment,
  FetchVNAssignmentSuccess,
  FetchVirtualNos,
  FetchVirtualNosSuccess,
  FetchWebhook,
  FetchWebhookAccount,
  FetchWebhookAccountSuccess,
  FetchWebhookSuccess,
  GmailIntegration,
  IntegrationActionTypes,
  IntegrationEmail,
  IntegrationEmailSuccess,
  IvrIntegration,
  IvrIntegrationSuccess,
  MakePrimary,
  ProjectList,
  ProjectListSuccess,
  SyncAdsOfAnFBAccount,
  ToggleFBSubscription,
  UpdateBayut,
  UpdateCommonFloor,
  UpdateDubizzle,
  UpdateIVRAccount,
  UpdateJustLead,
  UpdatePixelAccount,
  UpdatePropertyFinder,
  UpdateWebhook,
} from 'src/app/reducers/Integration/integration.actions';
import {
  FetchLeadList,
  FetchLeadListSuccess,
} from 'src/app/reducers/lead/lead.actions';
import { IntegrationService } from 'src/app/services/controllers/integration.service';
import { IvrCallingComponent } from 'src/app/shared/components/ivr-calling/ivr-calling.component';
import { FetchAllData, FetchAllDataSuccess } from '../data/data-management.actions';
import { CommonService } from 'src/app/services/shared/common.service';

@Injectable()
export class IntegrationEffects {
  addIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_INTEGRATION),
      map((action: AddIntegration) => action.payload),
      switchMap((data: any) => {
        return this.api.getExcelFile(data).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              const { accountName, ...filteredData } = data;
              if (![38, 39, 40].includes(data.source)) {
                this._store.dispatch(
                  new IntegrationEmail({ ...filteredData, accountId: resp.data }),
                );
              }
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  integrationEmail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.INTEGRATION_EMAIL),
      map((action: IntegrationEmail) => action.payload),
      switchMap((data: any) => {
        return this.api.IntegrationEmail(data).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Email Send Successfully`
              );
              return new IntegrationEmailSuccess(resp.data);
            }
            return new IntegrationEmailSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  googleLandingIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.GOOGLE_ADS_LANDING_PAGE),
      map((action: AddIntegrationGoogleLanding) => action.payload),
      switchMap((action: AddIntegrationGoogleLanding) => {
        return this.api.googleAdsLandingPage(action).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: 4 }));
              return new AddIntegrationGoogleLandingSuccess(resp.data);
            }
            return new AddIntegrationGoogleLandingSuccess({});
          }),
          catchError((err: any) => {
            if (err.status === 0) {
              this._notificationService.error(
                'Remove Ad Blocker and try again'
              );
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  googleLeadIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.GOOGLE_ADS_LEAD_FORM),
      map((action: AddIntegrationGoogleLead) => action.payload),
      switchMap((data: any) => {
        return this.api.googleAdsLeadForm(data).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              const { accountName, ...filteredData } = data;
              this._store.dispatch(new FetchGoogleIntegrationList(filteredData));
              this._store.dispatch(
                new IntegrationEmail({ ...filteredData, accountId: resp.data }),
              );
              return new AddIntegrationGoogleLeadSuccess(resp.data);
            }
            return new AddIntegrationGoogleLeadSuccess({});
          }),
          catchError((err: any) => {
            if (err.status === 0) {
              this._notificationService.error(
                'Remove Ad Blocker and try again'
              );
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  ivrIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.IVR_INTEGRATION),
      map((action: IvrIntegration) => action.payload),
      switchMap((action: IvrIntegration) => {
        return this.api.ivrIntegration(action).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: 1 }));
              return new IvrIntegrationSuccess(resp.data);
            }
            return new IvrIntegrationSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  commonIvrIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.COMMON_IVR_INTEGRATION),
      map((action: CommonIvrIntegration) => action.payload),
      switchMap((action: CommonIvrIntegration) => {
        return this.api.commonIvrIntegration(action).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIVRList());
              return new CommonIvrIntegrationSuccess(resp.data);
            }
            return new CommonIvrIntegrationSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  gmailIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.GMAIL_INTEGRATION),
      map((action: GmailIntegration) => action.resource),
      switchMap((action: GmailIntegration) => {
        return this.api.gmailIntegration(action).pipe(
          map((resp: any) => {
            if (resp) {
              this._notificationService.success(
                `Email successfully Integrated.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: 12 }));
              this._store.dispatch(new FetchIntegrationCount());
              return new FetchIntegrationCountSuccess(resp.data);
            }
            return new FetchIntegrationCountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIntegrationList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_INTEGRATION_LIST),
      map((action: FetchIntegrationList) => action.payload),
      switchMap((data: any) => {
        return this.api
          .getIntegrationList(data)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchIntegrationListSuccess(resp);
              }
              return new FetchIntegrationListSuccess([]);
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getIVRIntegrationById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_IVR_DETAILS_BY_ID),
      map((action: FetchIvrDetailsById) => action.id),
      switchMap((data: any) => {
        return this.api.getIVRDetailsById(data).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchIvrDetailsByIdSuccess(resp.data);
            }
            return new FetchIvrDetailsByIdSuccess('');
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  clickToCall$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.CLICK_TO_CALL),
      switchMap((action: ClickToCall) => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        this.modalRef = this.modalService.show(IvrCallingComponent, {
          class: 'modal-250 modal-dialog-centered modal-border-green',
        });
        return this.api.clickToCall(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this.modalRef.hide();
              this._notificationService.success(resp.data.message);
              this.modalService.hide();
              if (action.payload?.leadId) {
                return new FetchLeadList();
              } else {
                return new FetchAllData();
              }
            }
            if (action.payload?.leadId) {
              return new FetchLeadListSuccess();
            } else {
              return new FetchAllDataSuccess();
            }
          }),
          catchError((err: any) => {
            if (err.error?.messages?.[0]?.includes('GatewayTimeout')) {
              this._notificationService.error(EXCEPTION_MESSAGES.agentBusy);
            } else if (err.status == 500) {
              this.modalRef.hide();
              this._notificationService.error(EXCEPTION_MESSAGES.dndNetwork);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  commonClickToCall$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.COMMON_CLICK_TO_CALL),
      switchMap((action: CommonClickToCall) => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        this.modalRef = this.modalService.show(IvrCallingComponent, {
          class: 'modal-250 modal-dialog-centered modal-border-green',
        });
        return this.api.commonClickToCall(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.data.message);
              this.modalService.hide();
              if (action.payload?.leadId) {
                return new FetchLeadList();
              } else {
                return new FetchAllData();
              }
            } else {
              this._notificationService.error(resp?.data?.message);
              this.modalService.hide();
            }
            if (action.payload?.leadId) {
              return new FetchLeadListSuccess();
            } else {
              return new FetchAllDataSuccess();
            }
          }),
          catchError((err: any) => {
            if (err.error?.messages?.[0]?.includes('GatewayTimeout')) {
              this._notificationService.error(EXCEPTION_MESSAGES.agentBusy);
            } else if (err.status == 500) {
              this.modalRef.hide();
              this._notificationService.error(EXCEPTION_MESSAGES.dndNetwork);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateIVRAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_IVR_ACCOUNT),
      switchMap((action: UpdateIVRAccount) => {
        return this.api.updateIVRAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Updated Successfully');
              return new CommonIvrIntegrationSuccess(resp.data);
            }
            return new CommonIvrIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  makePrimary$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.IVR_PRIMARY),
      switchMap((action: MakePrimary) => {
        return this.api.makePrimary(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Successfully set as Primary Account'
              );

              this._store.dispatch(new FetchIVRList());
              return new FetchIntegrationListSuccess(resp.items);
            }
            return new FetchIntegrationListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAgents$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_AGENTS_LIST),
      map((action: FetchAgents) => action),
      switchMap((data: any) => {
        return this.api.getAgents().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAgentsSuccess(resp.items);
            }
            return new FetchAgentsSuccess({});
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getVirtualNos$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_VIRTUAL_NOS),
      switchMap((action: FetchVirtualNos) => {
        return this.api.getVirtualNos().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchVirtualNosSuccess(resp.data);
            }
            return new FetchVirtualNosSuccess({});
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getVNAssignment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_VIRTUAL_NO_ASSIGNMENT),
      switchMap((action: FetchVNAssignment) => {
        return this.api.getIVRVNAssignment(action.leadId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchVNAssignmentSuccess(resp.data);
            }
            return new FetchVNAssignmentSuccess({});
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getIvrServiceProviders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_IVR_SERVICE_PROVIDERS),
      switchMap((action: FetchIvrServiceProviders) => {
        return this.api.getIvrServiceProviders().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIvrServiceProvidersSuccess(resp.data);
            }
            return new FetchIvrServiceProvidersSuccess({});
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  getIntegrationCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_INTEGRATION_COUNT),
      map((action: FetchIntegrationCount) => action),
      switchMap((data: any) => {
        return this.api.getTotalCount().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIntegrationCountSuccess(resp.data);
            }
            return new FetchIntegrationCountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIVRIntegrationCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_IVR_INTEGRATION_COUNT),
      map((action: FetchIVRIntegrationCount) => action),
      switchMap((data: any) => {
        return this.api.getIVRAccountCount().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIVRIntegrationCountSuccess(resp.data);
            }
            return new FetchIVRIntegrationCountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getIntegrationById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_INTEGRATION_BY_ID),
      map((action: FetchIntegrationById) => action.id),
      switchMap((data: any) => {
        return this.api.getAccountDetailsById(data).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchIntegrationByIdSuccess(resp.data);
            }
            return new FetchIntegrationByIdSuccess('');
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGoogleIntegrationById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_BY_ID),
      map((action: FetchGoogleIntegrationById) => action.id),
      switchMap((data: any) => {
        return this.api.getGoogleAdsLeads(data).pipe(
          map((resp: any) => {
            if (resp) {
              return new FetchGoogleIntegrationByIdSuccess(resp.data);
            }
            return new FetchGoogleIntegrationByIdSuccess('');
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.DELETE_INTEGRATION),
      map((action: DeleteIntegration) => action),
      switchMap((action: any) => {
        return this.api.delete(action?.payload?.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(`Account deleted successfully.`);
              this._store.dispatch(new FetchIntegrationCount());
              this._store.dispatch(new FetchIVRList());
              const { id, ...payloadWithoutId } = action.payload;
              return new FetchIntegrationList(payloadWithoutId);
            }
            return new FetchIntegrationListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getGoogleAccountDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_GOOGLE_INTEGRATION_LIST),
      map((action: FetchGoogleIntegrationList) => action.payload),
      switchMap((data: any) => {
        return this.api.getGoogleAccountDetails(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGoogleIntegrationListSuccess(resp);
            }
            return new FetchGoogleIntegrationListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteGoogleIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.DELETE_GOOGLE_INTEGRATION),
      map((action: DeleteGoogleIntegration) => action),
      switchMap((action: DeleteGoogleIntegration) => {
        return this.api.deleteGoogleAd(action?.payload?.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Account deleted successfully.`
              );
              const { id, ...payloadWithoutId } = action?.payload;
              return new FetchGoogleIntegrationList(payloadWithoutId);
            }
            return new FetchGoogleIntegrationListSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteFacebookIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.DELETE_FACEBOOK_INTEGRATION),
      map((action: DeleteFacebookIntegration) => action),
      switchMap((action: DeleteFacebookIntegration) => {
        return this.api.deleteFBAccount(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Account deleted successfully.`
              );
              return new FetchFbAccountForms();
            }
            return new FetchFbAccountFormsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getSubscribedForms$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_SUBSCRIBED_FORMS),
      map((action: FetchSubscribedForms) => action.payload),
      switchMap((data: any) => {
        return this.api
          .getAllSubscribedForms(data.pageNumber, data.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchSubscribedFormsSuccess(resp.items);
              }
              return new FetchSubscribedFormsSuccess([]);
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  fetchFacebookAccounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_FACEBOOK_ACCOUNTS),
      map((action: FetchFacebookAccounts) => action.payload),
      switchMap((data: any) => {
        return this.api
          .getAllFacebookAccounts(data.pageNumber, data.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchFacebookAccountsSuccess(resp.items);
              }
              return new FetchFacebookAccountsSuccess([]);
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  addFacebookAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_FACEBOOK_ACCOUNT),
      map((action: AddFacebookAccount) => action.payload),
      switchMap((action: AddFacebookAccount) => {
        return this.api.addFacebookAccount(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp.message);
              return new FetchFbAccountForms();
            }
            return new FetchFbAccountFormsSuccess([]);
          }),
          catchError((err) => {
            if (err.error.messages?.[0]) {
              this._notificationService.error(err.error.messages?.[0]);
            }
            return of(new OnError(err));
          })
        );
      })
    )
  );

  automateIntegrationAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.AUTOMATE_INTEGRATION),
      map((action: AutomateIntegration) => action.payload),
      switchMap((action: AutomateIntegration) => {
        return this.api.automateIntegration(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                resp.message ?? 'Assignment details stored for the account'
              );
              return new AutomateIntegrationSuccess(resp.data);
            }
            return new AutomateIntegrationSuccess({});
          })
        );
      })
    )
  );

  automateFBAdIntegrationAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.AUTOMATE_FB_AD_INTEGRATION),
      map((action: AutomateFBAdIntegration) => action.payload),
      switchMap((action: AutomateFBAdIntegration) => {
        return this.api.automateFBAdIntegration(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                resp.message ?? 'Assignment details stored for the accounts'
              );
              return new AutomateFBAdIntegrationSuccess(resp.data);
            }
            return new AutomateFBAdIntegrationSuccess({});
          })
        );
      })
    )
  );

  automateFBFormsIntegrationAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.AUTOMATE_FB_FORMS_INTEGRATION),
      map((action: AutomateFBFormsIntegration) => action.payload),
      switchMap((action: AutomateFBFormsIntegration) => {
        return this.api.automateFBFormIntegration(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                resp.message ?? 'Assignment details stored for the accounts'
              );
              return new AutomateFBFormsIntegrationSuccess(resp.data);
            }
            return new AutomateFBFormsIntegrationSuccess({});
          })
        );
      })
    )
  );

  toggleFBSubscription$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.TOGGLE_fb_SUBSCRIPTION),
      map((action: ToggleFBSubscription) => action.payload),
      switchMap((action: ToggleFBSubscription) => {
        return this.api.ToggleFBSubscription(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                resp.message ?? 'Subscriptions successfully updated.'
              );
              this._store.dispatch(new FetchFbAccountForms());
              return new FetchFbAccountFormsSuccess([]);
            }
            return new FetchFbAccountFormsSuccess([]);
          })
        );
      })
    )
  );

  syncAdsOfAnFBAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.SYNC_FB_ADS),
      map((action: SyncAdsOfAnFBAccount) => action),
      switchMap((action: SyncAdsOfAnFBAccount) => {
        return this.api.syncAdsOfAnFBAccount(action.accountId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                resp.message ?? 'All the ads synced successfully.'
              );
              this._store.dispatch(new FetchFbAccountForms());
              return new FetchFbAccountFormsSuccess([]);
            }
            return new FetchFbAccountFormsSuccess([]);
          })
        );
      })
    )
  );

  fetchAssignmentDetails = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_INTEGRATION_ASSIGNMENT_DETAILS),
      map((action: FetchIntegrationAssignmentDetails) => action),
      switchMap((action: any) => {
        return this.api
          .getAssignmentDetails(action.resource.id, action.resource.source)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchIntegrationAssignmentDetailsSuccess(
                  resp.data?.assignedUserIds
                );
              }
              return new FetchIntegrationAssignmentDetailsSuccess([]);
            })
          );
      })
    )
  );

  agencyName$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.AGENCY_NAME),
      map((action: AgencyName) => action),
      switchMap((action: AgencyName) => {
        return this.api.agencyName(action?.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Agency Name Updated Successfully'
              );
              let payload = {
                LeadSource: action?.payload?.source
              }
              if (action?.payload?.name !== 'Google ads leads form') {
                this._store.dispatch(new FetchIntegrationList(payload));
              } else {
                this._store.dispatch(new FetchGoogleIntegrationList(resp));
              }
              this._store.dispatch(new FetchAgencyNameList());
              this._store.dispatch(new FetchFbAccountForms());
              return new AgencyNameSuccess(resp.data);
            }
            return new AgencyNameSuccess({});
          })
        );
      })
    )
  );

  updateProjectList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_PROJECT_LIST),
      map((action: ProjectList) => action.payload),
      switchMap((action: ProjectList) => {
        return this.api.updateProjects(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Projects Updated Successfully'
              );
              this._store.dispatch(new FetchIntegrationList(action.payload));
              return new ProjectListSuccess(resp.data);
            }
            return new ProjectListSuccess({});
          })
        );
      })
    )
  );

  getAgencyNameList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_AGENCY_NAME_LIST),
      map((action: FetchAgencyNameList) => action),
      switchMap((data: any) => {
        return this.api.getAgencyNameList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAgencyNameListSuccess(resp.data);
            }
            return new FetchAgencyNameListSuccess({});
          }),
          catchError((err: any) => {
            throwError(err);
            return throwError(() => err);
          })
        );
      })
    )
  );

  fetchProjects = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_INTEGRATION_PROJECTS),
      map((action: FetchIntegrationProjects) => action),
      switchMap((action: any) => {
        return this.api
          .getProjects(action.resource.id, action.resource.source)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchIntegrationProjectsSuccess(resp.items);
              }
              return new FetchIntegrationProjectsSuccess([]);
            })
          );
      })
    )
  );

  fetchFbAccountForms$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_FB_ACCOUNT_FORMS),
      map((action: FetchFbAccountForms) => action),
      switchMap((data: any) => {
        return this.api.getAllFbAccounts().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchFbAccountFormsSuccess(resp.items);
            }
            return new FetchFbAccountFormsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addJustLeadIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_JUST_LEAD),
      map((action: AddJustLead) => action.payload),
      switchMap((data: any) => {
        return this.api.addJustAccount(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              const { accountName, ...filteredData } = data;
              this._store.dispatch(
                new IntegrationEmail({ ...filteredData, accountId: resp.data }),
              );
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateJustLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_JUST_LEAD),
      switchMap((action: UpdateJustLead) => {
        return this.api.updateJustAccount({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Integration Updated Successfully'
              );
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  addCFIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_COMMON_FLOOR),
      map((action: AddCommonFloor) => action.payload),
      switchMap((data: any) => {
        return this.api.addCFAccount(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              const { accountName, ...filteredData } = data;
              this._store.dispatch(
                new IntegrationEmail({ ...filteredData, accountId: resp.data }),
              );
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateCF$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_COMMON_FLOOR),
      switchMap((action: UpdateCommonFloor) => {
        return this.api.updateCFAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Integration Updated Successfully'
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  fetchFbBulkLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_FB_BULK_LEADS),
      map((action: FetchBulkLeadsFromFb) => action.payload),
      switchMap((action: FetchBulkLeadsFromFb) => {
        return this.api.fetchFbBulkLeadsFetch(action).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(resp?.message);
              this._store.dispatch(new FetchIntegrationList());
              this._store.dispatch(new FetchIntegrationCount());
              return new FetchBulkLeadsFromFbSuccess(resp);
            }
            return new FetchBulkLeadsFromFbSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  addMicrositeLead$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(IntegrationActionTypes.ADD_MS_LEAD),
        map((action: AddMicrositeLead) => action),
        switchMap((action: AddMicrositeLead) => {
          return this.api.addMicrositeLead(action.payload).pipe(
            map((resp: any) => {
              if (resp.data) {
                this._notificationService.success(
                  'Enquiry Submitted Successfully'
                );
                this._store.dispatch(
                  new AddMicrositeLeadSuccess(resp.succeeded)
                );
              } else {
                this._notificationService.warn(resp?.message);
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  addProjectMicrositeLead$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(IntegrationActionTypes.ADD_PROJECT_MS_LEAD),
        map((action: AddProjectMicrositeLead) => action),
        switchMap((action: AddProjectMicrositeLead) => {
          return this.api.addProjectMicrositeLead(action.payload).pipe(
            map((resp: any) => {
              if (resp.data) {
                this._notificationService.success(
                  'Enquiry Submitted Successfully'
                );
                this._store.dispatch(
                  new AddProjectMicrositeLeadSuccess(resp.succeeded)
                );
              } else {
                this._notificationService.warn(resp?.message);
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  webhookAccount$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(IntegrationActionTypes.ADD_WEBHOOK),
        map((action: AddWebhook) => action),
        switchMap((action: AddWebhook) => {
          return this.api.webhookaccounts(action.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  'Enquiry Submitted Successfully'
                );
                this._store.dispatch(
                  new AddWebhookSuccess(resp.succeeded)
                );
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  getWebhook$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_WEBHOOK),
      switchMap((action: FetchWebhook) => {
        return this.api.addWebhook().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchWebhookSuccess(resp.data);
            }
            return null;
          }),
          catchError((err) => {
            return of(new OnError(err));
          })
        );
      })
    )
  );


  getWebhookAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_WEBHOOK_ACCOUNT),
      map((action: FetchWebhookAccount) => action),
      switchMap((action) => {
        return this.api.getWebhookAccount(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchWebhookAccountSuccess(resp);
            }
            return new FetchWebhookAccountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExportFacebookStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_EXPORT_FACEBOOK_STATUS),
      map((action: FetchExportFacebookStatus) => action),
      switchMap((data: any) => {
        return this.api
          .getExportFacebookStatus(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchExportFacebookStatusSuccess(resp);
              }
              return new FetchExportFacebookStatusSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getIVRList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_IVR_LIST),
      switchMap((action: FetchIVRList) => {
        return this.api.fetchIVRList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchIVRListSuccess(resp.data);
            }
            return null;
          }),
          catchError((err) => {
            return of(new OnError(err));
          })
        );
      })
    )
  );

  addPropertyFinderIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_PROPERTY_FINDER),
      map((action: AddPropertyFinder) => action.payload),
      switchMap((data: any) => {
        return this.api.addPropertyFinderAccount(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updatePropertyFinder$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_PROPERTY_FINDER),
      switchMap((action: UpdatePropertyFinder) => {
        return this.api.updatePropertyFinderAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Integration Updated Successfully'
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  addBayurIntegration$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_BAYUT),
      map((action: AddBayut) => action.payload),
      switchMap((data: any) => {
        return this.api.addBayutAccount(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateBayut$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_BAYUT),
      switchMap((action: UpdateBayut) => {
        return this.api.updateBayutAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Integration Updated Successfully'
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  addDubizzle$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.ADD_DUBIZZLE),
      map((action: AddDubizzle) => action.payload),
      switchMap((data: any) => {
        return this.api.addDubizzleAccount(data).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Integration added successfully.`
              );
              this._store.dispatch(new FetchIntegrationList({ LeadSource: data?.source, PageSize: data?.PageSize, PageNumber: data?.PageNumber }));
              this._store.dispatch(new FetchIntegrationCount());
              return new AddIntegrationSuccess(resp.data);
            }
            return new AddIntegrationSuccess({});
          }),
          catchError((err: any) => {
            if (err.status == 500) {
              this._notificationService.error(err?.error?.messages?.[0]);
            }
            return throwError(() => err);
          })
        );
      })
    )
  );

  updateDubizzle$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_DUBIZZLE),
      switchMap((action: UpdateDubizzle) => {
        return this.api.updateDubizzleAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Account Updated Successfully');
              this._store.dispatch(new FetchIntegrationList({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }));
            }
            return resp;
          })
        );
      })
    )
  );

  updateWebhookList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_WEBHOOK),
      switchMap((action: UpdateWebhook) => {
        return this.api.updateWebhookAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Account Updated Successfully');
              this._store.dispatch(new FetchIntegrationList({ LeadSource: action?.payload?.source, PageSize: action?.payload?.PageSize, PageNumber: action?.payload?.PageNumber }));
            }
            return resp;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    ),
    { dispatch: false }
  );



  doesAccountNameExists$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.DOES_ACCOUNTNAME_EXISTS),
      map((action: DoesExistAccountName) => action),
      switchMap((data: any) => {
        return this.api.doesAccountNameExists(data?.accountName, data?.source).pipe(
          map((resp: any) => {
            return new DoesExistAccountNameSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updatePixel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.UPDATE_PIXEL_ACCOUNT),
      switchMap((action: UpdatePixelAccount) => {
        return this.api.updatePixelAccount(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Pixel Updated Successfully');
              this._store.dispatch(new FetchFbAccountForms());
            }
            return resp;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getFacebookMarketing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_FACEBOOK_MARKETING),
      switchMap((action: FetchFacebookMarketing) => {
        return this.api.getFacebookMarketing(action.payload).pipe(
          map((resp: any) => {
            return new FetchFacebookMarketingSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchFbAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_FB_ACCOUNT),
      map((action: FetchFbAccount) => action),
      switchMap((data: any) => {
        return this.api.getFbAccountForms().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchFbAccountSuccess(resp.items);
            }
            return new FetchFbAccountSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  // Google Campaign Effects
  googleCampaignLogin$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.GOOGLE_CAMPAIGN_LOGIN),
      map((action: GoogleCampaignLogin) => action.payload),
      switchMap((payload: any) => {
        return this.api.googleCampaignLogin(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Google Campaign login successful');
              return new GoogleCampaignLoginSuccess(resp.data);
            }
            return new GoogleCampaignLoginSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchGoogleCampaignAccounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_ACCOUNTS),
      map((action: FetchGoogleCampaignAccounts) => action.payload),
      switchMap((payload: any) => {
        return this.api.getAllGoogleCampaignAccounts(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGoogleCampaignAccountsSuccess(resp.items || []);
            }
            return new FetchGoogleCampaignAccountsSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  fetchGoogleCampaignMarketing$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.FETCH_GOOGLE_CAMPAIGN_MARKETING),
      map((action: FetchGoogleCampaignMarketing) => action.payload),
      switchMap((payload: any) => {
        return this.api.getGoogleCampaignMarketing(payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchGoogleCampaignMarketingSuccess(resp.data || []);
            }
            return new FetchGoogleCampaignMarketingSuccess([]);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteGoogleCampaignAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(IntegrationActionTypes.DELETE_GOOGLE_CAMPAIGN_ACCOUNT),
      map((action: any) => action.accountId),
      switchMap((accountId: string) => {
        return this.api.deleteGoogleCampaignAccount(accountId).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Account deleted successfully');
              const refreshPayload = {
                LeadSource: 44,
                PageNumber: 1,
                PageSize: 10
              };
              this._store.dispatch(new FetchGoogleCampaignAccounts(refreshPayload));
              return new DeleteGoogleCampaignAccountSuccess(resp);
            }
            return new DeleteGoogleCampaignAccountSuccess({});
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  constructor(
    private actions$: Actions,
    private api: IntegrationService,
    private _notificationService: NotificationsService,
    private modalService: BsModalService,
    public modalRef: BsModalRef,
    private _store: Store<AppState>,
    private commonService: CommonService
  ) { }
}
